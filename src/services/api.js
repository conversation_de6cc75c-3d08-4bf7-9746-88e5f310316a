import { logApiRequest, logApiResponse, logApiError } from '../utils/debug';
import { API_CONFIG, DEBUG_CONFIG } from '../config';

// 从本地存储获取API Key
const getApiKey = () => localStorage.getItem('cursor_api_key');

// 创建Basic Auth凭证
const createBasicAuth = (apiKey) => {
  // 根据文档，用户名是API Key，密码为空
  return 'Basic ' + btoa(`${apiKey}:`);
};

// API请求通用方法
const fetchWithAuth = async (endpoint, method = 'GET', body = null) => {
  const apiKey = getApiKey();
  
  if (!apiKey) throw new Error('API Key未设置');
  
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': createBasicAuth(apiKey)
  };
  
  const options = {
    method,
    headers,
    // 添加跨域支持
    mode: 'cors',
    credentials: 'same-origin'
  };
  
  // 如果是POST/PUT请求并且有body，添加请求体
  if ((method === 'POST' || method === 'PUT') && body) {
    options.body = JSON.stringify(body);
  }
  
  const url = `${API_CONFIG.BASE_URL}${endpoint}`;
  
  if (DEBUG_CONFIG.LOG_API_CALLS) {
    logApiRequest(url, options);
  }
  
  try {
    const response = await fetch(url, options);
    
    // 尝试解析响应内容
    let responseData;
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      responseData = await response.json();
    } else {
      responseData = await response.text();
    }
    
    if (DEBUG_CONFIG.LOG_API_CALLS) {
      logApiResponse(url, response, responseData);
    }
    
    if (!response.ok) {
      const errorMessage = typeof responseData === 'object' && responseData.message 
        ? responseData.message 
        : `API请求失败: ${response.status}`;
      throw new Error(errorMessage);
    }
    
    return responseData;
  } catch (error) {
    if (DEBUG_CONFIG.LOG_API_CALLS) {
      logApiError(url, error);
    }
    throw error;
  }
};

// 测试API Key是否有效
export const testApiKey = async (apiKey) => {
  // 临时保存API Key用于测试
  localStorage.setItem('cursor_api_key', apiKey);
  
  // 尝试所有可能的基础URL
  const baseUrls = [API_CONFIG.BASE_URL, ...API_CONFIG.ALTERNATE_BASE_URLS];
  
  for (const baseUrl of baseUrls) {
    try {
      // 临时覆盖基础URL
      const originalBaseUrl = API_CONFIG.BASE_URL;
      API_CONFIG.BASE_URL = baseUrl;
      
      console.log(`尝试基础URL: ${baseUrl}`);
      
      // 尝试获取团队成员列表来验证API Key
      const result = await getTeamMembers();
      
      // 如果成功，保持这个基础URL
      console.log(`成功连接到: ${baseUrl}`);
      return { valid: true, data: result };
    } catch (error) {
      console.error(`使用 ${baseUrl} 验证失败:`, error);
      // 继续尝试下一个URL
    }
  }
  
  // 所有URL都失败了
  localStorage.removeItem('cursor_api_key');
  return { 
    valid: false, 
    error: '无法连接到Cursor API。请检查API密钥和网络连接。' 
  };
};

// 获取团队成员列表
export const getTeamMembers = () => {
  return fetchWithAuth(API_CONFIG.ENDPOINTS.TEAM_MEMBERS);
};

// 获取团队使用统计
export const getTeamUsage = (startDate, endDate) => {
  // 将日期转换为毫秒时间戳
  const start = new Date(startDate).getTime();
  const end = new Date(endDate).getTime();
  
  return fetchWithAuth(
    API_CONFIG.ENDPOINTS.TEAM_USAGE, 
    'POST', 
    { startDate: start, endDate: end }
  );
};

// 获取团队支出数据
export const getTeamSpend = (searchTerm = '', page = 1, pageSize = 10) => {
  return fetchWithAuth(
    API_CONFIG.ENDPOINTS.TEAM_SPEND,
    'POST',
    { searchTerm, page, pageSize }
  );
};

// 获取特定成员使用情况 (通过筛选团队使用数据)
export const getMemberUsage = async (userId, startDate, endDate) => {
  // 获取团队使用数据
  const teamData = await getTeamUsage(startDate, endDate);
  
  // 获取成员列表
  const membersData = await getTeamMembers();
  
  // 找到指定成员
  const member = membersData.find(m => m.email === userId || m.id === userId);
  
  if (!member) {
    throw new Error('找不到指定成员');
  }
  
  // 筛选该成员的使用数据
  // 注意：这里的实现取决于API返回的数据结构
  // 可能需要根据实际返回的数据结构进行调整
  const memberData = {
    ...member,
    // 假设API返回的数据中包含成员使用情况
    usage: teamData.memberUsage?.find(u => u.email === member.email || u.id === member.id) || {}
  };
  
  return memberData;
};

// 获取所有成员使用情况
export const getMembersUsage = async (startDate, endDate) => {
  // 获取团队使用数据
  const teamData = await getTeamUsage(startDate, endDate);
  
  // 获取成员列表
  const membersData = await getTeamMembers();
  
  // 合并成员信息和使用数据
  // 注意：这里的实现取决于API返回的数据结构
  // 可能需要根据实际返回的数据结构进行调整
  const membersWithUsage = membersData.map(member => {
    return {
      ...member,
      // 假设API返回的数据中包含成员使用情况
      usage: teamData.memberUsage?.find(u => u.email === member.email || u.id === member.id) || {}
    };
  });
  
  return { members: membersWithUsage };
};
