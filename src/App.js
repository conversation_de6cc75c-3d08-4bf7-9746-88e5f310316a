import React, { useState, useEffect } from 'react';
import Login from './components/Auth/Login';
import TeamOverview from './components/Dashboard/TeamOverview';
import MembersList from './components/Dashboard/MembersList';
import MemberDetail from './components/Dashboard/MemberDetail';
import Header from './components/UI/Header';
import ApiTest from './components/ApiTest';
import './styles.css';

function App() {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [selectedMemberId, setSelectedMemberId] = useState(null);
  const [testMode, setTestMode] = useState(false);
  
  useEffect(() => {
    // 检查是否已有API Key
    const apiKey = localStorage.getItem('cursor_api_key');
    if (apiKey) {
      setIsLoggedIn(true);
    }
  }, []);
  
  const handleLogout = () => {
    localStorage.removeItem('cursor_api_key');
    setIsLoggedIn(false);
    setSelectedMemberId(null);
  };
  
  const handleSelectMember = (userId) => {
    setSelectedMemberId(userId);
  };
  
  const handleBackToList = () => {
    setSelectedMemberId(null);
  };

  // 检查URL参数是否包含test模式
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('test') === 'true') {
      setTestMode(true);
    }
  }, []);

  if (testMode) {
    return <ApiTest />;
  }

  if (!isLoggedIn) {
    return <Login onLoginSuccess={() => setIsLoggedIn(true)} />;
  }

  return (
    <div className="app">
      <Header onLogout={handleLogout} />

      <main className="content">
        {selectedMemberId ? (
          <MemberDetail
            memberId={selectedMemberId}
            onBack={handleBackToList}
          />
        ) : (
          <>
            <TeamOverview />
            <MembersList onSelectMember={handleSelectMember} />
          </>
        )}
      </main>
    </div>
  );
}

export default App;