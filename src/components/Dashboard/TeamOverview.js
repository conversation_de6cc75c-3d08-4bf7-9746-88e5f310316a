import React, { useState, useEffect } from 'react';
import { getTeamUsage } from '../../services/api';
import TrendLineChart from '../Charts/TrendLineChart';
import DateRangePicker from '../UI/DateRangePicker';

function TeamOverview() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [usageData, setUsageData] = useState(null);
  const [dateRange, setDateRange] = useState({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });

  useEffect(() => {
    fetchUsageData();
  }, [dateRange]);

  const fetchUsageData = async () => {
    setLoading(true);
    try {
      console.log('正在获取团队使用数据...');
      const data = await getTeamUsage(dateRange.startDate, dateRange.endDate);
      console.log('团队使用数据响应:', data);
      setUsageData(data);
      setError('');
    } catch (err) {
      console.error('获取团队使用数据失败:', err);
      // 设置一个空的数据结构，这样组件仍然可以渲染
      setUsageData({ data: [] });
      setError(`使用数据暂时无法获取: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 计算总提示次数
  const calculateTotalPrompts = (data) => {
    if (!data || !data.data) return 0;
    return data.data.reduce((total, dayData) => {
      return total + (dayData.composerRequests || 0) + (dayData.chatRequests || 0) + (dayData.agentRequests || 0);
    }, 0);
  };

  // 计算总代码行数
  const calculateTotalLines = (data) => {
    if (!data || !data.data) return 0;
    return data.data.reduce((total, dayData) => {
      return total + (dayData.totalLinesAdded || 0) + (dayData.totalLinesDeleted || 0);
    }, 0);
  };

  // 计算活跃天数
  const calculateActiveDays = (data) => {
    if (!data || !data.data) return 0;
    return data.data.filter(dayData => dayData.isActive).length;
  };

  // 格式化图表数据
  const formatChartData = (data) => {
    if (!data || !data.data) return [];

    return data.data.map(dayData => ({
      date: new Date(dayData.date).toLocaleDateString('zh-CN'),
      prompts: (dayData.composerRequests || 0) + (dayData.chatRequests || 0) + (dayData.agentRequests || 0),
      tokens: (dayData.totalLinesAdded || 0) + (dayData.totalLinesDeleted || 0) // 用代码行数代替token
    }));
  };

  if (loading) return <div>加载中...</div>;
  if (!usageData) return null;

  return (
    <div className="team-overview">
      <h2>团队使用概览</h2>

      {error && (
        <div className="error-message" style={{
          backgroundColor: '#fff3cd',
          border: '1px solid #ffeaa7',
          color: '#856404',
          padding: '10px',
          borderRadius: '4px',
          marginBottom: '20px'
        }}>
          ⚠️ {error}
        </div>
      )}

      <DateRangePicker
        startDate={dateRange.startDate}
        endDate={dateRange.endDate}
        onChange={setDateRange}
      />
      
      <div className="stats-cards">
        <div className="stat-card">
          <h3>总提示次数</h3>
          <p className="stat-value">{calculateTotalPrompts(usageData)}</p>
        </div>
        <div className="stat-card">
          <h3>总代码行数</h3>
          <p className="stat-value">{calculateTotalLines(usageData)}</p>
        </div>
        <div className="stat-card">
          <h3>活跃天数</h3>
          <p className="stat-value">{calculateActiveDays(usageData)}</p>
        </div>
      </div>
      
      <div className="chart-container">
        <h3>使用趋势</h3>
        <TrendLineChart data={formatChartData(usageData)} />
      </div>
    </div>
  );
}

export default TeamOverview;