import React, { useState, useEffect } from 'react';
import { getTeamMembers, getMembersUsage } from '../../services/api';
import UsageBarChart from '../Charts/UsageBarChart';

function MembersList({ onSelectMember }) {
  const [members, setMembers] = useState([]);
  const [usageData, setUsageData] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    try {
      console.log('正在获取成员数据...');

      // 获取成员列表
      const membersData = await getTeamMembers();
      console.log('成员列表响应:', membersData);

      // 设置成员数据 - 直接使用数组，不需要.members属性
      const members = Array.isArray(membersData) ? membersData : (membersData.members || []);
      setMembers(members);

      console.log('成员数量:', members.length);

      // 获取使用数据 - 使用最近30天的数据
      const endDate = new Date().toISOString().split('T')[0];
      const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

      try {
        const usageResponse = await getMembersUsage(startDate, endDate);
        console.log('成员使用数据响应:', usageResponse);

        // 将使用数据转换为以email为键的对象
        const usageByMember = {};
        (usageResponse.members || []).forEach(member => {
          const memberId = member.email || member.id || member.userId;
          usageByMember[memberId] = member.usage || {
            totalPrompts: 0,
            totalTokens: 0,
            totalLinesAdded: 0,
            totalLinesDeleted: 0
          };
        });

        setUsageData(usageByMember);
      } catch (usageErr) {
        console.warn('获取使用数据失败，使用空数据:', usageErr);
        // 如果获取使用数据失败，创建空的使用数据对象
        const usageByMember = {};
        members.forEach(member => {
          usageByMember[member.email || member.id || member.userId] = {
            totalPrompts: 0,
            totalTokens: 0,
            totalLinesAdded: 0,
            totalLinesDeleted: 0
          };
        });
        setUsageData(usageByMember);
      }
    } catch (err) {
      console.error('获取成员数据失败:', err);
      setError(`获取成员数据失败: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  if (loading) return <div>加载中...</div>;
  if (error) return <div className="error-message">{error}</div>;

  return (
    <div className="members-list">
      <h2>团队成员使用情况</h2>
      
      <UsageBarChart members={members} usageData={usageData} />
      
      <div className="members-table">
        <table>
          <thead>
            <tr>
              <th>成员</th>
              <th>提示次数</th>
              <th>代码行数</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            {members.map(member => {
              const memberId = member.email || member.id || member.userId;
              const usage = usageData[memberId] || {};
              return (
                <tr key={memberId}>
                  <td>{member.name || member.email}</td>
                  <td>{usage.totalPrompts || 0}</td>
                  <td>{(usage.totalLinesAdded || 0) + (usage.totalLinesDeleted || 0)}</td>
                  <td>
                    <button onClick={() => onSelectMember(memberId)}>
                      查看详情
                    </button>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
}

export default MembersList;