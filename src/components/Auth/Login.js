import React, { useState } from 'react';
import { testApiKey } from '../../services/api';

function Login({ onLoginSuccess }) {
  const [apiKey, setApiKey] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    
    try {
      // 测试API Key是否有效
      const result = await testApiKey(apiKey);
      
      if (result.valid) {
        // 登录成功
        onLoginSuccess();
      } else {
        setError(result.error || 'API Key无效，请检查后重试');
        localStorage.removeItem('cursor_api_key');
      }
    } catch (err) {
      setError('连接失败，请检查网络连接');
      localStorage.removeItem('cursor_api_key');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-container">
      <h2>Cursor团队使用情况</h2>
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label>API Key</label>
          <input
            type="password"
            value={apiKey}
            onChange={(e) => setApiKey(e.target.value)}
            placeholder="输入您的Cursor Admin API Key"
            required
          />
        </div>
        {error && <div className="error-message">{error}</div>}
        <button type="submit" disabled={loading}>
          {loading ? '验证中...' : '登录'}
        </button>
      </form>
    </div>
  );
}

export default Login;
