import React, { useState } from 'react';
import { testApiKey } from '../../services/api';

function Login({ onLoginSuccess }) {
  const [apiKey, setApiKey] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [testResult, setTestResult] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      console.log('开始验证API Key...');
      // 测试API Key是否有效
      const result = await testApiKey(apiKey);

      if (result.valid) {
        console.log('API Key验证成功，登录成功');
        // 登录成功
        onLoginSuccess();
      } else {
        console.log('API Key验证失败:', result.error);
        setError(result.error || 'API Key无效，请检查后重试');
        localStorage.removeItem('cursor_api_key');
      }
    } catch (err) {
      console.error('登录过程中发生错误:', err);
      setError(`连接失败: ${err.message}`);
      localStorage.removeItem('cursor_api_key');
    } finally {
      setLoading(false);
    }
  };

  const handleTestConnection = async () => {
    if (!apiKey.trim()) {
      setTestResult('请先输入API Key');
      return;
    }

    setTestResult('测试连接中...');
    try {
      const response = await fetch('/api/teams/members', {
        method: 'GET',
        headers: {
          'Authorization': `Basic ${btoa(apiKey + ':')}`
        }
      });

      if (response.ok) {
        setTestResult('✅ 连接成功！API Key有效');
      } else {
        const errorText = await response.text();
        setTestResult(`❌ 连接失败: ${response.status} - ${errorText}`);
      }
    } catch (error) {
      setTestResult(`❌ 网络错误: ${error.message}`);
    }
  };

  return (
    <div className="login-container">
      <h2>Cursor团队使用情况</h2>
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label>API Key</label>
          <input
            type="password"
            value={apiKey}
            onChange={(e) => setApiKey(e.target.value)}
            placeholder="输入您的Cursor Admin API Key"
            required
          />
        </div>
        {error && <div className="error-message">{error}</div>}
        {testResult && <div className={testResult.includes('✅') ? 'success-message' : 'info-message'}>{testResult}</div>}
        <div className="button-group">
          <button type="button" onClick={handleTestConnection} disabled={loading}>
            测试连接
          </button>
          <button type="submit" disabled={loading}>
            {loading ? '验证中...' : '登录'}
          </button>
        </div>
      </form>
    </div>
  );
}

export default Login;
