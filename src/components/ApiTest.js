import React, { useState } from 'react';

function ApiTest() {
  const [apiKey, setApiKey] = useState('');
  const [result, setResult] = useState('');
  const [loading, setLoading] = useState(false);

  const testDirectConnection = async () => {
    if (!apiKey.trim()) {
      setResult('请先输入API Key');
      return;
    }

    setLoading(true);
    setResult('测试直接连接中...');

    try {
      const response = await fetch('https://api.cursor.com/teams/members', {
        method: 'GET',
        headers: {
          'Authorization': `Basic ${btoa(apiKey + ':')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setResult(`✅ 直接连接成功！获取到 ${data.length} 个团队成员`);
      } else {
        const errorText = await response.text();
        setResult(`❌ 直接连接失败: ${response.status} - ${errorText}`);
      }
    } catch (error) {
      setResult(`❌ 直接连接错误: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testProxyConnection = async () => {
    if (!apiKey.trim()) {
      setResult('请先输入API Key');
      return;
    }

    setLoading(true);
    setResult('测试代理连接中...');

    try {
      const response = await fetch('/api/teams/members', {
        method: 'GET',
        headers: {
          'Authorization': `Basic ${btoa(apiKey + ':')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setResult(`✅ 代理连接成功！获取到 ${data.length} 个团队成员`);
      } else {
        const errorText = await response.text();
        setResult(`❌ 代理连接失败: ${response.status} - ${errorText}`);
      }
    } catch (error) {
      setResult(`❌ 代理连接错误: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testHealthCheck = async () => {
    setLoading(true);
    setResult('测试健康检查中...');

    try {
      const response = await fetch('/health');
      if (response.ok) {
        const data = await response.json();
        setResult(`✅ 健康检查成功: ${JSON.stringify(data)}`);
      } else {
        setResult(`❌ 健康检查失败: ${response.status}`);
      }
    } catch (error) {
      setResult(`❌ 健康检查错误: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: '20px', maxWidth: '600px', margin: '0 auto' }}>
      <h2>API 连接测试</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <label>API Key:</label>
        <input
          type="password"
          value={apiKey}
          onChange={(e) => setApiKey(e.target.value)}
          placeholder="输入您的Cursor Admin API Key"
          style={{ width: '100%', padding: '10px', margin: '10px 0' }}
        />
      </div>

      <div style={{ display: 'flex', gap: '10px', marginBottom: '20px' }}>
        <button onClick={testHealthCheck} disabled={loading}>
          测试健康检查
        </button>
        <button onClick={testDirectConnection} disabled={loading}>
          测试直接连接
        </button>
        <button onClick={testProxyConnection} disabled={loading}>
          测试代理连接
        </button>
      </div>

      {result && (
        <div style={{
          padding: '15px',
          borderRadius: '5px',
          backgroundColor: result.includes('✅') ? '#e8f5e8' : '#ffebee',
          color: result.includes('✅') ? '#2e7d32' : '#d32f2f',
          border: `1px solid ${result.includes('✅') ? '#c8e6c9' : '#ffcdd2'}`,
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-word'
        }}>
          {result}
        </div>
      )}
    </div>
  );
}

export default ApiTest;
