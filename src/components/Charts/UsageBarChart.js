import React, { useEffect, useRef } from 'react';
import Chart from 'chart.js/auto';

function UsageBarChart({ members, usageData }) {
  const chartRef = useRef(null);
  const chartInstance = useRef(null);

  useEffect(() => {
    if (!members || members.length === 0 || !usageData) return;

    // 销毁之前的图表实例
    if (chartInstance.current) {
      chartInstance.current.destroy();
    }

    const ctx = chartRef.current.getContext('2d');
    
    // 准备数据
    const labels = members.map(member => member.name || member.email || member.id);
    const promptsData = members.map(member => {
      const memberId = member.email || member.id || member.userId;
      return usageData[memberId]?.totalPrompts || 0;
    });
    const tokensData = members.map(member => {
      const memberId = member.email || member.id || member.userId;
      const usage = usageData[memberId] || {};
      return (usage.totalLinesAdded || 0) + (usage.totalLinesDeleted || 0);
    });

    // 创建新图表
    chartInstance.current = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: labels,
        datasets: [
          {
            label: '提示次数',
            data: promptsData,
            backgroundColor: 'rgba(75, 192, 192, 0.6)',
            borderColor: 'rgb(75, 192, 192)',
            borderWidth: 1,
            yAxisID: 'y'
          },
          {
            label: '代码行数',
            data: tokensData,
            backgroundColor: 'rgba(255, 99, 132, 0.6)',
            borderColor: 'rgb(255, 99, 132)',
            borderWidth: 1,
            yAxisID: 'y1'
          }
        ]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            type: 'linear',
            display: true,
            position: 'left',
            title: {
              display: true,
              text: '提示次数'
            },
            beginAtZero: true
          },
          y1: {
            type: 'linear',
            display: true,
            position: 'right',
            title: {
              display: true,
              text: '代码行数'
            },
            beginAtZero: true,
            grid: {
              drawOnChartArea: false,
            },
          }
        }
      }
    });

    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, [members, usageData]);

  return (
    <div className="chart-wrapper">
      <canvas ref={chartRef}></canvas>
    </div>
  );
}

export default UsageBarChart;