// API配置
export const API_CONFIG = {
  // 基础URL - 根据官方文档
  BASE_URL: process.env.NODE_ENV === 'development' ? '/api' : 'https://api.cursor.com',

  // 备选基础URL - 如果上面的不工作，可以尝试这些
  ALTERNATE_BASE_URLS: [
    'https://api.cursor.com'
  ],

  // 是否使用代理
  USE_PROXY: process.env.NODE_ENV === 'development',

  // API路径
  ENDPOINTS: {
    TEAM_MEMBERS: '/teams/members',
    TEAM_DAILY_USAGE: '/teams/daily-usage-data',
    TEAM_SPEND: '/teams/spend'
  },

  // 认证方式
  AUTH_METHODS: {
    BASIC: 'Basic',
    BEARER: 'Bearer'
  }
};

// 调试配置
export const DEBUG_CONFIG = {
  // 是否启用详细日志
  VERBOSE_LOGGING: true,
  
  // 是否在控制台显示API请求和响应
  LOG_API_CALLS: true
};
