{"name": "cursor-team-dashboard", "version": "0.1.0", "private": true, "dependencies": {"chart.js": "^3.7.1", "cors": "^2.8.5", "express": "^4.17.1", "http-proxy-middleware": "^2.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "server": "node server.js", "dev": "concurrently \"npm run start\" \"npm run server\""}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:3001", "devDependencies": {"concurrently": "^7.6.0"}}