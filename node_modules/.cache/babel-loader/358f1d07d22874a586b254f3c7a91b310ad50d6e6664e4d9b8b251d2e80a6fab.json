{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport Login from './components/Auth/Login';\nimport TeamOverview from './components/Dashboard/TeamOverview';\nimport MembersList from './components/Dashboard/MembersList';\nimport MemberDetail from './components/Dashboard/MemberDetail';\nimport Header from './components/UI/Header';\nimport ApiTest from './components/ApiTest';\nimport './styles.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\n  const [selectedMemberId, setSelectedMemberId] = useState(null);\n  const [testMode, setTestMode] = useState(false);\n  useEffect(() => {\n    // 检查是否已有API Key\n    const apiKey = localStorage.getItem('cursor_api_key');\n    if (apiKey) {\n      setIsLoggedIn(true);\n    }\n  }, []);\n  const handleLogout = () => {\n    localStorage.removeItem('cursor_api_key');\n    setIsLoggedIn(false);\n    setSelectedMemberId(null);\n  };\n  const handleSelectMember = userId => {\n    setSelectedMemberId(userId);\n  };\n  const handleBackToList = () => {\n    setSelectedMemberId(null);\n  };\n\n  // 检查URL参数是否包含test模式\n  useEffect(() => {\n    const urlParams = new URLSearchParams(window.location.search);\n    if (urlParams.get('test') === 'true') {\n      setTestMode(true);\n    }\n  }, []);\n  if (testMode) {\n    return /*#__PURE__*/_jsxDEV(ApiTest, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 12\n    }, this);\n  }\n  if (!isLoggedIn) {\n    return /*#__PURE__*/_jsxDEV(Login, {\n      onLoginSuccess: () => setIsLoggedIn(true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      onLogout: handleLogout\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"content\",\n      children: selectedMemberId ? /*#__PURE__*/_jsxDEV(MemberDetail, {\n        memberId: selectedMemberId,\n        onBack: handleBackToList\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(TeamOverview, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(MembersList, {\n          onSelectMember: handleSelectMember\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"k5pIf8JfERUXTjl0QxZzMkoxxW0=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON>", "TeamOverview", "MembersList", "MemberDetail", "Header", "ApiTest", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "App", "_s", "isLoggedIn", "setIsLoggedIn", "selectedMemberId", "setSelectedMemberId", "testMode", "setTestMode", "<PERSON><PERSON><PERSON><PERSON>", "localStorage", "getItem", "handleLogout", "removeItem", "handleSelectMember", "userId", "handleBackToList", "urlParams", "URLSearchParams", "window", "location", "search", "get", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onLoginSuccess", "className", "children", "onLogout", "memberId", "onBack", "onSelectMember", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport Login from './components/Auth/Login';\nimport TeamOverview from './components/Dashboard/TeamOverview';\nimport MembersList from './components/Dashboard/MembersList';\nimport MemberDetail from './components/Dashboard/MemberDetail';\nimport Header from './components/UI/Header';\nimport ApiTest from './components/ApiTest';\nimport './styles.css';\n\nfunction App() {\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\n  const [selectedMemberId, setSelectedMemberId] = useState(null);\n  const [testMode, setTestMode] = useState(false);\n  \n  useEffect(() => {\n    // 检查是否已有API Key\n    const apiKey = localStorage.getItem('cursor_api_key');\n    if (apiKey) {\n      setIsLoggedIn(true);\n    }\n  }, []);\n  \n  const handleLogout = () => {\n    localStorage.removeItem('cursor_api_key');\n    setIsLoggedIn(false);\n    setSelectedMemberId(null);\n  };\n  \n  const handleSelectMember = (userId) => {\n    setSelectedMemberId(userId);\n  };\n  \n  const handleBackToList = () => {\n    setSelectedMemberId(null);\n  };\n\n  // 检查URL参数是否包含test模式\n  useEffect(() => {\n    const urlParams = new URLSearchParams(window.location.search);\n    if (urlParams.get('test') === 'true') {\n      setTestMode(true);\n    }\n  }, []);\n\n  if (testMode) {\n    return <ApiTest />;\n  }\n\n  if (!isLoggedIn) {\n    return <Login onLoginSuccess={() => setIsLoggedIn(true)} />;\n  }\n\n  return (\n    <div className=\"app\">\n      <Header onLogout={handleLogout} />\n\n      <main className=\"content\">\n        {selectedMemberId ? (\n          <MemberDetail\n            memberId={selectedMemberId}\n            onBack={handleBackToList}\n          />\n        ) : (\n          <>\n            <TeamOverview />\n            <MembersList onSelectMember={handleSelectMember} />\n          </>\n        )}\n      </main>\n    </div>\n  );\n}\n\nexport default App;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,yBAAyB;AAC3C,OAAOC,YAAY,MAAM,qCAAqC;AAC9D,OAAOC,WAAW,MAAM,oCAAoC;AAC5D,OAAOC,YAAY,MAAM,qCAAqC;AAC9D,OAAOC,MAAM,MAAM,wBAAwB;AAC3C,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACgB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACkB,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAE/CC,SAAS,CAAC,MAAM;IACd;IACA,MAAMmB,MAAM,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IACrD,IAAIF,MAAM,EAAE;MACVL,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,YAAY,GAAGA,CAAA,KAAM;IACzBF,YAAY,CAACG,UAAU,CAAC,gBAAgB,CAAC;IACzCT,aAAa,CAAC,KAAK,CAAC;IACpBE,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMQ,kBAAkB,GAAIC,MAAM,IAAK;IACrCT,mBAAmB,CAACS,MAAM,CAAC;EAC7B,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BV,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACAhB,SAAS,CAAC,MAAM;IACd,MAAM2B,SAAS,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;IAC7D,IAAIJ,SAAS,CAACK,GAAG,CAAC,MAAM,CAAC,KAAK,MAAM,EAAE;MACpCd,WAAW,CAAC,IAAI,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,IAAID,QAAQ,EAAE;IACZ,oBAAOT,OAAA,CAACF,OAAO;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpB;EAEA,IAAI,CAACvB,UAAU,EAAE;IACf,oBAAOL,OAAA,CAACP,KAAK;MAACoC,cAAc,EAAEA,CAAA,KAAMvB,aAAa,CAAC,IAAI;IAAE;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC7D;EAEA,oBACE5B,OAAA;IAAK8B,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClB/B,OAAA,CAACH,MAAM;MAACmC,QAAQ,EAAElB;IAAa;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAElC5B,OAAA;MAAM8B,SAAS,EAAC,SAAS;MAAAC,QAAA,EACtBxB,gBAAgB,gBACfP,OAAA,CAACJ,YAAY;QACXqC,QAAQ,EAAE1B,gBAAiB;QAC3B2B,MAAM,EAAEhB;MAAiB;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,gBAEF5B,OAAA,CAAAE,SAAA;QAAA6B,QAAA,gBACE/B,OAAA,CAACN,YAAY;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChB5B,OAAA,CAACL,WAAW;UAACwC,cAAc,EAAEnB;QAAmB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA,eACnD;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACxB,EAAA,CA9DQD,GAAG;AAAAiC,EAAA,GAAHjC,GAAG;AAgEZ,eAAeA,GAAG;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}