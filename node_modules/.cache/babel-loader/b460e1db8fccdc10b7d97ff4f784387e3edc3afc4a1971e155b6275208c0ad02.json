{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/ApiTest.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ApiTest() {\n  _s();\n  const [apiKey, setApiKey] = useState('');\n  const [result, setResult] = useState('');\n  const [loading, setLoading] = useState(false);\n  const testDirectConnection = async () => {\n    if (!apiKey.trim()) {\n      setResult('请先输入API Key');\n      return;\n    }\n    setLoading(true);\n    setResult('测试直接连接中...');\n    try {\n      const response = await fetch('https://api.cursor.com/teams/members', {\n        method: 'GET',\n        headers: {\n          'Authorization': `Basic ${btoa(apiKey + ':')}`\n        }\n      });\n      if (response.ok) {\n        const data = await response.json();\n        setResult(`✅ 直接连接成功！获取到 ${data.length} 个团队成员`);\n      } else {\n        const errorText = await response.text();\n        setResult(`❌ 直接连接失败: ${response.status} - ${errorText}`);\n      }\n    } catch (error) {\n      setResult(`❌ 直接连接错误: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const testProxyConnection = async () => {\n    if (!apiKey.trim()) {\n      setResult('请先输入API Key');\n      return;\n    }\n    setLoading(true);\n    setResult('测试代理连接中...');\n    try {\n      const response = await fetch('/api/teams/members', {\n        method: 'GET',\n        headers: {\n          'Authorization': `Basic ${btoa(apiKey + ':')}`\n        }\n      });\n      if (response.ok) {\n        const data = await response.json();\n        setResult(`✅ 代理连接成功！获取到 ${data.length} 个团队成员`);\n      } else {\n        const errorText = await response.text();\n        setResult(`❌ 代理连接失败: ${response.status} - ${errorText}`);\n      }\n    } catch (error) {\n      setResult(`❌ 代理连接错误: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const testHealthCheck = async () => {\n    setLoading(true);\n    setResult('测试健康检查中...');\n    try {\n      const response = await fetch('/health');\n      if (response.ok) {\n        const data = await response.json();\n        setResult(`✅ 健康检查成功: ${JSON.stringify(data)}`);\n      } else {\n        setResult(`❌ 健康检查失败: ${response.status}`);\n      }\n    } catch (error) {\n      setResult(`❌ 健康检查错误: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '20px',\n      maxWidth: '600px',\n      margin: '0 auto'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"API \\u8FDE\\u63A5\\u6D4B\\u8BD5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        children: \"API Key:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"password\",\n        value: apiKey,\n        onChange: e => setApiKey(e.target.value),\n        placeholder: \"\\u8F93\\u5165\\u60A8\\u7684Cursor Admin API Key\",\n        style: {\n          width: '100%',\n          padding: '10px',\n          margin: '10px 0'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        gap: '10px',\n        marginBottom: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: testHealthCheck,\n        disabled: loading,\n        children: \"\\u6D4B\\u8BD5\\u5065\\u5EB7\\u68C0\\u67E5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: testDirectConnection,\n        disabled: loading,\n        children: \"\\u6D4B\\u8BD5\\u76F4\\u63A5\\u8FDE\\u63A5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: testProxyConnection,\n        disabled: loading,\n        children: \"\\u6D4B\\u8BD5\\u4EE3\\u7406\\u8FDE\\u63A5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this), result && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '15px',\n        borderRadius: '5px',\n        backgroundColor: result.includes('✅') ? '#e8f5e8' : '#ffebee',\n        color: result.includes('✅') ? '#2e7d32' : '#d32f2f',\n        border: `1px solid ${result.includes('✅') ? '#c8e6c9' : '#ffcdd2'}`,\n        whiteSpace: 'pre-wrap',\n        wordBreak: 'break-word'\n      },\n      children: result\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 90,\n    columnNumber: 5\n  }, this);\n}\n_s(ApiTest, \"XxIMxUWHZtqkZxIO7VygMJuLvAY=\");\n_c = ApiTest;\nexport default ApiTest;\nvar _c;\n$RefreshReg$(_c, \"ApiTest\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "ApiTest", "_s", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "result", "setResult", "loading", "setLoading", "testDirectConnection", "trim", "response", "fetch", "method", "headers", "btoa", "ok", "data", "json", "length", "errorText", "text", "status", "error", "message", "testProxyConnection", "testHealthCheck", "JSON", "stringify", "style", "padding", "max<PERSON><PERSON><PERSON>", "margin", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginBottom", "type", "value", "onChange", "e", "target", "placeholder", "width", "display", "gap", "onClick", "disabled", "borderRadius", "backgroundColor", "includes", "color", "border", "whiteSpace", "wordBreak", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/ApiTest.js"], "sourcesContent": ["import React, { useState } from 'react';\n\nfunction ApiTest() {\n  const [apiKey, setApiKey] = useState('');\n  const [result, setResult] = useState('');\n  const [loading, setLoading] = useState(false);\n\n  const testDirectConnection = async () => {\n    if (!apiKey.trim()) {\n      setResult('请先输入API Key');\n      return;\n    }\n\n    setLoading(true);\n    setResult('测试直接连接中...');\n\n    try {\n      const response = await fetch('https://api.cursor.com/teams/members', {\n        method: 'GET',\n        headers: {\n          'Authorization': `Basic ${btoa(apiKey + ':')}`\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setResult(`✅ 直接连接成功！获取到 ${data.length} 个团队成员`);\n      } else {\n        const errorText = await response.text();\n        setResult(`❌ 直接连接失败: ${response.status} - ${errorText}`);\n      }\n    } catch (error) {\n      setResult(`❌ 直接连接错误: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const testProxyConnection = async () => {\n    if (!apiKey.trim()) {\n      setResult('请先输入API Key');\n      return;\n    }\n\n    setLoading(true);\n    setResult('测试代理连接中...');\n\n    try {\n      const response = await fetch('/api/teams/members', {\n        method: 'GET',\n        headers: {\n          'Authorization': `Basic ${btoa(apiKey + ':')}`\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setResult(`✅ 代理连接成功！获取到 ${data.length} 个团队成员`);\n      } else {\n        const errorText = await response.text();\n        setResult(`❌ 代理连接失败: ${response.status} - ${errorText}`);\n      }\n    } catch (error) {\n      setResult(`❌ 代理连接错误: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const testHealthCheck = async () => {\n    setLoading(true);\n    setResult('测试健康检查中...');\n\n    try {\n      const response = await fetch('/health');\n      if (response.ok) {\n        const data = await response.json();\n        setResult(`✅ 健康检查成功: ${JSON.stringify(data)}`);\n      } else {\n        setResult(`❌ 健康检查失败: ${response.status}`);\n      }\n    } catch (error) {\n      setResult(`❌ 健康检查错误: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div style={{ padding: '20px', maxWidth: '600px', margin: '0 auto' }}>\n      <h2>API 连接测试</h2>\n      \n      <div style={{ marginBottom: '20px' }}>\n        <label>API Key:</label>\n        <input\n          type=\"password\"\n          value={apiKey}\n          onChange={(e) => setApiKey(e.target.value)}\n          placeholder=\"输入您的Cursor Admin API Key\"\n          style={{ width: '100%', padding: '10px', margin: '10px 0' }}\n        />\n      </div>\n\n      <div style={{ display: 'flex', gap: '10px', marginBottom: '20px' }}>\n        <button onClick={testHealthCheck} disabled={loading}>\n          测试健康检查\n        </button>\n        <button onClick={testDirectConnection} disabled={loading}>\n          测试直接连接\n        </button>\n        <button onClick={testProxyConnection} disabled={loading}>\n          测试代理连接\n        </button>\n      </div>\n\n      {result && (\n        <div style={{\n          padding: '15px',\n          borderRadius: '5px',\n          backgroundColor: result.includes('✅') ? '#e8f5e8' : '#ffebee',\n          color: result.includes('✅') ? '#2e7d32' : '#d32f2f',\n          border: `1px solid ${result.includes('✅') ? '#c8e6c9' : '#ffcdd2'}`,\n          whiteSpace: 'pre-wrap',\n          wordBreak: 'break-word'\n        }}>\n          {result}\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport default ApiTest;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,SAASC,OAAOA,CAAA,EAAG;EAAAC,EAAA;EACjB,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGN,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACO,MAAM,EAAEC,SAAS,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAMW,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACN,MAAM,CAACO,IAAI,CAAC,CAAC,EAAE;MAClBJ,SAAS,CAAC,aAAa,CAAC;MACxB;IACF;IAEAE,UAAU,CAAC,IAAI,CAAC;IAChBF,SAAS,CAAC,YAAY,CAAC;IAEvB,IAAI;MACF,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAAC,sCAAsC,EAAE;QACnEC,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,eAAe,EAAE,SAASC,IAAI,CAACZ,MAAM,GAAG,GAAG,CAAC;QAC9C;MACF,CAAC,CAAC;MAEF,IAAIQ,QAAQ,CAACK,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;QAClCZ,SAAS,CAAC,gBAAgBW,IAAI,CAACE,MAAM,QAAQ,CAAC;MAChD,CAAC,MAAM;QACL,MAAMC,SAAS,GAAG,MAAMT,QAAQ,CAACU,IAAI,CAAC,CAAC;QACvCf,SAAS,CAAC,aAAaK,QAAQ,CAACW,MAAM,MAAMF,SAAS,EAAE,CAAC;MAC1D;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdjB,SAAS,CAAC,aAAaiB,KAAK,CAACC,OAAO,EAAE,CAAC;IACzC,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiB,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI,CAACtB,MAAM,CAACO,IAAI,CAAC,CAAC,EAAE;MAClBJ,SAAS,CAAC,aAAa,CAAC;MACxB;IACF;IAEAE,UAAU,CAAC,IAAI,CAAC;IAChBF,SAAS,CAAC,YAAY,CAAC;IAEvB,IAAI;MACF,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAAC,oBAAoB,EAAE;QACjDC,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,eAAe,EAAE,SAASC,IAAI,CAACZ,MAAM,GAAG,GAAG,CAAC;QAC9C;MACF,CAAC,CAAC;MAEF,IAAIQ,QAAQ,CAACK,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;QAClCZ,SAAS,CAAC,gBAAgBW,IAAI,CAACE,MAAM,QAAQ,CAAC;MAChD,CAAC,MAAM;QACL,MAAMC,SAAS,GAAG,MAAMT,QAAQ,CAACU,IAAI,CAAC,CAAC;QACvCf,SAAS,CAAC,aAAaK,QAAQ,CAACW,MAAM,MAAMF,SAAS,EAAE,CAAC;MAC1D;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdjB,SAAS,CAAC,aAAaiB,KAAK,CAACC,OAAO,EAAE,CAAC;IACzC,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClClB,UAAU,CAAC,IAAI,CAAC;IAChBF,SAAS,CAAC,YAAY,CAAC;IAEvB,IAAI;MACF,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAAC,SAAS,CAAC;MACvC,IAAID,QAAQ,CAACK,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;QAClCZ,SAAS,CAAC,aAAaqB,IAAI,CAACC,SAAS,CAACX,IAAI,CAAC,EAAE,CAAC;MAChD,CAAC,MAAM;QACLX,SAAS,CAAC,aAAaK,QAAQ,CAACW,MAAM,EAAE,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdjB,SAAS,CAAC,aAAaiB,KAAK,CAACC,OAAO,EAAE,CAAC;IACzC,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACER,OAAA;IAAK6B,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,QAAQ,EAAE,OAAO;MAAEC,MAAM,EAAE;IAAS,CAAE;IAAAC,QAAA,gBACnEjC,OAAA;MAAAiC,QAAA,EAAI;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAEjBrC,OAAA;MAAK6B,KAAK,EAAE;QAAES,YAAY,EAAE;MAAO,CAAE;MAAAL,QAAA,gBACnCjC,OAAA;QAAAiC,QAAA,EAAO;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACvBrC,OAAA;QACEuC,IAAI,EAAC,UAAU;QACfC,KAAK,EAAErC,MAAO;QACdsC,QAAQ,EAAGC,CAAC,IAAKtC,SAAS,CAACsC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;QAC3CI,WAAW,EAAC,8CAA0B;QACtCf,KAAK,EAAE;UAAEgB,KAAK,EAAE,MAAM;UAAEf,OAAO,EAAE,MAAM;UAAEE,MAAM,EAAE;QAAS;MAAE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENrC,OAAA;MAAK6B,KAAK,EAAE;QAAEiB,OAAO,EAAE,MAAM;QAAEC,GAAG,EAAE,MAAM;QAAET,YAAY,EAAE;MAAO,CAAE;MAAAL,QAAA,gBACjEjC,OAAA;QAAQgD,OAAO,EAAEtB,eAAgB;QAACuB,QAAQ,EAAE1C,OAAQ;QAAA0B,QAAA,EAAC;MAErD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTrC,OAAA;QAAQgD,OAAO,EAAEvC,oBAAqB;QAACwC,QAAQ,EAAE1C,OAAQ;QAAA0B,QAAA,EAAC;MAE1D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTrC,OAAA;QAAQgD,OAAO,EAAEvB,mBAAoB;QAACwB,QAAQ,EAAE1C,OAAQ;QAAA0B,QAAA,EAAC;MAEzD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAELhC,MAAM,iBACLL,OAAA;MAAK6B,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfoB,YAAY,EAAE,KAAK;QACnBC,eAAe,EAAE9C,MAAM,CAAC+C,QAAQ,CAAC,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;QAC7DC,KAAK,EAAEhD,MAAM,CAAC+C,QAAQ,CAAC,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;QACnDE,MAAM,EAAE,aAAajD,MAAM,CAAC+C,QAAQ,CAAC,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS,EAAE;QACnEG,UAAU,EAAE,UAAU;QACtBC,SAAS,EAAE;MACb,CAAE;MAAAvB,QAAA,EACC5B;IAAM;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACnC,EAAA,CAhIQD,OAAO;AAAAwD,EAAA,GAAPxD,OAAO;AAkIhB,eAAeA,OAAO;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}