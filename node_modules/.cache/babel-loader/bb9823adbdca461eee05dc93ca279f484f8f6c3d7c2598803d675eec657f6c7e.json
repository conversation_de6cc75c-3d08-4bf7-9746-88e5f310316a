{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Dashboard/TeamOverview.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { getTeamUsage } from '../../services/api';\nimport TrendLineChart from '../Charts/TrendLineChart';\nimport DateRangePicker from '../UI/DateRangePicker';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction TeamOverview() {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [usageData, setUsageData] = useState(null);\n  const [dateRange, setDateRange] = useState({\n    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],\n    endDate: new Date().toISOString().split('T')[0]\n  });\n  useEffect(() => {\n    fetchUsageData();\n  }, [dateRange]);\n  const fetchUsageData = async () => {\n    setLoading(true);\n    try {\n      console.log('正在获取团队使用数据...');\n      const data = await getTeamUsage(dateRange.startDate, dateRange.endDate);\n      console.log('团队使用数据响应:', data);\n      setUsageData(data);\n      setError('');\n    } catch (err) {\n      console.error('获取团队使用数据失败:', err);\n      setError(`获取团队使用数据失败: ${err.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 计算总提示次数\n  const calculateTotalPrompts = data => {\n    if (!data || !data.data) return 0;\n    return data.data.reduce((total, dayData) => {\n      return total + (dayData.composerRequests || 0) + (dayData.chatRequests || 0) + (dayData.agentRequests || 0);\n    }, 0);\n  };\n\n  // 计算总代码行数\n  const calculateTotalLines = data => {\n    if (!data || !data.data) return 0;\n    return data.data.reduce((total, dayData) => {\n      return total + (dayData.totalLinesAdded || 0) + (dayData.totalLinesDeleted || 0);\n    }, 0);\n  };\n\n  // 计算活跃天数\n  const calculateActiveDays = data => {\n    if (!data || !data.data) return 0;\n    return data.data.filter(dayData => dayData.isActive).length;\n  };\n  if (loading) return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"\\u52A0\\u8F7D\\u4E2D...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 23\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"error-message\",\n    children: error\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 21\n  }, this);\n  if (!usageData) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"team-overview\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"\\u56E2\\u961F\\u4F7F\\u7528\\u6982\\u89C8\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DateRangePicker, {\n      startDate: dateRange.startDate,\n      endDate: dateRange.endDate,\n      onChange: setDateRange\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats-cards\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"\\u603B\\u63D0\\u793A\\u6B21\\u6570\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"stat-value\",\n          children: calculateTotalPrompts(usageData)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"\\u603B\\u4EE3\\u7801\\u884C\\u6570\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"stat-value\",\n          children: calculateTotalLines(usageData)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"\\u6D3B\\u8DC3\\u5929\\u6570\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"stat-value\",\n          children: calculateActiveDays(usageData)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chart-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"\\u4F7F\\u7528\\u8D8B\\u52BF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TrendLineChart, {\n        data: formatChartData(usageData)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 62,\n    columnNumber: 5\n  }, this);\n}\n_s(TeamOverview, \"H57tu/n2GspRym0LrHNlfmkiz6s=\");\n_c = TeamOverview;\nexport default TeamOverview;\nvar _c;\n$RefreshReg$(_c, \"TeamOverview\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "getTeamUsage", "TrendLineChart", "DateRangePicker", "jsxDEV", "_jsxDEV", "TeamOverview", "_s", "loading", "setLoading", "error", "setError", "usageData", "setUsageData", "date<PERSON><PERSON><PERSON>", "setDateRange", "startDate", "Date", "now", "toISOString", "split", "endDate", "fetchUsageData", "console", "log", "data", "err", "message", "calculateTotalPrompts", "reduce", "total", "dayData", "composerRequests", "chatRequests", "agentRequests", "calculateTotalLines", "totalLinesAdded", "totalLinesDeleted", "calculateActiveDays", "filter", "isActive", "length", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "onChange", "formatChartData", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Dashboard/TeamOverview.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { getTeamUsage } from '../../services/api';\nimport TrendLineChart from '../Charts/TrendLineChart';\nimport DateRangePicker from '../UI/DateRangePicker';\n\nfunction TeamOverview() {\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [usageData, setUsageData] = useState(null);\n  const [dateRange, setDateRange] = useState({\n    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],\n    endDate: new Date().toISOString().split('T')[0]\n  });\n\n  useEffect(() => {\n    fetchUsageData();\n  }, [dateRange]);\n\n  const fetchUsageData = async () => {\n    setLoading(true);\n    try {\n      console.log('正在获取团队使用数据...');\n      const data = await getTeamUsage(dateRange.startDate, dateRange.endDate);\n      console.log('团队使用数据响应:', data);\n      setUsageData(data);\n      setError('');\n    } catch (err) {\n      console.error('获取团队使用数据失败:', err);\n      setError(`获取团队使用数据失败: ${err.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 计算总提示次数\n  const calculateTotalPrompts = (data) => {\n    if (!data || !data.data) return 0;\n    return data.data.reduce((total, dayData) => {\n      return total + (dayData.composerRequests || 0) + (dayData.chatRequests || 0) + (dayData.agentRequests || 0);\n    }, 0);\n  };\n\n  // 计算总代码行数\n  const calculateTotalLines = (data) => {\n    if (!data || !data.data) return 0;\n    return data.data.reduce((total, dayData) => {\n      return total + (dayData.totalLinesAdded || 0) + (dayData.totalLinesDeleted || 0);\n    }, 0);\n  };\n\n  // 计算活跃天数\n  const calculateActiveDays = (data) => {\n    if (!data || !data.data) return 0;\n    return data.data.filter(dayData => dayData.isActive).length;\n  };\n\n  if (loading) return <div>加载中...</div>;\n  if (error) return <div className=\"error-message\">{error}</div>;\n  if (!usageData) return null;\n\n  return (\n    <div className=\"team-overview\">\n      <h2>团队使用概览</h2>\n      \n      <DateRangePicker \n        startDate={dateRange.startDate}\n        endDate={dateRange.endDate}\n        onChange={setDateRange}\n      />\n      \n      <div className=\"stats-cards\">\n        <div className=\"stat-card\">\n          <h3>总提示次数</h3>\n          <p className=\"stat-value\">{calculateTotalPrompts(usageData)}</p>\n        </div>\n        <div className=\"stat-card\">\n          <h3>总代码行数</h3>\n          <p className=\"stat-value\">{calculateTotalLines(usageData)}</p>\n        </div>\n        <div className=\"stat-card\">\n          <h3>活跃天数</h3>\n          <p className=\"stat-value\">{calculateActiveDays(usageData)}</p>\n        </div>\n      </div>\n      \n      <div className=\"chart-container\">\n        <h3>使用趋势</h3>\n        <TrendLineChart data={formatChartData(usageData)} />\n      </div>\n    </div>\n  );\n}\n\nexport default TeamOverview;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,YAAY,QAAQ,oBAAoB;AACjD,OAAOC,cAAc,MAAM,0BAA0B;AACrD,OAAOC,eAAe,MAAM,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC;IACzCiB,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACtFC,OAAO,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;EAChD,CAAC,CAAC;EAEFpB,SAAS,CAAC,MAAM;IACdsB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACR,SAAS,CAAC,CAAC;EAEf,MAAMQ,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjCb,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACFc,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5B,MAAMC,IAAI,GAAG,MAAMxB,YAAY,CAACa,SAAS,CAACE,SAAS,EAAEF,SAAS,CAACO,OAAO,CAAC;MACvEE,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEC,IAAI,CAAC;MAC9BZ,YAAY,CAACY,IAAI,CAAC;MAClBd,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,OAAOe,GAAG,EAAE;MACZH,OAAO,CAACb,KAAK,CAAC,aAAa,EAAEgB,GAAG,CAAC;MACjCf,QAAQ,CAAC,eAAee,GAAG,CAACC,OAAO,EAAE,CAAC;IACxC,CAAC,SAAS;MACRlB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMmB,qBAAqB,GAAIH,IAAI,IAAK;IACtC,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAACA,IAAI,EAAE,OAAO,CAAC;IACjC,OAAOA,IAAI,CAACA,IAAI,CAACI,MAAM,CAAC,CAACC,KAAK,EAAEC,OAAO,KAAK;MAC1C,OAAOD,KAAK,IAAIC,OAAO,CAACC,gBAAgB,IAAI,CAAC,CAAC,IAAID,OAAO,CAACE,YAAY,IAAI,CAAC,CAAC,IAAIF,OAAO,CAACG,aAAa,IAAI,CAAC,CAAC;IAC7G,CAAC,EAAE,CAAC,CAAC;EACP,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAIV,IAAI,IAAK;IACpC,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAACA,IAAI,EAAE,OAAO,CAAC;IACjC,OAAOA,IAAI,CAACA,IAAI,CAACI,MAAM,CAAC,CAACC,KAAK,EAAEC,OAAO,KAAK;MAC1C,OAAOD,KAAK,IAAIC,OAAO,CAACK,eAAe,IAAI,CAAC,CAAC,IAAIL,OAAO,CAACM,iBAAiB,IAAI,CAAC,CAAC;IAClF,CAAC,EAAE,CAAC,CAAC;EACP,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAIb,IAAI,IAAK;IACpC,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAACA,IAAI,EAAE,OAAO,CAAC;IACjC,OAAOA,IAAI,CAACA,IAAI,CAACc,MAAM,CAACR,OAAO,IAAIA,OAAO,CAACS,QAAQ,CAAC,CAACC,MAAM;EAC7D,CAAC;EAED,IAAIjC,OAAO,EAAE,oBAAOH,OAAA;IAAAqC,QAAA,EAAK;EAAM;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EACrC,IAAIpC,KAAK,EAAE,oBAAOL,OAAA;IAAK0C,SAAS,EAAC,eAAe;IAAAL,QAAA,EAAEhC;EAAK;IAAAiC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAC9D,IAAI,CAAClC,SAAS,EAAE,OAAO,IAAI;EAE3B,oBACEP,OAAA;IAAK0C,SAAS,EAAC,eAAe;IAAAL,QAAA,gBAC5BrC,OAAA;MAAAqC,QAAA,EAAI;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAEfzC,OAAA,CAACF,eAAe;MACda,SAAS,EAAEF,SAAS,CAACE,SAAU;MAC/BK,OAAO,EAAEP,SAAS,CAACO,OAAQ;MAC3B2B,QAAQ,EAAEjC;IAAa;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC,eAEFzC,OAAA;MAAK0C,SAAS,EAAC,aAAa;MAAAL,QAAA,gBAC1BrC,OAAA;QAAK0C,SAAS,EAAC,WAAW;QAAAL,QAAA,gBACxBrC,OAAA;UAAAqC,QAAA,EAAI;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdzC,OAAA;UAAG0C,SAAS,EAAC,YAAY;UAAAL,QAAA,EAAEd,qBAAqB,CAAChB,SAAS;QAAC;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,eACNzC,OAAA;QAAK0C,SAAS,EAAC,WAAW;QAAAL,QAAA,gBACxBrC,OAAA;UAAAqC,QAAA,EAAI;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdzC,OAAA;UAAG0C,SAAS,EAAC,YAAY;UAAAL,QAAA,EAAEP,mBAAmB,CAACvB,SAAS;QAAC;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,eACNzC,OAAA;QAAK0C,SAAS,EAAC,WAAW;QAAAL,QAAA,gBACxBrC,OAAA;UAAAqC,QAAA,EAAI;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACbzC,OAAA;UAAG0C,SAAS,EAAC,YAAY;UAAAL,QAAA,EAAEJ,mBAAmB,CAAC1B,SAAS;QAAC;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENzC,OAAA;MAAK0C,SAAS,EAAC,iBAAiB;MAAAL,QAAA,gBAC9BrC,OAAA;QAAAqC,QAAA,EAAI;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACbzC,OAAA,CAACH,cAAc;QAACuB,IAAI,EAAEwB,eAAe,CAACrC,SAAS;MAAE;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACvC,EAAA,CAtFQD,YAAY;AAAA4C,EAAA,GAAZ5C,YAAY;AAwFrB,eAAeA,YAAY;AAAC,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}