{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Auth/Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { testApiKey } from '../../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Login({\n  onLoginSuccess\n}) {\n  _s();\n  const [apiKey, setApiKey] = useState('');\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    try {\n      // 测试API Key是否有效\n      const result = await testApiKey(apiKey);\n      if (result.valid) {\n        // 登录成功\n        onLoginSuccess();\n      } else {\n        setError(result.error || 'API Key无效，请检查后重试');\n        localStorage.removeItem('cursor_api_key');\n      }\n    } catch (err) {\n      setError('连接失败，请检查网络连接');\n      localStorage.removeItem('cursor_api_key');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"login-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Cursor\\u56E2\\u961F\\u4F7F\\u7528\\u60C5\\u51B5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"API Key\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"password\",\n          value: apiKey,\n          onChange: e => setApiKey(e.target.value),\n          placeholder: \"\\u8F93\\u5165\\u60A8\\u7684Cursor Admin API Key\",\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 19\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: loading,\n        children: loading ? '验证中...' : '登录'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this);\n}\n_s(Login, \"h3CWO2ptiM/NVyhcuZPyQv8gIBs=\");\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "test<PERSON>pi<PERSON>ey", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "onLoginSuccess", "_s", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "error", "setError", "loading", "setLoading", "handleSubmit", "e", "preventDefault", "result", "valid", "localStorage", "removeItem", "err", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "value", "onChange", "target", "placeholder", "required", "disabled", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Auth/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { testApiKey } from '../../services/api';\n\nfunction Login({ onLoginSuccess }) {\n  const [apiKey, setApiKey] = useState('');\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    \n    try {\n      // 测试API Key是否有效\n      const result = await testApiKey(apiKey);\n      \n      if (result.valid) {\n        // 登录成功\n        onLoginSuccess();\n      } else {\n        setError(result.error || 'API Key无效，请检查后重试');\n        localStorage.removeItem('cursor_api_key');\n      }\n    } catch (err) {\n      setError('连接失败，请检查网络连接');\n      localStorage.removeItem('cursor_api_key');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"login-container\">\n      <h2>Cursor团队使用情况</h2>\n      <form onSubmit={handleSubmit}>\n        <div className=\"form-group\">\n          <label>API Key</label>\n          <input\n            type=\"password\"\n            value={apiKey}\n            onChange={(e) => setApiKey(e.target.value)}\n            placeholder=\"输入您的Cursor Admin API Key\"\n            required\n          />\n        </div>\n        {error && <div className=\"error-message\">{error}</div>}\n        <button type=\"submit\" disabled={loading}>\n          {loading ? '验证中...' : '登录'}\n        </button>\n      </form>\n    </div>\n  );\n}\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,UAAU,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,SAASC,KAAKA,CAAC;EAAEC;AAAe,CAAC,EAAE;EAAAC,EAAA;EACjC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACS,KAAK,EAAEC,QAAQ,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAMa,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBH,UAAU,CAAC,IAAI,CAAC;IAChBF,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF;MACA,MAAMM,MAAM,GAAG,MAAMf,UAAU,CAACM,MAAM,CAAC;MAEvC,IAAIS,MAAM,CAACC,KAAK,EAAE;QAChB;QACAZ,cAAc,CAAC,CAAC;MAClB,CAAC,MAAM;QACLK,QAAQ,CAACM,MAAM,CAACP,KAAK,IAAI,kBAAkB,CAAC;QAC5CS,YAAY,CAACC,UAAU,CAAC,gBAAgB,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZV,QAAQ,CAAC,cAAc,CAAC;MACxBQ,YAAY,CAACC,UAAU,CAAC,gBAAgB,CAAC;IAC3C,CAAC,SAAS;MACRP,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACET,OAAA;IAAKkB,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC9BnB,OAAA;MAAAmB,QAAA,EAAI;IAAY;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACrBvB,OAAA;MAAMwB,QAAQ,EAAEd,YAAa;MAAAS,QAAA,gBAC3BnB,OAAA;QAAKkB,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBnB,OAAA;UAAAmB,QAAA,EAAO;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACtBvB,OAAA;UACEyB,IAAI,EAAC,UAAU;UACfC,KAAK,EAAEtB,MAAO;UACduB,QAAQ,EAAGhB,CAAC,IAAKN,SAAS,CAACM,CAAC,CAACiB,MAAM,CAACF,KAAK,CAAE;UAC3CG,WAAW,EAAC,8CAA0B;UACtCC,QAAQ;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EACLjB,KAAK,iBAAIN,OAAA;QAAKkB,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAEb;MAAK;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACtDvB,OAAA;QAAQyB,IAAI,EAAC,QAAQ;QAACM,QAAQ,EAAEvB,OAAQ;QAAAW,QAAA,EACrCX,OAAO,GAAG,QAAQ,GAAG;MAAI;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACpB,EAAA,CAlDQF,KAAK;AAAA+B,EAAA,GAAL/B,KAAK;AAoDd,eAAeA,KAAK;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}