{"ast": null, "code": "// API配置\nexport const API_CONFIG = {\n  // 基础URL - 根据官方文档\n  BASE_URL: 'https://api.cursor.com',\n  // 备选基础URL - 如果上面的不工作，可以尝试这些\n  ALTERNATE_BASE_URLS: ['https://api.cursor.sh', 'https://cursor.sh/api', 'https://cursor.com/api'],\n  // 是否使用代理\n  USE_PROXY: false,\n  // API路径\n  ENDPOINTS: {\n    TEAM_MEMBERS: '/teams/members',\n    TEAM_USAGE: '/teams/daily-usage-data',\n    TEAM_SPEND: '/teams/spend'\n  },\n  // 认证方式\n  AUTH_METHODS: {\n    BASIC: 'Basic',\n    BEARER: 'Bearer'\n  }\n};\n\n// 调试配置\nexport const DEBUG_CONFIG = {\n  // 是否启用详细日志\n  VERBOSE_LOGGING: true,\n  // 是否在控制台显示API请求和响应\n  LOG_API_CALLS: true\n};", "map": {"version": 3, "names": ["API_CONFIG", "BASE_URL", "ALTERNATE_BASE_URLS", "USE_PROXY", "ENDPOINTS", "TEAM_MEMBERS", "TEAM_USAGE", "TEAM_SPEND", "AUTH_METHODS", "BASIC", "BEARER", "DEBUG_CONFIG", "VERBOSE_LOGGING", "LOG_API_CALLS"], "sources": ["/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/config.js"], "sourcesContent": ["// API配置\nexport const API_CONFIG = {\n  // 基础URL - 根据官方文档\n  BASE_URL: 'https://api.cursor.com',\n  \n  // 备选基础URL - 如果上面的不工作，可以尝试这些\n  ALTERNATE_BASE_URLS: [\n    'https://api.cursor.sh',\n    'https://cursor.sh/api',\n    'https://cursor.com/api'\n  ],\n  \n  // 是否使用代理\n  USE_PROXY: false,\n  \n  // API路径\n  ENDPOINTS: {\n    TEAM_MEMBERS: '/teams/members',\n    TEAM_USAGE: '/teams/daily-usage-data',\n    TEAM_SPEND: '/teams/spend'\n  },\n  \n  // 认证方式\n  AUTH_METHODS: {\n    BASIC: 'Basic',\n    BEARER: 'Bearer'\n  }\n};\n\n// 调试配置\nexport const DEBUG_CONFIG = {\n  // 是否启用详细日志\n  VERBOSE_LOGGING: true,\n  \n  // 是否在控制台显示API请求和响应\n  LOG_API_CALLS: true\n};\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,UAAU,GAAG;EACxB;EACAC,QAAQ,EAAE,wBAAwB;EAElC;EACAC,mBAAmB,EAAE,CACnB,uBAAuB,EACvB,uBAAuB,EACvB,wBAAwB,CACzB;EAED;EACAC,SAAS,EAAE,KAAK;EAEhB;EACAC,SAAS,EAAE;IACTC,YAAY,EAAE,gBAAgB;IAC9BC,UAAU,EAAE,yBAAyB;IACrCC,UAAU,EAAE;EACd,CAAC;EAED;EACAC,YAAY,EAAE;IACZC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE;EACV;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,YAAY,GAAG;EAC1B;EACAC,eAAe,EAAE,IAAI;EAErB;EACAC,aAAa,EAAE;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}