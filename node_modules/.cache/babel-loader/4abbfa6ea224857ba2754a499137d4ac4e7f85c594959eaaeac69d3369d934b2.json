{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Dashboard/TeamOverview.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { getTeamUsage } from '../../services/api';\nimport TrendLineChart from '../Charts/TrendLineChart';\nimport DateRangePicker from '../UI/DateRangePicker';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction TeamOverview() {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [usageData, setUsageData] = useState(null);\n  const [dateRange, setDateRange] = useState({\n    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],\n    endDate: new Date().toISOString().split('T')[0]\n  });\n  useEffect(() => {\n    fetchUsageData();\n  }, [dateRange]);\n  const fetchUsageData = async () => {\n    setLoading(true);\n    try {\n      console.log('正在获取团队使用数据...');\n      const data = await getTeamUsage(dateRange.startDate, dateRange.endDate);\n      console.log('团队使用数据响应:', data);\n      setUsageData(data);\n      setError('');\n    } catch (err) {\n      console.error('获取团队使用数据失败:', err);\n      setError(`获取团队使用数据失败: ${err.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (loading) return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"\\u52A0\\u8F7D\\u4E2D...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 23\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"error-message\",\n    children: error\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 21\n  }, this);\n  if (!usageData) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"team-overview\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"\\u56E2\\u961F\\u4F7F\\u7528\\u6982\\u89C8\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DateRangePicker, {\n      startDate: dateRange.startDate,\n      endDate: dateRange.endDate,\n      onChange: setDateRange\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats-cards\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"\\u603B\\u63D0\\u793A\\u6B21\\u6570\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"stat-value\",\n          children: calculateTotalPrompts(usageData)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"\\u603B\\u4EE3\\u7801\\u884C\\u6570\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"stat-value\",\n          children: calculateTotalLines(usageData)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"\\u6D3B\\u8DC3\\u5929\\u6570\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"stat-value\",\n          children: calculateActiveDays(usageData)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chart-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"\\u4F7F\\u7528\\u8D8B\\u52BF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TrendLineChart, {\n        data: usageData.dailyUsage || []\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 40,\n    columnNumber: 5\n  }, this);\n}\n_s(TeamOverview, \"H57tu/n2GspRym0LrHNlfmkiz6s=\");\n_c = TeamOverview;\nexport default TeamOverview;\nvar _c;\n$RefreshReg$(_c, \"TeamOverview\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "getTeamUsage", "TrendLineChart", "DateRangePicker", "jsxDEV", "_jsxDEV", "TeamOverview", "_s", "loading", "setLoading", "error", "setError", "usageData", "setUsageData", "date<PERSON><PERSON><PERSON>", "setDateRange", "startDate", "Date", "now", "toISOString", "split", "endDate", "fetchUsageData", "console", "log", "data", "err", "message", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "onChange", "calculateTotalPrompts", "calculateTotalLines", "calculateActiveDays", "dailyUsage", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Dashboard/TeamOverview.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { getTeamUsage } from '../../services/api';\nimport TrendLineChart from '../Charts/TrendLineChart';\nimport DateRangePicker from '../UI/DateRangePicker';\n\nfunction TeamOverview() {\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [usageData, setUsageData] = useState(null);\n  const [dateRange, setDateRange] = useState({\n    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],\n    endDate: new Date().toISOString().split('T')[0]\n  });\n\n  useEffect(() => {\n    fetchUsageData();\n  }, [dateRange]);\n\n  const fetchUsageData = async () => {\n    setLoading(true);\n    try {\n      console.log('正在获取团队使用数据...');\n      const data = await getTeamUsage(dateRange.startDate, dateRange.endDate);\n      console.log('团队使用数据响应:', data);\n      setUsageData(data);\n      setError('');\n    } catch (err) {\n      console.error('获取团队使用数据失败:', err);\n      setError(`获取团队使用数据失败: ${err.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) return <div>加载中...</div>;\n  if (error) return <div className=\"error-message\">{error}</div>;\n  if (!usageData) return null;\n\n  return (\n    <div className=\"team-overview\">\n      <h2>团队使用概览</h2>\n      \n      <DateRangePicker \n        startDate={dateRange.startDate}\n        endDate={dateRange.endDate}\n        onChange={setDateRange}\n      />\n      \n      <div className=\"stats-cards\">\n        <div className=\"stat-card\">\n          <h3>总提示次数</h3>\n          <p className=\"stat-value\">{calculateTotalPrompts(usageData)}</p>\n        </div>\n        <div className=\"stat-card\">\n          <h3>总代码行数</h3>\n          <p className=\"stat-value\">{calculateTotalLines(usageData)}</p>\n        </div>\n        <div className=\"stat-card\">\n          <h3>活跃天数</h3>\n          <p className=\"stat-value\">{calculateActiveDays(usageData)}</p>\n        </div>\n      </div>\n      \n      <div className=\"chart-container\">\n        <h3>使用趋势</h3>\n        <TrendLineChart data={usageData.dailyUsage || []} />\n      </div>\n    </div>\n  );\n}\n\nexport default TeamOverview;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,YAAY,QAAQ,oBAAoB;AACjD,OAAOC,cAAc,MAAM,0BAA0B;AACrD,OAAOC,eAAe,MAAM,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC;IACzCiB,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACtFC,OAAO,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;EAChD,CAAC,CAAC;EAEFpB,SAAS,CAAC,MAAM;IACdsB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACR,SAAS,CAAC,CAAC;EAEf,MAAMQ,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjCb,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACFc,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5B,MAAMC,IAAI,GAAG,MAAMxB,YAAY,CAACa,SAAS,CAACE,SAAS,EAAEF,SAAS,CAACO,OAAO,CAAC;MACvEE,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEC,IAAI,CAAC;MAC9BZ,YAAY,CAACY,IAAI,CAAC;MAClBd,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,OAAOe,GAAG,EAAE;MACZH,OAAO,CAACb,KAAK,CAAC,aAAa,EAAEgB,GAAG,CAAC;MACjCf,QAAQ,CAAC,eAAee,GAAG,CAACC,OAAO,EAAE,CAAC;IACxC,CAAC,SAAS;MACRlB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAID,OAAO,EAAE,oBAAOH,OAAA;IAAAuB,QAAA,EAAK;EAAM;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EACrC,IAAItB,KAAK,EAAE,oBAAOL,OAAA;IAAK4B,SAAS,EAAC,eAAe;IAAAL,QAAA,EAAElB;EAAK;IAAAmB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAC9D,IAAI,CAACpB,SAAS,EAAE,OAAO,IAAI;EAE3B,oBACEP,OAAA;IAAK4B,SAAS,EAAC,eAAe;IAAAL,QAAA,gBAC5BvB,OAAA;MAAAuB,QAAA,EAAI;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAEf3B,OAAA,CAACF,eAAe;MACda,SAAS,EAAEF,SAAS,CAACE,SAAU;MAC/BK,OAAO,EAAEP,SAAS,CAACO,OAAQ;MAC3Ba,QAAQ,EAAEnB;IAAa;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC,eAEF3B,OAAA;MAAK4B,SAAS,EAAC,aAAa;MAAAL,QAAA,gBAC1BvB,OAAA;QAAK4B,SAAS,EAAC,WAAW;QAAAL,QAAA,gBACxBvB,OAAA;UAAAuB,QAAA,EAAI;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACd3B,OAAA;UAAG4B,SAAS,EAAC,YAAY;UAAAL,QAAA,EAAEO,qBAAqB,CAACvB,SAAS;QAAC;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,eACN3B,OAAA;QAAK4B,SAAS,EAAC,WAAW;QAAAL,QAAA,gBACxBvB,OAAA;UAAAuB,QAAA,EAAI;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACd3B,OAAA;UAAG4B,SAAS,EAAC,YAAY;UAAAL,QAAA,EAAEQ,mBAAmB,CAACxB,SAAS;QAAC;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,eACN3B,OAAA;QAAK4B,SAAS,EAAC,WAAW;QAAAL,QAAA,gBACxBvB,OAAA;UAAAuB,QAAA,EAAI;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACb3B,OAAA;UAAG4B,SAAS,EAAC,YAAY;UAAAL,QAAA,EAAES,mBAAmB,CAACzB,SAAS;QAAC;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN3B,OAAA;MAAK4B,SAAS,EAAC,iBAAiB;MAAAL,QAAA,gBAC9BvB,OAAA;QAAAuB,QAAA,EAAI;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACb3B,OAAA,CAACH,cAAc;QAACuB,IAAI,EAAEb,SAAS,CAAC0B,UAAU,IAAI;MAAG;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACzB,EAAA,CAhEQD,YAAY;AAAAiC,EAAA,GAAZjC,YAAY;AAkErB,eAAeA,YAAY;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}