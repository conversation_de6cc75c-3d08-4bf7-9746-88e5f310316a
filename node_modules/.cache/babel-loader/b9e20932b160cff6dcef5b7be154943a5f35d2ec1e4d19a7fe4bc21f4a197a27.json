{"ast": null, "code": "/*!\n * Chart.js v3.9.1\n * https://www.chartjs.org\n * (c) 2022 Chart.js Contributors\n * Released under the MIT License\n */\nfunction noop() {}\nconst uid = function () {\n  let id = 0;\n  return function () {\n    return id++;\n  };\n}();\nfunction isNullOrUndef(value) {\n  return value === null || typeof value === 'undefined';\n}\nfunction isArray(value) {\n  if (Array.isArray && Array.isArray(value)) {\n    return true;\n  }\n  const type = Object.prototype.toString.call(value);\n  if (type.slice(0, 7) === '[object' && type.slice(-6) === 'Array]') {\n    return true;\n  }\n  return false;\n}\nfunction isObject(value) {\n  return value !== null && Object.prototype.toString.call(value) === '[object Object]';\n}\nconst isNumberFinite = value => (typeof value === 'number' || value instanceof Number) && isFinite(+value);\nfunction finiteOrDefault(value, defaultValue) {\n  return isNumberFinite(value) ? value : defaultValue;\n}\nfunction valueOrDefault(value, defaultValue) {\n  return typeof value === 'undefined' ? defaultValue : value;\n}\nconst toPercentage = (value, dimension) => typeof value === 'string' && value.endsWith('%') ? parseFloat(value) / 100 : value / dimension;\nconst toDimension = (value, dimension) => typeof value === 'string' && value.endsWith('%') ? parseFloat(value) / 100 * dimension : +value;\nfunction callback(fn, args, thisArg) {\n  if (fn && typeof fn.call === 'function') {\n    return fn.apply(thisArg, args);\n  }\n}\nfunction each(loopable, fn, thisArg, reverse) {\n  let i, len, keys;\n  if (isArray(loopable)) {\n    len = loopable.length;\n    if (reverse) {\n      for (i = len - 1; i >= 0; i--) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    } else {\n      for (i = 0; i < len; i++) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    }\n  } else if (isObject(loopable)) {\n    keys = Object.keys(loopable);\n    len = keys.length;\n    for (i = 0; i < len; i++) {\n      fn.call(thisArg, loopable[keys[i]], keys[i]);\n    }\n  }\n}\nfunction _elementsEqual(a0, a1) {\n  let i, ilen, v0, v1;\n  if (!a0 || !a1 || a0.length !== a1.length) {\n    return false;\n  }\n  for (i = 0, ilen = a0.length; i < ilen; ++i) {\n    v0 = a0[i];\n    v1 = a1[i];\n    if (v0.datasetIndex !== v1.datasetIndex || v0.index !== v1.index) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction clone$1(source) {\n  if (isArray(source)) {\n    return source.map(clone$1);\n  }\n  if (isObject(source)) {\n    const target = Object.create(null);\n    const keys = Object.keys(source);\n    const klen = keys.length;\n    let k = 0;\n    for (; k < klen; ++k) {\n      target[keys[k]] = clone$1(source[keys[k]]);\n    }\n    return target;\n  }\n  return source;\n}\nfunction isValidKey(key) {\n  return ['__proto__', 'prototype', 'constructor'].indexOf(key) === -1;\n}\nfunction _merger(key, target, source, options) {\n  if (!isValidKey(key)) {\n    return;\n  }\n  const tval = target[key];\n  const sval = source[key];\n  if (isObject(tval) && isObject(sval)) {\n    merge(tval, sval, options);\n  } else {\n    target[key] = clone$1(sval);\n  }\n}\nfunction merge(target, source, options) {\n  const sources = isArray(source) ? source : [source];\n  const ilen = sources.length;\n  if (!isObject(target)) {\n    return target;\n  }\n  options = options || {};\n  const merger = options.merger || _merger;\n  for (let i = 0; i < ilen; ++i) {\n    source = sources[i];\n    if (!isObject(source)) {\n      continue;\n    }\n    const keys = Object.keys(source);\n    for (let k = 0, klen = keys.length; k < klen; ++k) {\n      merger(keys[k], target, source, options);\n    }\n  }\n  return target;\n}\nfunction mergeIf(target, source) {\n  return merge(target, source, {\n    merger: _mergerIf\n  });\n}\nfunction _mergerIf(key, target, source) {\n  if (!isValidKey(key)) {\n    return;\n  }\n  const tval = target[key];\n  const sval = source[key];\n  if (isObject(tval) && isObject(sval)) {\n    mergeIf(tval, sval);\n  } else if (!Object.prototype.hasOwnProperty.call(target, key)) {\n    target[key] = clone$1(sval);\n  }\n}\nfunction _deprecated(scope, value, previous, current) {\n  if (value !== undefined) {\n    console.warn(scope + ': \"' + previous + '\" is deprecated. Please use \"' + current + '\" instead');\n  }\n}\nconst keyResolvers = {\n  '': v => v,\n  x: o => o.x,\n  y: o => o.y\n};\nfunction resolveObjectKey(obj, key) {\n  const resolver = keyResolvers[key] || (keyResolvers[key] = _getKeyResolver(key));\n  return resolver(obj);\n}\nfunction _getKeyResolver(key) {\n  const keys = _splitKey(key);\n  return obj => {\n    for (const k of keys) {\n      if (k === '') {\n        break;\n      }\n      obj = obj && obj[k];\n    }\n    return obj;\n  };\n}\nfunction _splitKey(key) {\n  const parts = key.split('.');\n  const keys = [];\n  let tmp = '';\n  for (const part of parts) {\n    tmp += part;\n    if (tmp.endsWith('\\\\')) {\n      tmp = tmp.slice(0, -1) + '.';\n    } else {\n      keys.push(tmp);\n      tmp = '';\n    }\n  }\n  return keys;\n}\nfunction _capitalize(str) {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\nconst defined = value => typeof value !== 'undefined';\nconst isFunction = value => typeof value === 'function';\nconst setsEqual = (a, b) => {\n  if (a.size !== b.size) {\n    return false;\n  }\n  for (const item of a) {\n    if (!b.has(item)) {\n      return false;\n    }\n  }\n  return true;\n};\nfunction _isClickEvent(e) {\n  return e.type === 'mouseup' || e.type === 'click' || e.type === 'contextmenu';\n}\nconst PI = Math.PI;\nconst TAU = 2 * PI;\nconst PITAU = TAU + PI;\nconst INFINITY = Number.POSITIVE_INFINITY;\nconst RAD_PER_DEG = PI / 180;\nconst HALF_PI = PI / 2;\nconst QUARTER_PI = PI / 4;\nconst TWO_THIRDS_PI = PI * 2 / 3;\nconst log10 = Math.log10;\nconst sign = Math.sign;\nfunction niceNum(range) {\n  const roundedRange = Math.round(range);\n  range = almostEquals(range, roundedRange, range / 1000) ? roundedRange : range;\n  const niceRange = Math.pow(10, Math.floor(log10(range)));\n  const fraction = range / niceRange;\n  const niceFraction = fraction <= 1 ? 1 : fraction <= 2 ? 2 : fraction <= 5 ? 5 : 10;\n  return niceFraction * niceRange;\n}\nfunction _factorize(value) {\n  const result = [];\n  const sqrt = Math.sqrt(value);\n  let i;\n  for (i = 1; i < sqrt; i++) {\n    if (value % i === 0) {\n      result.push(i);\n      result.push(value / i);\n    }\n  }\n  if (sqrt === (sqrt | 0)) {\n    result.push(sqrt);\n  }\n  result.sort((a, b) => a - b).pop();\n  return result;\n}\nfunction isNumber(n) {\n  return !isNaN(parseFloat(n)) && isFinite(n);\n}\nfunction almostEquals(x, y, epsilon) {\n  return Math.abs(x - y) < epsilon;\n}\nfunction almostWhole(x, epsilon) {\n  const rounded = Math.round(x);\n  return rounded - epsilon <= x && rounded + epsilon >= x;\n}\nfunction _setMinAndMaxByKey(array, target, property) {\n  let i, ilen, value;\n  for (i = 0, ilen = array.length; i < ilen; i++) {\n    value = array[i][property];\n    if (!isNaN(value)) {\n      target.min = Math.min(target.min, value);\n      target.max = Math.max(target.max, value);\n    }\n  }\n}\nfunction toRadians(degrees) {\n  return degrees * (PI / 180);\n}\nfunction toDegrees(radians) {\n  return radians * (180 / PI);\n}\nfunction _decimalPlaces(x) {\n  if (!isNumberFinite(x)) {\n    return;\n  }\n  let e = 1;\n  let p = 0;\n  while (Math.round(x * e) / e !== x) {\n    e *= 10;\n    p++;\n  }\n  return p;\n}\nfunction getAngleFromPoint(centrePoint, anglePoint) {\n  const distanceFromXCenter = anglePoint.x - centrePoint.x;\n  const distanceFromYCenter = anglePoint.y - centrePoint.y;\n  const radialDistanceFromCenter = Math.sqrt(distanceFromXCenter * distanceFromXCenter + distanceFromYCenter * distanceFromYCenter);\n  let angle = Math.atan2(distanceFromYCenter, distanceFromXCenter);\n  if (angle < -0.5 * PI) {\n    angle += TAU;\n  }\n  return {\n    angle,\n    distance: radialDistanceFromCenter\n  };\n}\nfunction distanceBetweenPoints(pt1, pt2) {\n  return Math.sqrt(Math.pow(pt2.x - pt1.x, 2) + Math.pow(pt2.y - pt1.y, 2));\n}\nfunction _angleDiff(a, b) {\n  return (a - b + PITAU) % TAU - PI;\n}\nfunction _normalizeAngle(a) {\n  return (a % TAU + TAU) % TAU;\n}\nfunction _angleBetween(angle, start, end, sameAngleIsFullCircle) {\n  const a = _normalizeAngle(angle);\n  const s = _normalizeAngle(start);\n  const e = _normalizeAngle(end);\n  const angleToStart = _normalizeAngle(s - a);\n  const angleToEnd = _normalizeAngle(e - a);\n  const startToAngle = _normalizeAngle(a - s);\n  const endToAngle = _normalizeAngle(a - e);\n  return a === s || a === e || sameAngleIsFullCircle && s === e || angleToStart > angleToEnd && startToAngle < endToAngle;\n}\nfunction _limitValue(value, min, max) {\n  return Math.max(min, Math.min(max, value));\n}\nfunction _int16Range(value) {\n  return _limitValue(value, -32768, 32767);\n}\nfunction _isBetween(value, start, end, epsilon = 1e-6) {\n  return value >= Math.min(start, end) - epsilon && value <= Math.max(start, end) + epsilon;\n}\nfunction _lookup(table, value, cmp) {\n  cmp = cmp || (index => table[index] < value);\n  let hi = table.length - 1;\n  let lo = 0;\n  let mid;\n  while (hi - lo > 1) {\n    mid = lo + hi >> 1;\n    if (cmp(mid)) {\n      lo = mid;\n    } else {\n      hi = mid;\n    }\n  }\n  return {\n    lo,\n    hi\n  };\n}\nconst _lookupByKey = (table, key, value, last) => _lookup(table, value, last ? index => table[index][key] <= value : index => table[index][key] < value);\nconst _rlookupByKey = (table, key, value) => _lookup(table, value, index => table[index][key] >= value);\nfunction _filterBetween(values, min, max) {\n  let start = 0;\n  let end = values.length;\n  while (start < end && values[start] < min) {\n    start++;\n  }\n  while (end > start && values[end - 1] > max) {\n    end--;\n  }\n  return start > 0 || end < values.length ? values.slice(start, end) : values;\n}\nconst arrayEvents = ['push', 'pop', 'shift', 'splice', 'unshift'];\nfunction listenArrayEvents(array, listener) {\n  if (array._chartjs) {\n    array._chartjs.listeners.push(listener);\n    return;\n  }\n  Object.defineProperty(array, '_chartjs', {\n    configurable: true,\n    enumerable: false,\n    value: {\n      listeners: [listener]\n    }\n  });\n  arrayEvents.forEach(key => {\n    const method = '_onData' + _capitalize(key);\n    const base = array[key];\n    Object.defineProperty(array, key, {\n      configurable: true,\n      enumerable: false,\n      value(...args) {\n        const res = base.apply(this, args);\n        array._chartjs.listeners.forEach(object => {\n          if (typeof object[method] === 'function') {\n            object[method](...args);\n          }\n        });\n        return res;\n      }\n    });\n  });\n}\nfunction unlistenArrayEvents(array, listener) {\n  const stub = array._chartjs;\n  if (!stub) {\n    return;\n  }\n  const listeners = stub.listeners;\n  const index = listeners.indexOf(listener);\n  if (index !== -1) {\n    listeners.splice(index, 1);\n  }\n  if (listeners.length > 0) {\n    return;\n  }\n  arrayEvents.forEach(key => {\n    delete array[key];\n  });\n  delete array._chartjs;\n}\nfunction _arrayUnique(items) {\n  const set = new Set();\n  let i, ilen;\n  for (i = 0, ilen = items.length; i < ilen; ++i) {\n    set.add(items[i]);\n  }\n  if (set.size === ilen) {\n    return items;\n  }\n  return Array.from(set);\n}\nfunction fontString(pixelSize, fontStyle, fontFamily) {\n  return fontStyle + ' ' + pixelSize + 'px ' + fontFamily;\n}\nconst requestAnimFrame = function () {\n  if (typeof window === 'undefined') {\n    return function (callback) {\n      return callback();\n    };\n  }\n  return window.requestAnimationFrame;\n}();\nfunction throttled(fn, thisArg, updateFn) {\n  const updateArgs = updateFn || (args => Array.prototype.slice.call(args));\n  let ticking = false;\n  let args = [];\n  return function (...rest) {\n    args = updateArgs(rest);\n    if (!ticking) {\n      ticking = true;\n      requestAnimFrame.call(window, () => {\n        ticking = false;\n        fn.apply(thisArg, args);\n      });\n    }\n  };\n}\nfunction debounce(fn, delay) {\n  let timeout;\n  return function (...args) {\n    if (delay) {\n      clearTimeout(timeout);\n      timeout = setTimeout(fn, delay, args);\n    } else {\n      fn.apply(this, args);\n    }\n    return delay;\n  };\n}\nconst _toLeftRightCenter = align => align === 'start' ? 'left' : align === 'end' ? 'right' : 'center';\nconst _alignStartEnd = (align, start, end) => align === 'start' ? start : align === 'end' ? end : (start + end) / 2;\nconst _textX = (align, left, right, rtl) => {\n  const check = rtl ? 'left' : 'right';\n  return align === check ? right : align === 'center' ? (left + right) / 2 : left;\n};\nfunction _getStartAndCountOfVisiblePoints(meta, points, animationsDisabled) {\n  const pointCount = points.length;\n  let start = 0;\n  let count = pointCount;\n  if (meta._sorted) {\n    const {\n      iScale,\n      _parsed\n    } = meta;\n    const axis = iScale.axis;\n    const {\n      min,\n      max,\n      minDefined,\n      maxDefined\n    } = iScale.getUserBounds();\n    if (minDefined) {\n      start = _limitValue(Math.min(_lookupByKey(_parsed, iScale.axis, min).lo, animationsDisabled ? pointCount : _lookupByKey(points, axis, iScale.getPixelForValue(min)).lo), 0, pointCount - 1);\n    }\n    if (maxDefined) {\n      count = _limitValue(Math.max(_lookupByKey(_parsed, iScale.axis, max, true).hi + 1, animationsDisabled ? 0 : _lookupByKey(points, axis, iScale.getPixelForValue(max), true).hi + 1), start, pointCount) - start;\n    } else {\n      count = pointCount - start;\n    }\n  }\n  return {\n    start,\n    count\n  };\n}\nfunction _scaleRangesChanged(meta) {\n  const {\n    xScale,\n    yScale,\n    _scaleRanges\n  } = meta;\n  const newRanges = {\n    xmin: xScale.min,\n    xmax: xScale.max,\n    ymin: yScale.min,\n    ymax: yScale.max\n  };\n  if (!_scaleRanges) {\n    meta._scaleRanges = newRanges;\n    return true;\n  }\n  const changed = _scaleRanges.xmin !== xScale.min || _scaleRanges.xmax !== xScale.max || _scaleRanges.ymin !== yScale.min || _scaleRanges.ymax !== yScale.max;\n  Object.assign(_scaleRanges, newRanges);\n  return changed;\n}\nconst atEdge = t => t === 0 || t === 1;\nconst elasticIn = (t, s, p) => -(Math.pow(2, 10 * (t -= 1)) * Math.sin((t - s) * TAU / p));\nconst elasticOut = (t, s, p) => Math.pow(2, -10 * t) * Math.sin((t - s) * TAU / p) + 1;\nconst effects = {\n  linear: t => t,\n  easeInQuad: t => t * t,\n  easeOutQuad: t => -t * (t - 2),\n  easeInOutQuad: t => (t /= 0.5) < 1 ? 0.5 * t * t : -0.5 * (--t * (t - 2) - 1),\n  easeInCubic: t => t * t * t,\n  easeOutCubic: t => (t -= 1) * t * t + 1,\n  easeInOutCubic: t => (t /= 0.5) < 1 ? 0.5 * t * t * t : 0.5 * ((t -= 2) * t * t + 2),\n  easeInQuart: t => t * t * t * t,\n  easeOutQuart: t => -((t -= 1) * t * t * t - 1),\n  easeInOutQuart: t => (t /= 0.5) < 1 ? 0.5 * t * t * t * t : -0.5 * ((t -= 2) * t * t * t - 2),\n  easeInQuint: t => t * t * t * t * t,\n  easeOutQuint: t => (t -= 1) * t * t * t * t + 1,\n  easeInOutQuint: t => (t /= 0.5) < 1 ? 0.5 * t * t * t * t * t : 0.5 * ((t -= 2) * t * t * t * t + 2),\n  easeInSine: t => -Math.cos(t * HALF_PI) + 1,\n  easeOutSine: t => Math.sin(t * HALF_PI),\n  easeInOutSine: t => -0.5 * (Math.cos(PI * t) - 1),\n  easeInExpo: t => t === 0 ? 0 : Math.pow(2, 10 * (t - 1)),\n  easeOutExpo: t => t === 1 ? 1 : -Math.pow(2, -10 * t) + 1,\n  easeInOutExpo: t => atEdge(t) ? t : t < 0.5 ? 0.5 * Math.pow(2, 10 * (t * 2 - 1)) : 0.5 * (-Math.pow(2, -10 * (t * 2 - 1)) + 2),\n  easeInCirc: t => t >= 1 ? t : -(Math.sqrt(1 - t * t) - 1),\n  easeOutCirc: t => Math.sqrt(1 - (t -= 1) * t),\n  easeInOutCirc: t => (t /= 0.5) < 1 ? -0.5 * (Math.sqrt(1 - t * t) - 1) : 0.5 * (Math.sqrt(1 - (t -= 2) * t) + 1),\n  easeInElastic: t => atEdge(t) ? t : elasticIn(t, 0.075, 0.3),\n  easeOutElastic: t => atEdge(t) ? t : elasticOut(t, 0.075, 0.3),\n  easeInOutElastic(t) {\n    const s = 0.1125;\n    const p = 0.45;\n    return atEdge(t) ? t : t < 0.5 ? 0.5 * elasticIn(t * 2, s, p) : 0.5 + 0.5 * elasticOut(t * 2 - 1, s, p);\n  },\n  easeInBack(t) {\n    const s = 1.70158;\n    return t * t * ((s + 1) * t - s);\n  },\n  easeOutBack(t) {\n    const s = 1.70158;\n    return (t -= 1) * t * ((s + 1) * t + s) + 1;\n  },\n  easeInOutBack(t) {\n    let s = 1.70158;\n    if ((t /= 0.5) < 1) {\n      return 0.5 * (t * t * (((s *= 1.525) + 1) * t - s));\n    }\n    return 0.5 * ((t -= 2) * t * (((s *= 1.525) + 1) * t + s) + 2);\n  },\n  easeInBounce: t => 1 - effects.easeOutBounce(1 - t),\n  easeOutBounce(t) {\n    const m = 7.5625;\n    const d = 2.75;\n    if (t < 1 / d) {\n      return m * t * t;\n    }\n    if (t < 2 / d) {\n      return m * (t -= 1.5 / d) * t + 0.75;\n    }\n    if (t < 2.5 / d) {\n      return m * (t -= 2.25 / d) * t + 0.9375;\n    }\n    return m * (t -= 2.625 / d) * t + 0.984375;\n  },\n  easeInOutBounce: t => t < 0.5 ? effects.easeInBounce(t * 2) * 0.5 : effects.easeOutBounce(t * 2 - 1) * 0.5 + 0.5\n};\n\n/*!\n * @kurkle/color v0.2.1\n * https://github.com/kurkle/color#readme\n * (c) 2022 Jukka Kurkela\n * Released under the MIT License\n */\nfunction round(v) {\n  return v + 0.5 | 0;\n}\nconst lim = (v, l, h) => Math.max(Math.min(v, h), l);\nfunction p2b(v) {\n  return lim(round(v * 2.55), 0, 255);\n}\nfunction n2b(v) {\n  return lim(round(v * 255), 0, 255);\n}\nfunction b2n(v) {\n  return lim(round(v / 2.55) / 100, 0, 1);\n}\nfunction n2p(v) {\n  return lim(round(v * 100), 0, 100);\n}\nconst map$1 = {\n  0: 0,\n  1: 1,\n  2: 2,\n  3: 3,\n  4: 4,\n  5: 5,\n  6: 6,\n  7: 7,\n  8: 8,\n  9: 9,\n  A: 10,\n  B: 11,\n  C: 12,\n  D: 13,\n  E: 14,\n  F: 15,\n  a: 10,\n  b: 11,\n  c: 12,\n  d: 13,\n  e: 14,\n  f: 15\n};\nconst hex = [...'0123456789ABCDEF'];\nconst h1 = b => hex[b & 0xF];\nconst h2 = b => hex[(b & 0xF0) >> 4] + hex[b & 0xF];\nconst eq = b => (b & 0xF0) >> 4 === (b & 0xF);\nconst isShort = v => eq(v.r) && eq(v.g) && eq(v.b) && eq(v.a);\nfunction hexParse(str) {\n  var len = str.length;\n  var ret;\n  if (str[0] === '#') {\n    if (len === 4 || len === 5) {\n      ret = {\n        r: 255 & map$1[str[1]] * 17,\n        g: 255 & map$1[str[2]] * 17,\n        b: 255 & map$1[str[3]] * 17,\n        a: len === 5 ? map$1[str[4]] * 17 : 255\n      };\n    } else if (len === 7 || len === 9) {\n      ret = {\n        r: map$1[str[1]] << 4 | map$1[str[2]],\n        g: map$1[str[3]] << 4 | map$1[str[4]],\n        b: map$1[str[5]] << 4 | map$1[str[6]],\n        a: len === 9 ? map$1[str[7]] << 4 | map$1[str[8]] : 255\n      };\n    }\n  }\n  return ret;\n}\nconst alpha = (a, f) => a < 255 ? f(a) : '';\nfunction hexString(v) {\n  var f = isShort(v) ? h1 : h2;\n  return v ? '#' + f(v.r) + f(v.g) + f(v.b) + alpha(v.a, f) : undefined;\n}\nconst HUE_RE = /^(hsla?|hwb|hsv)\\(\\s*([-+.e\\d]+)(?:deg)?[\\s,]+([-+.e\\d]+)%[\\s,]+([-+.e\\d]+)%(?:[\\s,]+([-+.e\\d]+)(%)?)?\\s*\\)$/;\nfunction hsl2rgbn(h, s, l) {\n  const a = s * Math.min(l, 1 - l);\n  const f = (n, k = (n + h / 30) % 12) => l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);\n  return [f(0), f(8), f(4)];\n}\nfunction hsv2rgbn(h, s, v) {\n  const f = (n, k = (n + h / 60) % 6) => v - v * s * Math.max(Math.min(k, 4 - k, 1), 0);\n  return [f(5), f(3), f(1)];\n}\nfunction hwb2rgbn(h, w, b) {\n  const rgb = hsl2rgbn(h, 1, 0.5);\n  let i;\n  if (w + b > 1) {\n    i = 1 / (w + b);\n    w *= i;\n    b *= i;\n  }\n  for (i = 0; i < 3; i++) {\n    rgb[i] *= 1 - w - b;\n    rgb[i] += w;\n  }\n  return rgb;\n}\nfunction hueValue(r, g, b, d, max) {\n  if (r === max) {\n    return (g - b) / d + (g < b ? 6 : 0);\n  }\n  if (g === max) {\n    return (b - r) / d + 2;\n  }\n  return (r - g) / d + 4;\n}\nfunction rgb2hsl(v) {\n  const range = 255;\n  const r = v.r / range;\n  const g = v.g / range;\n  const b = v.b / range;\n  const max = Math.max(r, g, b);\n  const min = Math.min(r, g, b);\n  const l = (max + min) / 2;\n  let h, s, d;\n  if (max !== min) {\n    d = max - min;\n    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n    h = hueValue(r, g, b, d, max);\n    h = h * 60 + 0.5;\n  }\n  return [h | 0, s || 0, l];\n}\nfunction calln(f, a, b, c) {\n  return (Array.isArray(a) ? f(a[0], a[1], a[2]) : f(a, b, c)).map(n2b);\n}\nfunction hsl2rgb(h, s, l) {\n  return calln(hsl2rgbn, h, s, l);\n}\nfunction hwb2rgb(h, w, b) {\n  return calln(hwb2rgbn, h, w, b);\n}\nfunction hsv2rgb(h, s, v) {\n  return calln(hsv2rgbn, h, s, v);\n}\nfunction hue(h) {\n  return (h % 360 + 360) % 360;\n}\nfunction hueParse(str) {\n  const m = HUE_RE.exec(str);\n  let a = 255;\n  let v;\n  if (!m) {\n    return;\n  }\n  if (m[5] !== v) {\n    a = m[6] ? p2b(+m[5]) : n2b(+m[5]);\n  }\n  const h = hue(+m[2]);\n  const p1 = +m[3] / 100;\n  const p2 = +m[4] / 100;\n  if (m[1] === 'hwb') {\n    v = hwb2rgb(h, p1, p2);\n  } else if (m[1] === 'hsv') {\n    v = hsv2rgb(h, p1, p2);\n  } else {\n    v = hsl2rgb(h, p1, p2);\n  }\n  return {\n    r: v[0],\n    g: v[1],\n    b: v[2],\n    a: a\n  };\n}\nfunction rotate(v, deg) {\n  var h = rgb2hsl(v);\n  h[0] = hue(h[0] + deg);\n  h = hsl2rgb(h);\n  v.r = h[0];\n  v.g = h[1];\n  v.b = h[2];\n}\nfunction hslString(v) {\n  if (!v) {\n    return;\n  }\n  const a = rgb2hsl(v);\n  const h = a[0];\n  const s = n2p(a[1]);\n  const l = n2p(a[2]);\n  return v.a < 255 ? `hsla(${h}, ${s}%, ${l}%, ${b2n(v.a)})` : `hsl(${h}, ${s}%, ${l}%)`;\n}\nconst map = {\n  x: 'dark',\n  Z: 'light',\n  Y: 're',\n  X: 'blu',\n  W: 'gr',\n  V: 'medium',\n  U: 'slate',\n  A: 'ee',\n  T: 'ol',\n  S: 'or',\n  B: 'ra',\n  C: 'lateg',\n  D: 'ights',\n  R: 'in',\n  Q: 'turquois',\n  E: 'hi',\n  P: 'ro',\n  O: 'al',\n  N: 'le',\n  M: 'de',\n  L: 'yello',\n  F: 'en',\n  K: 'ch',\n  G: 'arks',\n  H: 'ea',\n  I: 'ightg',\n  J: 'wh'\n};\nconst names$1 = {\n  OiceXe: 'f0f8ff',\n  antiquewEte: 'faebd7',\n  aqua: 'ffff',\n  aquamarRe: '7fffd4',\n  azuY: 'f0ffff',\n  beige: 'f5f5dc',\n  bisque: 'ffe4c4',\n  black: '0',\n  blanKedOmond: 'ffebcd',\n  Xe: 'ff',\n  XeviTet: '8a2be2',\n  bPwn: 'a52a2a',\n  burlywood: 'deb887',\n  caMtXe: '5f9ea0',\n  KartYuse: '7fff00',\n  KocTate: 'd2691e',\n  cSO: 'ff7f50',\n  cSnflowerXe: '6495ed',\n  cSnsilk: 'fff8dc',\n  crimson: 'dc143c',\n  cyan: 'ffff',\n  xXe: '8b',\n  xcyan: '8b8b',\n  xgTMnPd: 'b8860b',\n  xWay: 'a9a9a9',\n  xgYF: '6400',\n  xgYy: 'a9a9a9',\n  xkhaki: 'bdb76b',\n  xmagFta: '8b008b',\n  xTivegYF: '556b2f',\n  xSange: 'ff8c00',\n  xScEd: '9932cc',\n  xYd: '8b0000',\n  xsOmon: 'e9967a',\n  xsHgYF: '8fbc8f',\n  xUXe: '483d8b',\n  xUWay: '2f4f4f',\n  xUgYy: '2f4f4f',\n  xQe: 'ced1',\n  xviTet: '9400d3',\n  dAppRk: 'ff1493',\n  dApskyXe: 'bfff',\n  dimWay: '696969',\n  dimgYy: '696969',\n  dodgerXe: '1e90ff',\n  fiYbrick: 'b22222',\n  flSOwEte: 'fffaf0',\n  foYstWAn: '228b22',\n  fuKsia: 'ff00ff',\n  gaRsbSo: 'dcdcdc',\n  ghostwEte: 'f8f8ff',\n  gTd: 'ffd700',\n  gTMnPd: 'daa520',\n  Way: '808080',\n  gYF: '8000',\n  gYFLw: 'adff2f',\n  gYy: '808080',\n  honeyMw: 'f0fff0',\n  hotpRk: 'ff69b4',\n  RdianYd: 'cd5c5c',\n  Rdigo: '4b0082',\n  ivSy: 'fffff0',\n  khaki: 'f0e68c',\n  lavFMr: 'e6e6fa',\n  lavFMrXsh: 'fff0f5',\n  lawngYF: '7cfc00',\n  NmoncEffon: 'fffacd',\n  ZXe: 'add8e6',\n  ZcSO: 'f08080',\n  Zcyan: 'e0ffff',\n  ZgTMnPdLw: 'fafad2',\n  ZWay: 'd3d3d3',\n  ZgYF: '90ee90',\n  ZgYy: 'd3d3d3',\n  ZpRk: 'ffb6c1',\n  ZsOmon: 'ffa07a',\n  ZsHgYF: '20b2aa',\n  ZskyXe: '87cefa',\n  ZUWay: '778899',\n  ZUgYy: '778899',\n  ZstAlXe: 'b0c4de',\n  ZLw: 'ffffe0',\n  lime: 'ff00',\n  limegYF: '32cd32',\n  lRF: 'faf0e6',\n  magFta: 'ff00ff',\n  maPon: '800000',\n  VaquamarRe: '66cdaa',\n  VXe: 'cd',\n  VScEd: 'ba55d3',\n  VpurpN: '9370db',\n  VsHgYF: '3cb371',\n  VUXe: '7b68ee',\n  VsprRggYF: 'fa9a',\n  VQe: '48d1cc',\n  VviTetYd: 'c71585',\n  midnightXe: '191970',\n  mRtcYam: 'f5fffa',\n  mistyPse: 'ffe4e1',\n  moccasR: 'ffe4b5',\n  navajowEte: 'ffdead',\n  navy: '80',\n  Tdlace: 'fdf5e6',\n  Tive: '808000',\n  TivedBb: '6b8e23',\n  Sange: 'ffa500',\n  SangeYd: 'ff4500',\n  ScEd: 'da70d6',\n  pOegTMnPd: 'eee8aa',\n  pOegYF: '98fb98',\n  pOeQe: 'afeeee',\n  pOeviTetYd: 'db7093',\n  papayawEp: 'ffefd5',\n  pHKpuff: 'ffdab9',\n  peru: 'cd853f',\n  pRk: 'ffc0cb',\n  plum: 'dda0dd',\n  powMrXe: 'b0e0e6',\n  purpN: '800080',\n  YbeccapurpN: '663399',\n  Yd: 'ff0000',\n  Psybrown: 'bc8f8f',\n  PyOXe: '4169e1',\n  saddNbPwn: '8b4513',\n  sOmon: 'fa8072',\n  sandybPwn: 'f4a460',\n  sHgYF: '2e8b57',\n  sHshell: 'fff5ee',\n  siFna: 'a0522d',\n  silver: 'c0c0c0',\n  skyXe: '87ceeb',\n  UXe: '6a5acd',\n  UWay: '708090',\n  UgYy: '708090',\n  snow: 'fffafa',\n  sprRggYF: 'ff7f',\n  stAlXe: '4682b4',\n  tan: 'd2b48c',\n  teO: '8080',\n  tEstN: 'd8bfd8',\n  tomato: 'ff6347',\n  Qe: '40e0d0',\n  viTet: 'ee82ee',\n  JHt: 'f5deb3',\n  wEte: 'ffffff',\n  wEtesmoke: 'f5f5f5',\n  Lw: 'ffff00',\n  LwgYF: '9acd32'\n};\nfunction unpack() {\n  const unpacked = {};\n  const keys = Object.keys(names$1);\n  const tkeys = Object.keys(map);\n  let i, j, k, ok, nk;\n  for (i = 0; i < keys.length; i++) {\n    ok = nk = keys[i];\n    for (j = 0; j < tkeys.length; j++) {\n      k = tkeys[j];\n      nk = nk.replace(k, map[k]);\n    }\n    k = parseInt(names$1[ok], 16);\n    unpacked[nk] = [k >> 16 & 0xFF, k >> 8 & 0xFF, k & 0xFF];\n  }\n  return unpacked;\n}\nlet names;\nfunction nameParse(str) {\n  if (!names) {\n    names = unpack();\n    names.transparent = [0, 0, 0, 0];\n  }\n  const a = names[str.toLowerCase()];\n  return a && {\n    r: a[0],\n    g: a[1],\n    b: a[2],\n    a: a.length === 4 ? a[3] : 255\n  };\n}\nconst RGB_RE = /^rgba?\\(\\s*([-+.\\d]+)(%)?[\\s,]+([-+.e\\d]+)(%)?[\\s,]+([-+.e\\d]+)(%)?(?:[\\s,/]+([-+.e\\d]+)(%)?)?\\s*\\)$/;\nfunction rgbParse(str) {\n  const m = RGB_RE.exec(str);\n  let a = 255;\n  let r, g, b;\n  if (!m) {\n    return;\n  }\n  if (m[7] !== r) {\n    const v = +m[7];\n    a = m[8] ? p2b(v) : lim(v * 255, 0, 255);\n  }\n  r = +m[1];\n  g = +m[3];\n  b = +m[5];\n  r = 255 & (m[2] ? p2b(r) : lim(r, 0, 255));\n  g = 255 & (m[4] ? p2b(g) : lim(g, 0, 255));\n  b = 255 & (m[6] ? p2b(b) : lim(b, 0, 255));\n  return {\n    r: r,\n    g: g,\n    b: b,\n    a: a\n  };\n}\nfunction rgbString(v) {\n  return v && (v.a < 255 ? `rgba(${v.r}, ${v.g}, ${v.b}, ${b2n(v.a)})` : `rgb(${v.r}, ${v.g}, ${v.b})`);\n}\nconst to = v => v <= 0.0031308 ? v * 12.92 : Math.pow(v, 1.0 / 2.4) * 1.055 - 0.055;\nconst from = v => v <= 0.04045 ? v / 12.92 : Math.pow((v + 0.055) / 1.055, 2.4);\nfunction interpolate(rgb1, rgb2, t) {\n  const r = from(b2n(rgb1.r));\n  const g = from(b2n(rgb1.g));\n  const b = from(b2n(rgb1.b));\n  return {\n    r: n2b(to(r + t * (from(b2n(rgb2.r)) - r))),\n    g: n2b(to(g + t * (from(b2n(rgb2.g)) - g))),\n    b: n2b(to(b + t * (from(b2n(rgb2.b)) - b))),\n    a: rgb1.a + t * (rgb2.a - rgb1.a)\n  };\n}\nfunction modHSL(v, i, ratio) {\n  if (v) {\n    let tmp = rgb2hsl(v);\n    tmp[i] = Math.max(0, Math.min(tmp[i] + tmp[i] * ratio, i === 0 ? 360 : 1));\n    tmp = hsl2rgb(tmp);\n    v.r = tmp[0];\n    v.g = tmp[1];\n    v.b = tmp[2];\n  }\n}\nfunction clone(v, proto) {\n  return v ? Object.assign(proto || {}, v) : v;\n}\nfunction fromObject(input) {\n  var v = {\n    r: 0,\n    g: 0,\n    b: 0,\n    a: 255\n  };\n  if (Array.isArray(input)) {\n    if (input.length >= 3) {\n      v = {\n        r: input[0],\n        g: input[1],\n        b: input[2],\n        a: 255\n      };\n      if (input.length > 3) {\n        v.a = n2b(input[3]);\n      }\n    }\n  } else {\n    v = clone(input, {\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 1\n    });\n    v.a = n2b(v.a);\n  }\n  return v;\n}\nfunction functionParse(str) {\n  if (str.charAt(0) === 'r') {\n    return rgbParse(str);\n  }\n  return hueParse(str);\n}\nclass Color {\n  constructor(input) {\n    if (input instanceof Color) {\n      return input;\n    }\n    const type = typeof input;\n    let v;\n    if (type === 'object') {\n      v = fromObject(input);\n    } else if (type === 'string') {\n      v = hexParse(input) || nameParse(input) || functionParse(input);\n    }\n    this._rgb = v;\n    this._valid = !!v;\n  }\n  get valid() {\n    return this._valid;\n  }\n  get rgb() {\n    var v = clone(this._rgb);\n    if (v) {\n      v.a = b2n(v.a);\n    }\n    return v;\n  }\n  set rgb(obj) {\n    this._rgb = fromObject(obj);\n  }\n  rgbString() {\n    return this._valid ? rgbString(this._rgb) : undefined;\n  }\n  hexString() {\n    return this._valid ? hexString(this._rgb) : undefined;\n  }\n  hslString() {\n    return this._valid ? hslString(this._rgb) : undefined;\n  }\n  mix(color, weight) {\n    if (color) {\n      const c1 = this.rgb;\n      const c2 = color.rgb;\n      let w2;\n      const p = weight === w2 ? 0.5 : weight;\n      const w = 2 * p - 1;\n      const a = c1.a - c2.a;\n      const w1 = ((w * a === -1 ? w : (w + a) / (1 + w * a)) + 1) / 2.0;\n      w2 = 1 - w1;\n      c1.r = 0xFF & w1 * c1.r + w2 * c2.r + 0.5;\n      c1.g = 0xFF & w1 * c1.g + w2 * c2.g + 0.5;\n      c1.b = 0xFF & w1 * c1.b + w2 * c2.b + 0.5;\n      c1.a = p * c1.a + (1 - p) * c2.a;\n      this.rgb = c1;\n    }\n    return this;\n  }\n  interpolate(color, t) {\n    if (color) {\n      this._rgb = interpolate(this._rgb, color._rgb, t);\n    }\n    return this;\n  }\n  clone() {\n    return new Color(this.rgb);\n  }\n  alpha(a) {\n    this._rgb.a = n2b(a);\n    return this;\n  }\n  clearer(ratio) {\n    const rgb = this._rgb;\n    rgb.a *= 1 - ratio;\n    return this;\n  }\n  greyscale() {\n    const rgb = this._rgb;\n    const val = round(rgb.r * 0.3 + rgb.g * 0.59 + rgb.b * 0.11);\n    rgb.r = rgb.g = rgb.b = val;\n    return this;\n  }\n  opaquer(ratio) {\n    const rgb = this._rgb;\n    rgb.a *= 1 + ratio;\n    return this;\n  }\n  negate() {\n    const v = this._rgb;\n    v.r = 255 - v.r;\n    v.g = 255 - v.g;\n    v.b = 255 - v.b;\n    return this;\n  }\n  lighten(ratio) {\n    modHSL(this._rgb, 2, ratio);\n    return this;\n  }\n  darken(ratio) {\n    modHSL(this._rgb, 2, -ratio);\n    return this;\n  }\n  saturate(ratio) {\n    modHSL(this._rgb, 1, ratio);\n    return this;\n  }\n  desaturate(ratio) {\n    modHSL(this._rgb, 1, -ratio);\n    return this;\n  }\n  rotate(deg) {\n    rotate(this._rgb, deg);\n    return this;\n  }\n}\nfunction index_esm(input) {\n  return new Color(input);\n}\nfunction isPatternOrGradient(value) {\n  if (value && typeof value === 'object') {\n    const type = value.toString();\n    return type === '[object CanvasPattern]' || type === '[object CanvasGradient]';\n  }\n  return false;\n}\nfunction color(value) {\n  return isPatternOrGradient(value) ? value : index_esm(value);\n}\nfunction getHoverColor(value) {\n  return isPatternOrGradient(value) ? value : index_esm(value).saturate(0.5).darken(0.1).hexString();\n}\nconst overrides = Object.create(null);\nconst descriptors = Object.create(null);\nfunction getScope$1(node, key) {\n  if (!key) {\n    return node;\n  }\n  const keys = key.split('.');\n  for (let i = 0, n = keys.length; i < n; ++i) {\n    const k = keys[i];\n    node = node[k] || (node[k] = Object.create(null));\n  }\n  return node;\n}\nfunction set(root, scope, values) {\n  if (typeof scope === 'string') {\n    return merge(getScope$1(root, scope), values);\n  }\n  return merge(getScope$1(root, ''), scope);\n}\nclass Defaults {\n  constructor(_descriptors) {\n    this.animation = undefined;\n    this.backgroundColor = 'rgba(0,0,0,0.1)';\n    this.borderColor = 'rgba(0,0,0,0.1)';\n    this.color = '#666';\n    this.datasets = {};\n    this.devicePixelRatio = context => context.chart.platform.getDevicePixelRatio();\n    this.elements = {};\n    this.events = ['mousemove', 'mouseout', 'click', 'touchstart', 'touchmove'];\n    this.font = {\n      family: \"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif\",\n      size: 12,\n      style: 'normal',\n      lineHeight: 1.2,\n      weight: null\n    };\n    this.hover = {};\n    this.hoverBackgroundColor = (ctx, options) => getHoverColor(options.backgroundColor);\n    this.hoverBorderColor = (ctx, options) => getHoverColor(options.borderColor);\n    this.hoverColor = (ctx, options) => getHoverColor(options.color);\n    this.indexAxis = 'x';\n    this.interaction = {\n      mode: 'nearest',\n      intersect: true,\n      includeInvisible: false\n    };\n    this.maintainAspectRatio = true;\n    this.onHover = null;\n    this.onClick = null;\n    this.parsing = true;\n    this.plugins = {};\n    this.responsive = true;\n    this.scale = undefined;\n    this.scales = {};\n    this.showLine = true;\n    this.drawActiveElementsOnTop = true;\n    this.describe(_descriptors);\n  }\n  set(scope, values) {\n    return set(this, scope, values);\n  }\n  get(scope) {\n    return getScope$1(this, scope);\n  }\n  describe(scope, values) {\n    return set(descriptors, scope, values);\n  }\n  override(scope, values) {\n    return set(overrides, scope, values);\n  }\n  route(scope, name, targetScope, targetName) {\n    const scopeObject = getScope$1(this, scope);\n    const targetScopeObject = getScope$1(this, targetScope);\n    const privateName = '_' + name;\n    Object.defineProperties(scopeObject, {\n      [privateName]: {\n        value: scopeObject[name],\n        writable: true\n      },\n      [name]: {\n        enumerable: true,\n        get() {\n          const local = this[privateName];\n          const target = targetScopeObject[targetName];\n          if (isObject(local)) {\n            return Object.assign({}, target, local);\n          }\n          return valueOrDefault(local, target);\n        },\n        set(value) {\n          this[privateName] = value;\n        }\n      }\n    });\n  }\n}\nvar defaults = new Defaults({\n  _scriptable: name => !name.startsWith('on'),\n  _indexable: name => name !== 'events',\n  hover: {\n    _fallback: 'interaction'\n  },\n  interaction: {\n    _scriptable: false,\n    _indexable: false\n  }\n});\nfunction toFontString(font) {\n  if (!font || isNullOrUndef(font.size) || isNullOrUndef(font.family)) {\n    return null;\n  }\n  return (font.style ? font.style + ' ' : '') + (font.weight ? font.weight + ' ' : '') + font.size + 'px ' + font.family;\n}\nfunction _measureText(ctx, data, gc, longest, string) {\n  let textWidth = data[string];\n  if (!textWidth) {\n    textWidth = data[string] = ctx.measureText(string).width;\n    gc.push(string);\n  }\n  if (textWidth > longest) {\n    longest = textWidth;\n  }\n  return longest;\n}\nfunction _longestText(ctx, font, arrayOfThings, cache) {\n  cache = cache || {};\n  let data = cache.data = cache.data || {};\n  let gc = cache.garbageCollect = cache.garbageCollect || [];\n  if (cache.font !== font) {\n    data = cache.data = {};\n    gc = cache.garbageCollect = [];\n    cache.font = font;\n  }\n  ctx.save();\n  ctx.font = font;\n  let longest = 0;\n  const ilen = arrayOfThings.length;\n  let i, j, jlen, thing, nestedThing;\n  for (i = 0; i < ilen; i++) {\n    thing = arrayOfThings[i];\n    if (thing !== undefined && thing !== null && isArray(thing) !== true) {\n      longest = _measureText(ctx, data, gc, longest, thing);\n    } else if (isArray(thing)) {\n      for (j = 0, jlen = thing.length; j < jlen; j++) {\n        nestedThing = thing[j];\n        if (nestedThing !== undefined && nestedThing !== null && !isArray(nestedThing)) {\n          longest = _measureText(ctx, data, gc, longest, nestedThing);\n        }\n      }\n    }\n  }\n  ctx.restore();\n  const gcLen = gc.length / 2;\n  if (gcLen > arrayOfThings.length) {\n    for (i = 0; i < gcLen; i++) {\n      delete data[gc[i]];\n    }\n    gc.splice(0, gcLen);\n  }\n  return longest;\n}\nfunction _alignPixel(chart, pixel, width) {\n  const devicePixelRatio = chart.currentDevicePixelRatio;\n  const halfWidth = width !== 0 ? Math.max(width / 2, 0.5) : 0;\n  return Math.round((pixel - halfWidth) * devicePixelRatio) / devicePixelRatio + halfWidth;\n}\nfunction clearCanvas(canvas, ctx) {\n  ctx = ctx || canvas.getContext('2d');\n  ctx.save();\n  ctx.resetTransform();\n  ctx.clearRect(0, 0, canvas.width, canvas.height);\n  ctx.restore();\n}\nfunction drawPoint(ctx, options, x, y) {\n  drawPointLegend(ctx, options, x, y, null);\n}\nfunction drawPointLegend(ctx, options, x, y, w) {\n  let type, xOffset, yOffset, size, cornerRadius, width;\n  const style = options.pointStyle;\n  const rotation = options.rotation;\n  const radius = options.radius;\n  let rad = (rotation || 0) * RAD_PER_DEG;\n  if (style && typeof style === 'object') {\n    type = style.toString();\n    if (type === '[object HTMLImageElement]' || type === '[object HTMLCanvasElement]') {\n      ctx.save();\n      ctx.translate(x, y);\n      ctx.rotate(rad);\n      ctx.drawImage(style, -style.width / 2, -style.height / 2, style.width, style.height);\n      ctx.restore();\n      return;\n    }\n  }\n  if (isNaN(radius) || radius <= 0) {\n    return;\n  }\n  ctx.beginPath();\n  switch (style) {\n    default:\n      if (w) {\n        ctx.ellipse(x, y, w / 2, radius, 0, 0, TAU);\n      } else {\n        ctx.arc(x, y, radius, 0, TAU);\n      }\n      ctx.closePath();\n      break;\n    case 'triangle':\n      ctx.moveTo(x + Math.sin(rad) * radius, y - Math.cos(rad) * radius);\n      rad += TWO_THIRDS_PI;\n      ctx.lineTo(x + Math.sin(rad) * radius, y - Math.cos(rad) * radius);\n      rad += TWO_THIRDS_PI;\n      ctx.lineTo(x + Math.sin(rad) * radius, y - Math.cos(rad) * radius);\n      ctx.closePath();\n      break;\n    case 'rectRounded':\n      cornerRadius = radius * 0.516;\n      size = radius - cornerRadius;\n      xOffset = Math.cos(rad + QUARTER_PI) * size;\n      yOffset = Math.sin(rad + QUARTER_PI) * size;\n      ctx.arc(x - xOffset, y - yOffset, cornerRadius, rad - PI, rad - HALF_PI);\n      ctx.arc(x + yOffset, y - xOffset, cornerRadius, rad - HALF_PI, rad);\n      ctx.arc(x + xOffset, y + yOffset, cornerRadius, rad, rad + HALF_PI);\n      ctx.arc(x - yOffset, y + xOffset, cornerRadius, rad + HALF_PI, rad + PI);\n      ctx.closePath();\n      break;\n    case 'rect':\n      if (!rotation) {\n        size = Math.SQRT1_2 * radius;\n        width = w ? w / 2 : size;\n        ctx.rect(x - width, y - size, 2 * width, 2 * size);\n        break;\n      }\n      rad += QUARTER_PI;\n    case 'rectRot':\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      ctx.moveTo(x - xOffset, y - yOffset);\n      ctx.lineTo(x + yOffset, y - xOffset);\n      ctx.lineTo(x + xOffset, y + yOffset);\n      ctx.lineTo(x - yOffset, y + xOffset);\n      ctx.closePath();\n      break;\n    case 'crossRot':\n      rad += QUARTER_PI;\n    case 'cross':\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      ctx.moveTo(x - xOffset, y - yOffset);\n      ctx.lineTo(x + xOffset, y + yOffset);\n      ctx.moveTo(x + yOffset, y - xOffset);\n      ctx.lineTo(x - yOffset, y + xOffset);\n      break;\n    case 'star':\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      ctx.moveTo(x - xOffset, y - yOffset);\n      ctx.lineTo(x + xOffset, y + yOffset);\n      ctx.moveTo(x + yOffset, y - xOffset);\n      ctx.lineTo(x - yOffset, y + xOffset);\n      rad += QUARTER_PI;\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      ctx.moveTo(x - xOffset, y - yOffset);\n      ctx.lineTo(x + xOffset, y + yOffset);\n      ctx.moveTo(x + yOffset, y - xOffset);\n      ctx.lineTo(x - yOffset, y + xOffset);\n      break;\n    case 'line':\n      xOffset = w ? w / 2 : Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      ctx.moveTo(x - xOffset, y - yOffset);\n      ctx.lineTo(x + xOffset, y + yOffset);\n      break;\n    case 'dash':\n      ctx.moveTo(x, y);\n      ctx.lineTo(x + Math.cos(rad) * radius, y + Math.sin(rad) * radius);\n      break;\n  }\n  ctx.fill();\n  if (options.borderWidth > 0) {\n    ctx.stroke();\n  }\n}\nfunction _isPointInArea(point, area, margin) {\n  margin = margin || 0.5;\n  return !area || point && point.x > area.left - margin && point.x < area.right + margin && point.y > area.top - margin && point.y < area.bottom + margin;\n}\nfunction clipArea(ctx, area) {\n  ctx.save();\n  ctx.beginPath();\n  ctx.rect(area.left, area.top, area.right - area.left, area.bottom - area.top);\n  ctx.clip();\n}\nfunction unclipArea(ctx) {\n  ctx.restore();\n}\nfunction _steppedLineTo(ctx, previous, target, flip, mode) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n  if (mode === 'middle') {\n    const midpoint = (previous.x + target.x) / 2.0;\n    ctx.lineTo(midpoint, previous.y);\n    ctx.lineTo(midpoint, target.y);\n  } else if (mode === 'after' !== !!flip) {\n    ctx.lineTo(previous.x, target.y);\n  } else {\n    ctx.lineTo(target.x, previous.y);\n  }\n  ctx.lineTo(target.x, target.y);\n}\nfunction _bezierCurveTo(ctx, previous, target, flip) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n  ctx.bezierCurveTo(flip ? previous.cp1x : previous.cp2x, flip ? previous.cp1y : previous.cp2y, flip ? target.cp2x : target.cp1x, flip ? target.cp2y : target.cp1y, target.x, target.y);\n}\nfunction renderText(ctx, text, x, y, font, opts = {}) {\n  const lines = isArray(text) ? text : [text];\n  const stroke = opts.strokeWidth > 0 && opts.strokeColor !== '';\n  let i, line;\n  ctx.save();\n  ctx.font = font.string;\n  setRenderOpts(ctx, opts);\n  for (i = 0; i < lines.length; ++i) {\n    line = lines[i];\n    if (stroke) {\n      if (opts.strokeColor) {\n        ctx.strokeStyle = opts.strokeColor;\n      }\n      if (!isNullOrUndef(opts.strokeWidth)) {\n        ctx.lineWidth = opts.strokeWidth;\n      }\n      ctx.strokeText(line, x, y, opts.maxWidth);\n    }\n    ctx.fillText(line, x, y, opts.maxWidth);\n    decorateText(ctx, x, y, line, opts);\n    y += font.lineHeight;\n  }\n  ctx.restore();\n}\nfunction setRenderOpts(ctx, opts) {\n  if (opts.translation) {\n    ctx.translate(opts.translation[0], opts.translation[1]);\n  }\n  if (!isNullOrUndef(opts.rotation)) {\n    ctx.rotate(opts.rotation);\n  }\n  if (opts.color) {\n    ctx.fillStyle = opts.color;\n  }\n  if (opts.textAlign) {\n    ctx.textAlign = opts.textAlign;\n  }\n  if (opts.textBaseline) {\n    ctx.textBaseline = opts.textBaseline;\n  }\n}\nfunction decorateText(ctx, x, y, line, opts) {\n  if (opts.strikethrough || opts.underline) {\n    const metrics = ctx.measureText(line);\n    const left = x - metrics.actualBoundingBoxLeft;\n    const right = x + metrics.actualBoundingBoxRight;\n    const top = y - metrics.actualBoundingBoxAscent;\n    const bottom = y + metrics.actualBoundingBoxDescent;\n    const yDecoration = opts.strikethrough ? (top + bottom) / 2 : bottom;\n    ctx.strokeStyle = ctx.fillStyle;\n    ctx.beginPath();\n    ctx.lineWidth = opts.decorationWidth || 2;\n    ctx.moveTo(left, yDecoration);\n    ctx.lineTo(right, yDecoration);\n    ctx.stroke();\n  }\n}\nfunction addRoundedRectPath(ctx, rect) {\n  const {\n    x,\n    y,\n    w,\n    h,\n    radius\n  } = rect;\n  ctx.arc(x + radius.topLeft, y + radius.topLeft, radius.topLeft, -HALF_PI, PI, true);\n  ctx.lineTo(x, y + h - radius.bottomLeft);\n  ctx.arc(x + radius.bottomLeft, y + h - radius.bottomLeft, radius.bottomLeft, PI, HALF_PI, true);\n  ctx.lineTo(x + w - radius.bottomRight, y + h);\n  ctx.arc(x + w - radius.bottomRight, y + h - radius.bottomRight, radius.bottomRight, HALF_PI, 0, true);\n  ctx.lineTo(x + w, y + radius.topRight);\n  ctx.arc(x + w - radius.topRight, y + radius.topRight, radius.topRight, 0, -HALF_PI, true);\n  ctx.lineTo(x + radius.topLeft, y);\n}\nconst LINE_HEIGHT = new RegExp(/^(normal|(\\d+(?:\\.\\d+)?)(px|em|%)?)$/);\nconst FONT_STYLE = new RegExp(/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/);\nfunction toLineHeight(value, size) {\n  const matches = ('' + value).match(LINE_HEIGHT);\n  if (!matches || matches[1] === 'normal') {\n    return size * 1.2;\n  }\n  value = +matches[2];\n  switch (matches[3]) {\n    case 'px':\n      return value;\n    case '%':\n      value /= 100;\n      break;\n  }\n  return size * value;\n}\nconst numberOrZero = v => +v || 0;\nfunction _readValueToProps(value, props) {\n  const ret = {};\n  const objProps = isObject(props);\n  const keys = objProps ? Object.keys(props) : props;\n  const read = isObject(value) ? objProps ? prop => valueOrDefault(value[prop], value[props[prop]]) : prop => value[prop] : () => value;\n  for (const prop of keys) {\n    ret[prop] = numberOrZero(read(prop));\n  }\n  return ret;\n}\nfunction toTRBL(value) {\n  return _readValueToProps(value, {\n    top: 'y',\n    right: 'x',\n    bottom: 'y',\n    left: 'x'\n  });\n}\nfunction toTRBLCorners(value) {\n  return _readValueToProps(value, ['topLeft', 'topRight', 'bottomLeft', 'bottomRight']);\n}\nfunction toPadding(value) {\n  const obj = toTRBL(value);\n  obj.width = obj.left + obj.right;\n  obj.height = obj.top + obj.bottom;\n  return obj;\n}\nfunction toFont(options, fallback) {\n  options = options || {};\n  fallback = fallback || defaults.font;\n  let size = valueOrDefault(options.size, fallback.size);\n  if (typeof size === 'string') {\n    size = parseInt(size, 10);\n  }\n  let style = valueOrDefault(options.style, fallback.style);\n  if (style && !('' + style).match(FONT_STYLE)) {\n    console.warn('Invalid font style specified: \"' + style + '\"');\n    style = '';\n  }\n  const font = {\n    family: valueOrDefault(options.family, fallback.family),\n    lineHeight: toLineHeight(valueOrDefault(options.lineHeight, fallback.lineHeight), size),\n    size,\n    style,\n    weight: valueOrDefault(options.weight, fallback.weight),\n    string: ''\n  };\n  font.string = toFontString(font);\n  return font;\n}\nfunction resolve(inputs, context, index, info) {\n  let cacheable = true;\n  let i, ilen, value;\n  for (i = 0, ilen = inputs.length; i < ilen; ++i) {\n    value = inputs[i];\n    if (value === undefined) {\n      continue;\n    }\n    if (context !== undefined && typeof value === 'function') {\n      value = value(context);\n      cacheable = false;\n    }\n    if (index !== undefined && isArray(value)) {\n      value = value[index % value.length];\n      cacheable = false;\n    }\n    if (value !== undefined) {\n      if (info && !cacheable) {\n        info.cacheable = false;\n      }\n      return value;\n    }\n  }\n}\nfunction _addGrace(minmax, grace, beginAtZero) {\n  const {\n    min,\n    max\n  } = minmax;\n  const change = toDimension(grace, (max - min) / 2);\n  const keepZero = (value, add) => beginAtZero && value === 0 ? 0 : value + add;\n  return {\n    min: keepZero(min, -Math.abs(change)),\n    max: keepZero(max, change)\n  };\n}\nfunction createContext(parentContext, context) {\n  return Object.assign(Object.create(parentContext), context);\n}\nfunction _createResolver(scopes, prefixes = [''], rootScopes = scopes, fallback, getTarget = () => scopes[0]) {\n  if (!defined(fallback)) {\n    fallback = _resolve('_fallback', scopes);\n  }\n  const cache = {\n    [Symbol.toStringTag]: 'Object',\n    _cacheable: true,\n    _scopes: scopes,\n    _rootScopes: rootScopes,\n    _fallback: fallback,\n    _getTarget: getTarget,\n    override: scope => _createResolver([scope, ...scopes], prefixes, rootScopes, fallback)\n  };\n  return new Proxy(cache, {\n    deleteProperty(target, prop) {\n      delete target[prop];\n      delete target._keys;\n      delete scopes[0][prop];\n      return true;\n    },\n    get(target, prop) {\n      return _cached(target, prop, () => _resolveWithPrefixes(prop, prefixes, scopes, target));\n    },\n    getOwnPropertyDescriptor(target, prop) {\n      return Reflect.getOwnPropertyDescriptor(target._scopes[0], prop);\n    },\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(scopes[0]);\n    },\n    has(target, prop) {\n      return getKeysFromAllScopes(target).includes(prop);\n    },\n    ownKeys(target) {\n      return getKeysFromAllScopes(target);\n    },\n    set(target, prop, value) {\n      const storage = target._storage || (target._storage = getTarget());\n      target[prop] = storage[prop] = value;\n      delete target._keys;\n      return true;\n    }\n  });\n}\nfunction _attachContext(proxy, context, subProxy, descriptorDefaults) {\n  const cache = {\n    _cacheable: false,\n    _proxy: proxy,\n    _context: context,\n    _subProxy: subProxy,\n    _stack: new Set(),\n    _descriptors: _descriptors(proxy, descriptorDefaults),\n    setContext: ctx => _attachContext(proxy, ctx, subProxy, descriptorDefaults),\n    override: scope => _attachContext(proxy.override(scope), context, subProxy, descriptorDefaults)\n  };\n  return new Proxy(cache, {\n    deleteProperty(target, prop) {\n      delete target[prop];\n      delete proxy[prop];\n      return true;\n    },\n    get(target, prop, receiver) {\n      return _cached(target, prop, () => _resolveWithContext(target, prop, receiver));\n    },\n    getOwnPropertyDescriptor(target, prop) {\n      return target._descriptors.allKeys ? Reflect.has(proxy, prop) ? {\n        enumerable: true,\n        configurable: true\n      } : undefined : Reflect.getOwnPropertyDescriptor(proxy, prop);\n    },\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(proxy);\n    },\n    has(target, prop) {\n      return Reflect.has(proxy, prop);\n    },\n    ownKeys() {\n      return Reflect.ownKeys(proxy);\n    },\n    set(target, prop, value) {\n      proxy[prop] = value;\n      delete target[prop];\n      return true;\n    }\n  });\n}\nfunction _descriptors(proxy, defaults = {\n  scriptable: true,\n  indexable: true\n}) {\n  const {\n    _scriptable = defaults.scriptable,\n    _indexable = defaults.indexable,\n    _allKeys = defaults.allKeys\n  } = proxy;\n  return {\n    allKeys: _allKeys,\n    scriptable: _scriptable,\n    indexable: _indexable,\n    isScriptable: isFunction(_scriptable) ? _scriptable : () => _scriptable,\n    isIndexable: isFunction(_indexable) ? _indexable : () => _indexable\n  };\n}\nconst readKey = (prefix, name) => prefix ? prefix + _capitalize(name) : name;\nconst needsSubResolver = (prop, value) => isObject(value) && prop !== 'adapters' && (Object.getPrototypeOf(value) === null || value.constructor === Object);\nfunction _cached(target, prop, resolve) {\n  if (Object.prototype.hasOwnProperty.call(target, prop)) {\n    return target[prop];\n  }\n  const value = resolve();\n  target[prop] = value;\n  return value;\n}\nfunction _resolveWithContext(target, prop, receiver) {\n  const {\n    _proxy,\n    _context,\n    _subProxy,\n    _descriptors: descriptors\n  } = target;\n  let value = _proxy[prop];\n  if (isFunction(value) && descriptors.isScriptable(prop)) {\n    value = _resolveScriptable(prop, value, target, receiver);\n  }\n  if (isArray(value) && value.length) {\n    value = _resolveArray(prop, value, target, descriptors.isIndexable);\n  }\n  if (needsSubResolver(prop, value)) {\n    value = _attachContext(value, _context, _subProxy && _subProxy[prop], descriptors);\n  }\n  return value;\n}\nfunction _resolveScriptable(prop, value, target, receiver) {\n  const {\n    _proxy,\n    _context,\n    _subProxy,\n    _stack\n  } = target;\n  if (_stack.has(prop)) {\n    throw new Error('Recursion detected: ' + Array.from(_stack).join('->') + '->' + prop);\n  }\n  _stack.add(prop);\n  value = value(_context, _subProxy || receiver);\n  _stack.delete(prop);\n  if (needsSubResolver(prop, value)) {\n    value = createSubResolver(_proxy._scopes, _proxy, prop, value);\n  }\n  return value;\n}\nfunction _resolveArray(prop, value, target, isIndexable) {\n  const {\n    _proxy,\n    _context,\n    _subProxy,\n    _descriptors: descriptors\n  } = target;\n  if (defined(_context.index) && isIndexable(prop)) {\n    value = value[_context.index % value.length];\n  } else if (isObject(value[0])) {\n    const arr = value;\n    const scopes = _proxy._scopes.filter(s => s !== arr);\n    value = [];\n    for (const item of arr) {\n      const resolver = createSubResolver(scopes, _proxy, prop, item);\n      value.push(_attachContext(resolver, _context, _subProxy && _subProxy[prop], descriptors));\n    }\n  }\n  return value;\n}\nfunction resolveFallback(fallback, prop, value) {\n  return isFunction(fallback) ? fallback(prop, value) : fallback;\n}\nconst getScope = (key, parent) => key === true ? parent : typeof key === 'string' ? resolveObjectKey(parent, key) : undefined;\nfunction addScopes(set, parentScopes, key, parentFallback, value) {\n  for (const parent of parentScopes) {\n    const scope = getScope(key, parent);\n    if (scope) {\n      set.add(scope);\n      const fallback = resolveFallback(scope._fallback, key, value);\n      if (defined(fallback) && fallback !== key && fallback !== parentFallback) {\n        return fallback;\n      }\n    } else if (scope === false && defined(parentFallback) && key !== parentFallback) {\n      return null;\n    }\n  }\n  return false;\n}\nfunction createSubResolver(parentScopes, resolver, prop, value) {\n  const rootScopes = resolver._rootScopes;\n  const fallback = resolveFallback(resolver._fallback, prop, value);\n  const allScopes = [...parentScopes, ...rootScopes];\n  const set = new Set();\n  set.add(value);\n  let key = addScopesFromKey(set, allScopes, prop, fallback || prop, value);\n  if (key === null) {\n    return false;\n  }\n  if (defined(fallback) && fallback !== prop) {\n    key = addScopesFromKey(set, allScopes, fallback, key, value);\n    if (key === null) {\n      return false;\n    }\n  }\n  return _createResolver(Array.from(set), [''], rootScopes, fallback, () => subGetTarget(resolver, prop, value));\n}\nfunction addScopesFromKey(set, allScopes, key, fallback, item) {\n  while (key) {\n    key = addScopes(set, allScopes, key, fallback, item);\n  }\n  return key;\n}\nfunction subGetTarget(resolver, prop, value) {\n  const parent = resolver._getTarget();\n  if (!(prop in parent)) {\n    parent[prop] = {};\n  }\n  const target = parent[prop];\n  if (isArray(target) && isObject(value)) {\n    return value;\n  }\n  return target;\n}\nfunction _resolveWithPrefixes(prop, prefixes, scopes, proxy) {\n  let value;\n  for (const prefix of prefixes) {\n    value = _resolve(readKey(prefix, prop), scopes);\n    if (defined(value)) {\n      return needsSubResolver(prop, value) ? createSubResolver(scopes, proxy, prop, value) : value;\n    }\n  }\n}\nfunction _resolve(key, scopes) {\n  for (const scope of scopes) {\n    if (!scope) {\n      continue;\n    }\n    const value = scope[key];\n    if (defined(value)) {\n      return value;\n    }\n  }\n}\nfunction getKeysFromAllScopes(target) {\n  let keys = target._keys;\n  if (!keys) {\n    keys = target._keys = resolveKeysFromAllScopes(target._scopes);\n  }\n  return keys;\n}\nfunction resolveKeysFromAllScopes(scopes) {\n  const set = new Set();\n  for (const scope of scopes) {\n    for (const key of Object.keys(scope).filter(k => !k.startsWith('_'))) {\n      set.add(key);\n    }\n  }\n  return Array.from(set);\n}\nfunction _parseObjectDataRadialScale(meta, data, start, count) {\n  const {\n    iScale\n  } = meta;\n  const {\n    key = 'r'\n  } = this._parsing;\n  const parsed = new Array(count);\n  let i, ilen, index, item;\n  for (i = 0, ilen = count; i < ilen; ++i) {\n    index = i + start;\n    item = data[index];\n    parsed[i] = {\n      r: iScale.parse(resolveObjectKey(item, key), index)\n    };\n  }\n  return parsed;\n}\nconst EPSILON = Number.EPSILON || 1e-14;\nconst getPoint = (points, i) => i < points.length && !points[i].skip && points[i];\nconst getValueAxis = indexAxis => indexAxis === 'x' ? 'y' : 'x';\nfunction splineCurve(firstPoint, middlePoint, afterPoint, t) {\n  const previous = firstPoint.skip ? middlePoint : firstPoint;\n  const current = middlePoint;\n  const next = afterPoint.skip ? middlePoint : afterPoint;\n  const d01 = distanceBetweenPoints(current, previous);\n  const d12 = distanceBetweenPoints(next, current);\n  let s01 = d01 / (d01 + d12);\n  let s12 = d12 / (d01 + d12);\n  s01 = isNaN(s01) ? 0 : s01;\n  s12 = isNaN(s12) ? 0 : s12;\n  const fa = t * s01;\n  const fb = t * s12;\n  return {\n    previous: {\n      x: current.x - fa * (next.x - previous.x),\n      y: current.y - fa * (next.y - previous.y)\n    },\n    next: {\n      x: current.x + fb * (next.x - previous.x),\n      y: current.y + fb * (next.y - previous.y)\n    }\n  };\n}\nfunction monotoneAdjust(points, deltaK, mK) {\n  const pointsLen = points.length;\n  let alphaK, betaK, tauK, squaredMagnitude, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n  for (let i = 0; i < pointsLen - 1; ++i) {\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent || !pointAfter) {\n      continue;\n    }\n    if (almostEquals(deltaK[i], 0, EPSILON)) {\n      mK[i] = mK[i + 1] = 0;\n      continue;\n    }\n    alphaK = mK[i] / deltaK[i];\n    betaK = mK[i + 1] / deltaK[i];\n    squaredMagnitude = Math.pow(alphaK, 2) + Math.pow(betaK, 2);\n    if (squaredMagnitude <= 9) {\n      continue;\n    }\n    tauK = 3 / Math.sqrt(squaredMagnitude);\n    mK[i] = alphaK * tauK * deltaK[i];\n    mK[i + 1] = betaK * tauK * deltaK[i];\n  }\n}\nfunction monotoneCompute(points, mK, indexAxis = 'x') {\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  let delta, pointBefore, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n  for (let i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent) {\n      continue;\n    }\n    const iPixel = pointCurrent[indexAxis];\n    const vPixel = pointCurrent[valueAxis];\n    if (pointBefore) {\n      delta = (iPixel - pointBefore[indexAxis]) / 3;\n      pointCurrent[`cp1${indexAxis}`] = iPixel - delta;\n      pointCurrent[`cp1${valueAxis}`] = vPixel - delta * mK[i];\n    }\n    if (pointAfter) {\n      delta = (pointAfter[indexAxis] - iPixel) / 3;\n      pointCurrent[`cp2${indexAxis}`] = iPixel + delta;\n      pointCurrent[`cp2${valueAxis}`] = vPixel + delta * mK[i];\n    }\n  }\n}\nfunction splineCurveMonotone(points, indexAxis = 'x') {\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  const deltaK = Array(pointsLen).fill(0);\n  const mK = Array(pointsLen);\n  let i, pointBefore, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n  for (i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent) {\n      continue;\n    }\n    if (pointAfter) {\n      const slopeDelta = pointAfter[indexAxis] - pointCurrent[indexAxis];\n      deltaK[i] = slopeDelta !== 0 ? (pointAfter[valueAxis] - pointCurrent[valueAxis]) / slopeDelta : 0;\n    }\n    mK[i] = !pointBefore ? deltaK[i] : !pointAfter ? deltaK[i - 1] : sign(deltaK[i - 1]) !== sign(deltaK[i]) ? 0 : (deltaK[i - 1] + deltaK[i]) / 2;\n  }\n  monotoneAdjust(points, deltaK, mK);\n  monotoneCompute(points, mK, indexAxis);\n}\nfunction capControlPoint(pt, min, max) {\n  return Math.max(Math.min(pt, max), min);\n}\nfunction capBezierPoints(points, area) {\n  let i, ilen, point, inArea, inAreaPrev;\n  let inAreaNext = _isPointInArea(points[0], area);\n  for (i = 0, ilen = points.length; i < ilen; ++i) {\n    inAreaPrev = inArea;\n    inArea = inAreaNext;\n    inAreaNext = i < ilen - 1 && _isPointInArea(points[i + 1], area);\n    if (!inArea) {\n      continue;\n    }\n    point = points[i];\n    if (inAreaPrev) {\n      point.cp1x = capControlPoint(point.cp1x, area.left, area.right);\n      point.cp1y = capControlPoint(point.cp1y, area.top, area.bottom);\n    }\n    if (inAreaNext) {\n      point.cp2x = capControlPoint(point.cp2x, area.left, area.right);\n      point.cp2y = capControlPoint(point.cp2y, area.top, area.bottom);\n    }\n  }\n}\nfunction _updateBezierControlPoints(points, options, area, loop, indexAxis) {\n  let i, ilen, point, controlPoints;\n  if (options.spanGaps) {\n    points = points.filter(pt => !pt.skip);\n  }\n  if (options.cubicInterpolationMode === 'monotone') {\n    splineCurveMonotone(points, indexAxis);\n  } else {\n    let prev = loop ? points[points.length - 1] : points[0];\n    for (i = 0, ilen = points.length; i < ilen; ++i) {\n      point = points[i];\n      controlPoints = splineCurve(prev, point, points[Math.min(i + 1, ilen - (loop ? 0 : 1)) % ilen], options.tension);\n      point.cp1x = controlPoints.previous.x;\n      point.cp1y = controlPoints.previous.y;\n      point.cp2x = controlPoints.next.x;\n      point.cp2y = controlPoints.next.y;\n      prev = point;\n    }\n  }\n  if (options.capBezierPoints) {\n    capBezierPoints(points, area);\n  }\n}\nfunction _isDomSupported() {\n  return typeof window !== 'undefined' && typeof document !== 'undefined';\n}\nfunction _getParentNode(domNode) {\n  let parent = domNode.parentNode;\n  if (parent && parent.toString() === '[object ShadowRoot]') {\n    parent = parent.host;\n  }\n  return parent;\n}\nfunction parseMaxStyle(styleValue, node, parentProperty) {\n  let valueInPixels;\n  if (typeof styleValue === 'string') {\n    valueInPixels = parseInt(styleValue, 10);\n    if (styleValue.indexOf('%') !== -1) {\n      valueInPixels = valueInPixels / 100 * node.parentNode[parentProperty];\n    }\n  } else {\n    valueInPixels = styleValue;\n  }\n  return valueInPixels;\n}\nconst getComputedStyle = element => window.getComputedStyle(element, null);\nfunction getStyle(el, property) {\n  return getComputedStyle(el).getPropertyValue(property);\n}\nconst positions = ['top', 'right', 'bottom', 'left'];\nfunction getPositionedStyle(styles, style, suffix) {\n  const result = {};\n  suffix = suffix ? '-' + suffix : '';\n  for (let i = 0; i < 4; i++) {\n    const pos = positions[i];\n    result[pos] = parseFloat(styles[style + '-' + pos + suffix]) || 0;\n  }\n  result.width = result.left + result.right;\n  result.height = result.top + result.bottom;\n  return result;\n}\nconst useOffsetPos = (x, y, target) => (x > 0 || y > 0) && (!target || !target.shadowRoot);\nfunction getCanvasPosition(e, canvas) {\n  const touches = e.touches;\n  const source = touches && touches.length ? touches[0] : e;\n  const {\n    offsetX,\n    offsetY\n  } = source;\n  let box = false;\n  let x, y;\n  if (useOffsetPos(offsetX, offsetY, e.target)) {\n    x = offsetX;\n    y = offsetY;\n  } else {\n    const rect = canvas.getBoundingClientRect();\n    x = source.clientX - rect.left;\n    y = source.clientY - rect.top;\n    box = true;\n  }\n  return {\n    x,\n    y,\n    box\n  };\n}\nfunction getRelativePosition(evt, chart) {\n  if ('native' in evt) {\n    return evt;\n  }\n  const {\n    canvas,\n    currentDevicePixelRatio\n  } = chart;\n  const style = getComputedStyle(canvas);\n  const borderBox = style.boxSizing === 'border-box';\n  const paddings = getPositionedStyle(style, 'padding');\n  const borders = getPositionedStyle(style, 'border', 'width');\n  const {\n    x,\n    y,\n    box\n  } = getCanvasPosition(evt, canvas);\n  const xOffset = paddings.left + (box && borders.left);\n  const yOffset = paddings.top + (box && borders.top);\n  let {\n    width,\n    height\n  } = chart;\n  if (borderBox) {\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n  return {\n    x: Math.round((x - xOffset) / width * canvas.width / currentDevicePixelRatio),\n    y: Math.round((y - yOffset) / height * canvas.height / currentDevicePixelRatio)\n  };\n}\nfunction getContainerSize(canvas, width, height) {\n  let maxWidth, maxHeight;\n  if (width === undefined || height === undefined) {\n    const container = _getParentNode(canvas);\n    if (!container) {\n      width = canvas.clientWidth;\n      height = canvas.clientHeight;\n    } else {\n      const rect = container.getBoundingClientRect();\n      const containerStyle = getComputedStyle(container);\n      const containerBorder = getPositionedStyle(containerStyle, 'border', 'width');\n      const containerPadding = getPositionedStyle(containerStyle, 'padding');\n      width = rect.width - containerPadding.width - containerBorder.width;\n      height = rect.height - containerPadding.height - containerBorder.height;\n      maxWidth = parseMaxStyle(containerStyle.maxWidth, container, 'clientWidth');\n      maxHeight = parseMaxStyle(containerStyle.maxHeight, container, 'clientHeight');\n    }\n  }\n  return {\n    width,\n    height,\n    maxWidth: maxWidth || INFINITY,\n    maxHeight: maxHeight || INFINITY\n  };\n}\nconst round1 = v => Math.round(v * 10) / 10;\nfunction getMaximumSize(canvas, bbWidth, bbHeight, aspectRatio) {\n  const style = getComputedStyle(canvas);\n  const margins = getPositionedStyle(style, 'margin');\n  const maxWidth = parseMaxStyle(style.maxWidth, canvas, 'clientWidth') || INFINITY;\n  const maxHeight = parseMaxStyle(style.maxHeight, canvas, 'clientHeight') || INFINITY;\n  const containerSize = getContainerSize(canvas, bbWidth, bbHeight);\n  let {\n    width,\n    height\n  } = containerSize;\n  if (style.boxSizing === 'content-box') {\n    const borders = getPositionedStyle(style, 'border', 'width');\n    const paddings = getPositionedStyle(style, 'padding');\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n  width = Math.max(0, width - margins.width);\n  height = Math.max(0, aspectRatio ? Math.floor(width / aspectRatio) : height - margins.height);\n  width = round1(Math.min(width, maxWidth, containerSize.maxWidth));\n  height = round1(Math.min(height, maxHeight, containerSize.maxHeight));\n  if (width && !height) {\n    height = round1(width / 2);\n  }\n  return {\n    width,\n    height\n  };\n}\nfunction retinaScale(chart, forceRatio, forceStyle) {\n  const pixelRatio = forceRatio || 1;\n  const deviceHeight = Math.floor(chart.height * pixelRatio);\n  const deviceWidth = Math.floor(chart.width * pixelRatio);\n  chart.height = deviceHeight / pixelRatio;\n  chart.width = deviceWidth / pixelRatio;\n  const canvas = chart.canvas;\n  if (canvas.style && (forceStyle || !canvas.style.height && !canvas.style.width)) {\n    canvas.style.height = `${chart.height}px`;\n    canvas.style.width = `${chart.width}px`;\n  }\n  if (chart.currentDevicePixelRatio !== pixelRatio || canvas.height !== deviceHeight || canvas.width !== deviceWidth) {\n    chart.currentDevicePixelRatio = pixelRatio;\n    canvas.height = deviceHeight;\n    canvas.width = deviceWidth;\n    chart.ctx.setTransform(pixelRatio, 0, 0, pixelRatio, 0, 0);\n    return true;\n  }\n  return false;\n}\nconst supportsEventListenerOptions = function () {\n  let passiveSupported = false;\n  try {\n    const options = {\n      get passive() {\n        passiveSupported = true;\n        return false;\n      }\n    };\n    window.addEventListener('test', null, options);\n    window.removeEventListener('test', null, options);\n  } catch (e) {}\n  return passiveSupported;\n}();\nfunction readUsedSize(element, property) {\n  const value = getStyle(element, property);\n  const matches = value && value.match(/^(\\d+)(\\.\\d+)?px$/);\n  return matches ? +matches[1] : undefined;\n}\nfunction _pointInLine(p1, p2, t, mode) {\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: p1.y + t * (p2.y - p1.y)\n  };\n}\nfunction _steppedInterpolation(p1, p2, t, mode) {\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: mode === 'middle' ? t < 0.5 ? p1.y : p2.y : mode === 'after' ? t < 1 ? p1.y : p2.y : t > 0 ? p2.y : p1.y\n  };\n}\nfunction _bezierInterpolation(p1, p2, t, mode) {\n  const cp1 = {\n    x: p1.cp2x,\n    y: p1.cp2y\n  };\n  const cp2 = {\n    x: p2.cp1x,\n    y: p2.cp1y\n  };\n  const a = _pointInLine(p1, cp1, t);\n  const b = _pointInLine(cp1, cp2, t);\n  const c = _pointInLine(cp2, p2, t);\n  const d = _pointInLine(a, b, t);\n  const e = _pointInLine(b, c, t);\n  return _pointInLine(d, e, t);\n}\nconst intlCache = new Map();\nfunction getNumberFormat(locale, options) {\n  options = options || {};\n  const cacheKey = locale + JSON.stringify(options);\n  let formatter = intlCache.get(cacheKey);\n  if (!formatter) {\n    formatter = new Intl.NumberFormat(locale, options);\n    intlCache.set(cacheKey, formatter);\n  }\n  return formatter;\n}\nfunction formatNumber(num, locale, options) {\n  return getNumberFormat(locale, options).format(num);\n}\nconst getRightToLeftAdapter = function (rectX, width) {\n  return {\n    x(x) {\n      return rectX + rectX + width - x;\n    },\n    setWidth(w) {\n      width = w;\n    },\n    textAlign(align) {\n      if (align === 'center') {\n        return align;\n      }\n      return align === 'right' ? 'left' : 'right';\n    },\n    xPlus(x, value) {\n      return x - value;\n    },\n    leftForLtr(x, itemWidth) {\n      return x - itemWidth;\n    }\n  };\n};\nconst getLeftToRightAdapter = function () {\n  return {\n    x(x) {\n      return x;\n    },\n    setWidth(w) {},\n    textAlign(align) {\n      return align;\n    },\n    xPlus(x, value) {\n      return x + value;\n    },\n    leftForLtr(x, _itemWidth) {\n      return x;\n    }\n  };\n};\nfunction getRtlAdapter(rtl, rectX, width) {\n  return rtl ? getRightToLeftAdapter(rectX, width) : getLeftToRightAdapter();\n}\nfunction overrideTextDirection(ctx, direction) {\n  let style, original;\n  if (direction === 'ltr' || direction === 'rtl') {\n    style = ctx.canvas.style;\n    original = [style.getPropertyValue('direction'), style.getPropertyPriority('direction')];\n    style.setProperty('direction', direction, 'important');\n    ctx.prevTextDirection = original;\n  }\n}\nfunction restoreTextDirection(ctx, original) {\n  if (original !== undefined) {\n    delete ctx.prevTextDirection;\n    ctx.canvas.style.setProperty('direction', original[0], original[1]);\n  }\n}\nfunction propertyFn(property) {\n  if (property === 'angle') {\n    return {\n      between: _angleBetween,\n      compare: _angleDiff,\n      normalize: _normalizeAngle\n    };\n  }\n  return {\n    between: _isBetween,\n    compare: (a, b) => a - b,\n    normalize: x => x\n  };\n}\nfunction normalizeSegment({\n  start,\n  end,\n  count,\n  loop,\n  style\n}) {\n  return {\n    start: start % count,\n    end: end % count,\n    loop: loop && (end - start + 1) % count === 0,\n    style\n  };\n}\nfunction getSegment(segment, points, bounds) {\n  const {\n    property,\n    start: startBound,\n    end: endBound\n  } = bounds;\n  const {\n    between,\n    normalize\n  } = propertyFn(property);\n  const count = points.length;\n  let {\n    start,\n    end,\n    loop\n  } = segment;\n  let i, ilen;\n  if (loop) {\n    start += count;\n    end += count;\n    for (i = 0, ilen = count; i < ilen; ++i) {\n      if (!between(normalize(points[start % count][property]), startBound, endBound)) {\n        break;\n      }\n      start--;\n      end--;\n    }\n    start %= count;\n    end %= count;\n  }\n  if (end < start) {\n    end += count;\n  }\n  return {\n    start,\n    end,\n    loop,\n    style: segment.style\n  };\n}\nfunction _boundSegment(segment, points, bounds) {\n  if (!bounds) {\n    return [segment];\n  }\n  const {\n    property,\n    start: startBound,\n    end: endBound\n  } = bounds;\n  const count = points.length;\n  const {\n    compare,\n    between,\n    normalize\n  } = propertyFn(property);\n  const {\n    start,\n    end,\n    loop,\n    style\n  } = getSegment(segment, points, bounds);\n  const result = [];\n  let inside = false;\n  let subStart = null;\n  let value, point, prevValue;\n  const startIsBefore = () => between(startBound, prevValue, value) && compare(startBound, prevValue) !== 0;\n  const endIsBefore = () => compare(endBound, value) === 0 || between(endBound, prevValue, value);\n  const shouldStart = () => inside || startIsBefore();\n  const shouldStop = () => !inside || endIsBefore();\n  for (let i = start, prev = start; i <= end; ++i) {\n    point = points[i % count];\n    if (point.skip) {\n      continue;\n    }\n    value = normalize(point[property]);\n    if (value === prevValue) {\n      continue;\n    }\n    inside = between(value, startBound, endBound);\n    if (subStart === null && shouldStart()) {\n      subStart = compare(value, startBound) === 0 ? i : prev;\n    }\n    if (subStart !== null && shouldStop()) {\n      result.push(normalizeSegment({\n        start: subStart,\n        end: i,\n        loop,\n        count,\n        style\n      }));\n      subStart = null;\n    }\n    prev = i;\n    prevValue = value;\n  }\n  if (subStart !== null) {\n    result.push(normalizeSegment({\n      start: subStart,\n      end,\n      loop,\n      count,\n      style\n    }));\n  }\n  return result;\n}\nfunction _boundSegments(line, bounds) {\n  const result = [];\n  const segments = line.segments;\n  for (let i = 0; i < segments.length; i++) {\n    const sub = _boundSegment(segments[i], line.points, bounds);\n    if (sub.length) {\n      result.push(...sub);\n    }\n  }\n  return result;\n}\nfunction findStartAndEnd(points, count, loop, spanGaps) {\n  let start = 0;\n  let end = count - 1;\n  if (loop && !spanGaps) {\n    while (start < count && !points[start].skip) {\n      start++;\n    }\n  }\n  while (start < count && points[start].skip) {\n    start++;\n  }\n  start %= count;\n  if (loop) {\n    end += start;\n  }\n  while (end > start && points[end % count].skip) {\n    end--;\n  }\n  end %= count;\n  return {\n    start,\n    end\n  };\n}\nfunction solidSegments(points, start, max, loop) {\n  const count = points.length;\n  const result = [];\n  let last = start;\n  let prev = points[start];\n  let end;\n  for (end = start + 1; end <= max; ++end) {\n    const cur = points[end % count];\n    if (cur.skip || cur.stop) {\n      if (!prev.skip) {\n        loop = false;\n        result.push({\n          start: start % count,\n          end: (end - 1) % count,\n          loop\n        });\n        start = last = cur.stop ? end : null;\n      }\n    } else {\n      last = end;\n      if (prev.skip) {\n        start = end;\n      }\n    }\n    prev = cur;\n  }\n  if (last !== null) {\n    result.push({\n      start: start % count,\n      end: last % count,\n      loop\n    });\n  }\n  return result;\n}\nfunction _computeSegments(line, segmentOptions) {\n  const points = line.points;\n  const spanGaps = line.options.spanGaps;\n  const count = points.length;\n  if (!count) {\n    return [];\n  }\n  const loop = !!line._loop;\n  const {\n    start,\n    end\n  } = findStartAndEnd(points, count, loop, spanGaps);\n  if (spanGaps === true) {\n    return splitByStyles(line, [{\n      start,\n      end,\n      loop\n    }], points, segmentOptions);\n  }\n  const max = end < start ? end + count : end;\n  const completeLoop = !!line._fullLoop && start === 0 && end === count - 1;\n  return splitByStyles(line, solidSegments(points, start, max, completeLoop), points, segmentOptions);\n}\nfunction splitByStyles(line, segments, points, segmentOptions) {\n  if (!segmentOptions || !segmentOptions.setContext || !points) {\n    return segments;\n  }\n  return doSplitByStyles(line, segments, points, segmentOptions);\n}\nfunction doSplitByStyles(line, segments, points, segmentOptions) {\n  const chartContext = line._chart.getContext();\n  const baseStyle = readStyle(line.options);\n  const {\n    _datasetIndex: datasetIndex,\n    options: {\n      spanGaps\n    }\n  } = line;\n  const count = points.length;\n  const result = [];\n  let prevStyle = baseStyle;\n  let start = segments[0].start;\n  let i = start;\n  function addStyle(s, e, l, st) {\n    const dir = spanGaps ? -1 : 1;\n    if (s === e) {\n      return;\n    }\n    s += count;\n    while (points[s % count].skip) {\n      s -= dir;\n    }\n    while (points[e % count].skip) {\n      e += dir;\n    }\n    if (s % count !== e % count) {\n      result.push({\n        start: s % count,\n        end: e % count,\n        loop: l,\n        style: st\n      });\n      prevStyle = st;\n      start = e % count;\n    }\n  }\n  for (const segment of segments) {\n    start = spanGaps ? start : segment.start;\n    let prev = points[start % count];\n    let style;\n    for (i = start + 1; i <= segment.end; i++) {\n      const pt = points[i % count];\n      style = readStyle(segmentOptions.setContext(createContext(chartContext, {\n        type: 'segment',\n        p0: prev,\n        p1: pt,\n        p0DataIndex: (i - 1) % count,\n        p1DataIndex: i % count,\n        datasetIndex\n      })));\n      if (styleChanged(style, prevStyle)) {\n        addStyle(start, i - 1, segment.loop, prevStyle);\n      }\n      prev = pt;\n      prevStyle = style;\n    }\n    if (start < i - 1) {\n      addStyle(start, i - 1, segment.loop, prevStyle);\n    }\n  }\n  return result;\n}\nfunction readStyle(options) {\n  return {\n    backgroundColor: options.backgroundColor,\n    borderCapStyle: options.borderCapStyle,\n    borderDash: options.borderDash,\n    borderDashOffset: options.borderDashOffset,\n    borderJoinStyle: options.borderJoinStyle,\n    borderWidth: options.borderWidth,\n    borderColor: options.borderColor\n  };\n}\nfunction styleChanged(style, prevStyle) {\n  return prevStyle && JSON.stringify(style) !== JSON.stringify(prevStyle);\n}\nexport { _isPointInArea as $, _factorize as A, finiteOrDefault as B, callback as C, _addGrace as D, _limitValue as E, toDegrees as F, _measureText as G, HALF_PI as H, _int16Range as I, _alignPixel as J, toPadding as K, clipArea as L, renderText as M, unclipArea as N, toFont as O, PI as P, each as Q, _toLeftRightCenter as R, _alignStartEnd as S, TAU as T, overrides as U, merge as V, _capitalize as W, getRelativePosition as X, _rlookupByKey as Y, _lookupByKey as Z, _arrayUnique as _, resolve as a, toLineHeight as a$, getAngleFromPoint as a0, getMaximumSize as a1, _getParentNode as a2, readUsedSize as a3, throttled as a4, supportsEventListenerOptions as a5, _isDomSupported as a6, descriptors as a7, isFunction as a8, _attachContext as a9, getRtlAdapter as aA, overrideTextDirection as aB, _textX as aC, restoreTextDirection as aD, drawPointLegend as aE, noop as aF, distanceBetweenPoints as aG, _setMinAndMaxByKey as aH, niceNum as aI, almostWhole as aJ, almostEquals as aK, _decimalPlaces as aL, _longestText as aM, _filterBetween as aN, _lookup as aO, isPatternOrGradient as aP, getHoverColor as aQ, clone$1 as aR, _merger as aS, _mergerIf as aT, _deprecated as aU, _splitKey as aV, toFontString as aW, splineCurve as aX, splineCurveMonotone as aY, getStyle as aZ, fontString as a_, _createResolver as aa, _descriptors as ab, mergeIf as ac, uid as ad, debounce as ae, retinaScale as af, clearCanvas as ag, setsEqual as ah, _elementsEqual as ai, _isClickEvent as aj, _isBetween as ak, _readValueToProps as al, _updateBezierControlPoints as am, _computeSegments as an, _boundSegments as ao, _steppedInterpolation as ap, _bezierInterpolation as aq, _pointInLine as ar, _steppedLineTo as as, _bezierCurveTo as at, drawPoint as au, addRoundedRectPath as av, toTRBL as aw, toTRBLCorners as ax, _boundSegment as ay, _normalizeAngle as az, isArray as b, PITAU as b0, INFINITY as b1, RAD_PER_DEG as b2, QUARTER_PI as b3, TWO_THIRDS_PI as b4, _angleDiff as b5, color as c, defaults as d, effects as e, resolveObjectKey as f, isNumberFinite as g, createContext as h, isObject as i, defined as j, isNullOrUndef as k, listenArrayEvents as l, toPercentage as m, toDimension as n, formatNumber as o, _angleBetween as p, _getStartAndCountOfVisiblePoints as q, requestAnimFrame as r, sign as s, toRadians as t, unlistenArrayEvents as u, valueOrDefault as v, _scaleRangesChanged as w, isNumber as x, _parseObjectDataRadialScale as y, log10 as z };", "map": {"version": 3, "names": ["noop", "uid", "id", "isNullOrUndef", "value", "isArray", "Array", "type", "Object", "prototype", "toString", "call", "slice", "isObject", "isNumberFinite", "Number", "isFinite", "finiteOrDefault", "defaultValue", "valueOrDefault", "toPercentage", "dimension", "endsWith", "parseFloat", "toDimension", "callback", "fn", "args", "thisArg", "apply", "each", "loopable", "reverse", "i", "len", "keys", "length", "_elementsEqual", "a0", "a1", "ilen", "v0", "v1", "datasetIndex", "index", "clone$1", "source", "map", "target", "create", "klen", "k", "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "indexOf", "_merger", "options", "tval", "sval", "merge", "sources", "merger", "mergeIf", "_mergerIf", "hasOwnProperty", "_deprecated", "scope", "previous", "current", "undefined", "console", "warn", "keyResolvers", "v", "x", "o", "y", "resolveObjectKey", "obj", "resolver", "_getKeyResolver", "_splitKey", "parts", "split", "tmp", "part", "push", "_capitalize", "str", "char<PERSON>t", "toUpperCase", "defined", "isFunction", "setsEqual", "a", "b", "size", "item", "has", "_isClickEvent", "e", "PI", "Math", "TAU", "PITAU", "INFINITY", "POSITIVE_INFINITY", "RAD_PER_DEG", "HALF_PI", "QUARTER_PI", "TWO_THIRDS_PI", "log10", "sign", "niceNum", "range", "roundedRange", "round", "almostEquals", "niceRange", "pow", "floor", "fraction", "niceFraction", "_factorize", "result", "sqrt", "sort", "pop", "isNumber", "n", "isNaN", "epsilon", "abs", "almostWhole", "rounded", "_setMinAndMaxByKey", "array", "property", "min", "max", "toRadians", "degrees", "toDegrees", "radians", "_decimalPlaces", "p", "getAngleFromPoint", "centrePoint", "anglePoint", "distanceFromXCenter", "distanceFromYCenter", "radialDistanceFromCenter", "angle", "atan2", "distance", "distanceBetweenPoints", "pt1", "pt2", "_angleDiff", "_normalizeAngle", "_angleBetween", "start", "end", "sameAngleIsFullCircle", "s", "angleToStart", "angleToEnd", "startToAngle", "endToAngle", "_limitValue", "_int16Range", "_isBetween", "_lookup", "table", "cmp", "hi", "lo", "mid", "_lookup<PERSON><PERSON><PERSON><PERSON>", "last", "_rlookupByKey", "_filterBetween", "values", "arrayEvents", "listenArrayEvents", "listener", "_chartjs", "listeners", "defineProperty", "configurable", "enumerable", "for<PERSON>ach", "method", "base", "res", "object", "unlistenArrayEvents", "stub", "splice", "_arrayUnique", "items", "set", "Set", "add", "from", "fontString", "pixelSize", "fontStyle", "fontFamily", "requestAnimFrame", "window", "requestAnimationFrame", "throttled", "updateFn", "updateArgs", "ticking", "rest", "debounce", "delay", "timeout", "clearTimeout", "setTimeout", "_toLeftRightCenter", "align", "_alignStartEnd", "_textX", "left", "right", "rtl", "check", "_getStartAndCountOfVisiblePoints", "meta", "points", "animationsDisabled", "pointCount", "count", "_sorted", "iScale", "_parsed", "axis", "minDefined", "maxDefined", "getUserBounds", "getPixelForValue", "_scaleRangesChanged", "xScale", "yScale", "_scaleRanges", "newRang<PERSON>", "xmin", "xmax", "ymin", "ymax", "changed", "assign", "atEdge", "t", "elasticIn", "sin", "elasticOut", "effects", "linear", "easeInQuad", "easeOutQuad", "easeInOutQuad", "easeInCubic", "easeOutCubic", "easeInOutCubic", "easeInQuart", "easeOutQuart", "easeInOutQuart", "easeInQuint", "easeOutQuint", "easeInOutQuint", "easeInSine", "cos", "easeOutSine", "easeInOutSine", "easeInExpo", "easeOutExpo", "easeInOutExpo", "easeInCirc", "easeOutCirc", "easeInOutCirc", "easeInElastic", "easeOutElastic", "easeInOutElastic", "easeInBack", "easeOutBack", "easeInOutBack", "easeInBounce", "easeOutBounce", "m", "d", "easeInOutBounce", "lim", "l", "h", "p2b", "n2b", "b2n", "n2p", "map$1", "A", "B", "C", "D", "E", "F", "c", "f", "hex", "h1", "h2", "eq", "isShort", "r", "g", "hexParse", "ret", "alpha", "hexString", "HUE_RE", "hsl2rgbn", "hsv2rgbn", "hwb2rgbn", "w", "rgb", "hueValue", "rgb2hsl", "calln", "hsl2rgb", "hwb2rgb", "hsv2rgb", "hue", "hue<PERSON><PERSON><PERSON>", "exec", "p1", "p2", "rotate", "deg", "hslString", "Z", "Y", "X", "W", "V", "U", "T", "S", "R", "Q", "P", "O", "N", "M", "L", "K", "G", "H", "I", "J", "names$1", "OiceXe", "antiquewEte", "aqua", "aquamarRe", "azuY", "beige", "bisque", "black", "blan<PERSON>ed<PERSON><PERSON>", "Xe", "XeviTet", "bPwn", "burlywood", "caMtXe", "<PERSON><PERSON><PERSON><PERSON>", "KocTate", "cSO", "cSnflowerXe", "cSnsilk", "crimson", "cyan", "xXe", "xcyan", "xgTMnPd", "xWay", "xgYF", "xgYy", "xkhaki", "xmagFta", "xTivegYF", "xSange", "xScEd", "xYd", "xsOmon", "xsHgYF", "xUXe", "xUWay", "xUgYy", "xQe", "xviTet", "dAppRk", "dApskyXe", "dim<PERSON>ay", "dimgYy", "dodgerXe", "fiYbrick", "flSOwEte", "foYstWAn", "fuKsia", "gaRsbSo", "ghostwEte", "gTd", "gTMnPd", "Way", "gYF", "gYFLw", "gYy", "honeyMw", "hotpRk", "RdianYd", "Rdigo", "ivSy", "khaki", "lavFMr", "lavFMrXsh", "lawngYF", "NmoncEffon", "ZXe", "ZcSO", "<PERSON><PERSON><PERSON>", "ZgTMnPdLw", "ZWay", "ZgYF", "ZgYy", "ZpRk", "ZsOmon", "ZsHgYF", "ZskyXe", "ZUWay", "ZUgYy", "ZstAlXe", "ZLw", "lime", "limegYF", "lRF", "magFta", "ma<PERSON><PERSON>", "VaquamarRe", "VXe", "VScEd", "VpurpN", "VsHgYF", "VUXe", "VsprRggYF", "VQe", "VviTetYd", "midnightXe", "mRtcYam", "misty<PERSON>e", "moccasR", "navajowEte", "navy", "Tdlace", "Tive", "TivedBb", "<PERSON><PERSON>", "SangeYd", "ScEd", "pOegTMnPd", "pOegYF", "pOeQe", "pOeviTetYd", "papayawEp", "pHKpuff", "peru", "pRk", "plum", "powMrXe", "purpN", "YbeccapurpN", "Yd", "Psybrown", "PyOXe", "saddNbPwn", "sOmon", "sandybPwn", "sHgYF", "sHshell", "siFna", "silver", "skyXe", "UXe", "UWay", "UgYy", "snow", "sprRggYF", "stAlXe", "tan", "teO", "tEstN", "tomato", "Qe", "viTet", "JHt", "wEte", "wEtesmoke", "Lw", "LwgYF", "unpack", "unpacked", "tkeys", "j", "ok", "nk", "replace", "parseInt", "names", "nameParse", "transparent", "toLowerCase", "RGB_RE", "rgbParse", "rgbString", "to", "interpolate", "rgb1", "rgb2", "modHSL", "ratio", "clone", "proto", "fromObject", "input", "functionParse", "Color", "constructor", "_rgb", "_valid", "valid", "mix", "color", "weight", "c1", "c2", "w2", "w1", "clearer", "greyscale", "val", "opaquer", "negate", "lighten", "darken", "saturate", "desaturate", "index_esm", "isPatternOrGradient", "getHoverColor", "overrides", "descriptors", "getScope$1", "node", "root", "De<PERSON>ults", "_descriptors", "animation", "backgroundColor", "borderColor", "datasets", "devicePixelRatio", "context", "chart", "platform", "getDevicePixelRatio", "elements", "events", "font", "family", "style", "lineHeight", "hover", "hoverBackgroundColor", "ctx", "hoverBorderColor", "hoverColor", "indexAxis", "interaction", "mode", "intersect", "includeInvisible", "maintainAspectRatio", "onHover", "onClick", "parsing", "plugins", "responsive", "scale", "scales", "showLine", "drawActiveElementsOnTop", "describe", "get", "override", "route", "name", "targetScope", "targetName", "scopeObject", "targetScopeObject", "privateName", "defineProperties", "writable", "local", "defaults", "_scriptable", "startsWith", "_indexable", "_fallback", "toFontString", "_measureText", "data", "gc", "longest", "string", "textWidth", "measureText", "width", "_longestText", "arrayOfThings", "cache", "garbageCollect", "save", "jlen", "thing", "nestedThing", "restore", "gcLen", "_alignPixel", "pixel", "currentDevicePixelRatio", "halfWidth", "clearCanvas", "canvas", "getContext", "resetTransform", "clearRect", "height", "drawPoint", "drawPointLegend", "xOffset", "yOffset", "cornerRadius", "pointStyle", "rotation", "radius", "rad", "translate", "drawImage", "beginPath", "ellipse", "arc", "closePath", "moveTo", "lineTo", "SQRT1_2", "rect", "fill", "borderWidth", "stroke", "_isPointInArea", "point", "area", "margin", "top", "bottom", "clipArea", "clip", "unclipArea", "_steppedLineTo", "flip", "midpoint", "_bezierCurveTo", "bezierCurveTo", "cp1x", "cp2x", "cp1y", "cp2y", "renderText", "text", "opts", "lines", "strokeWidth", "strokeColor", "line", "setRenderOpts", "strokeStyle", "lineWidth", "strokeText", "max<PERSON><PERSON><PERSON>", "fillText", "decorateText", "translation", "fillStyle", "textAlign", "textBaseline", "strikethrough", "underline", "metrics", "actualBoundingBoxLeft", "actualBoundingBoxRight", "actualBoundingBoxAscent", "actualBoundingBoxDescent", "yDecoration", "decorationWidth", "addRoundedRectPath", "topLeft", "bottomLeft", "bottomRight", "topRight", "LINE_HEIGHT", "RegExp", "FONT_STYLE", "toLineHeight", "matches", "match", "numberOrZero", "_readValueToProps", "props", "objProps", "read", "prop", "toTRBL", "toTRBLCorners", "toPadding", "toFont", "fallback", "resolve", "inputs", "info", "cacheable", "_addGrace", "minmax", "grace", "beginAtZero", "change", "keepZero", "createContext", "parentContext", "_createResolver", "scopes", "prefixes", "rootScopes", "get<PERSON><PERSON><PERSON>", "_resolve", "Symbol", "toStringTag", "_cacheable", "_scopes", "_rootScopes", "_getTarget", "Proxy", "deleteProperty", "_keys", "_cached", "_resolveWithPrefixes", "getOwnPropertyDescriptor", "Reflect", "getPrototypeOf", "getKeysFromAllScopes", "includes", "ownKeys", "storage", "_storage", "_attachContext", "proxy", "subProxy", "descriptor<PERSON><PERSON><PERSON><PERSON>", "_proxy", "_context", "_subProxy", "_stack", "setContext", "receiver", "_resolveWithContext", "allKeys", "scriptable", "indexable", "_allKeys", "isScriptable", "isIndexable", "read<PERSON><PERSON>", "prefix", "needsSubResolver", "_resolveScriptable", "_resolveArray", "Error", "join", "delete", "createSubResolver", "arr", "filter", "<PERSON><PERSON><PERSON><PERSON>", "getScope", "parent", "addScopes", "parentScopes", "parentFallback", "allScopes", "addScopesFromKey", "subGetTarget", "resolveKeysFromAllScopes", "_parseObjectDataRadialScale", "_parsing", "parsed", "parse", "EPSILON", "getPoint", "skip", "getValueAxis", "splineCurve", "firstPoint", "middlePoint", "afterPoint", "next", "d01", "d12", "s01", "s12", "fa", "fb", "monotoneAdjust", "deltaK", "mK", "pointsLen", "alphaK", "betaK", "tauK", "squaredMagnitude", "pointCurrent", "pointAfter", "monotoneCompute", "valueAxis", "delta", "pointBefore", "iPixel", "vPixel", "splineCurveMonotone", "slopeDel<PERSON>", "capControlPoint", "pt", "capBezierPoints", "inArea", "inAreaPrev", "inAreaNext", "_updateBezierControlPoints", "loop", "controlPoints", "spanGaps", "cubicInterpolationMode", "prev", "tension", "_isDomSupported", "document", "_getParentNode", "domNode", "parentNode", "host", "parseMaxStyle", "styleValue", "parentProperty", "valueInPixels", "getComputedStyle", "element", "getStyle", "el", "getPropertyValue", "positions", "getPositionedStyle", "styles", "suffix", "pos", "useOffsetPos", "shadowRoot", "getCanvasPosition", "touches", "offsetX", "offsetY", "box", "getBoundingClientRect", "clientX", "clientY", "getRelativePosition", "evt", "borderBox", "boxSizing", "paddings", "borders", "getContainerSize", "maxHeight", "container", "clientWidth", "clientHeight", "containerStyle", "containerBorder", "containerPadding", "round1", "getMaximumSize", "bb<PERSON><PERSON><PERSON>", "bbHeight", "aspectRatio", "margins", "containerSize", "retinaScale", "forceRatio", "forceStyle", "pixelRatio", "deviceHeight", "deviceWidth", "setTransform", "supportsEventListenerOptions", "passiveSupported", "passive", "addEventListener", "removeEventListener", "readUsedSize", "_pointInLine", "_steppedInterpolation", "_bezierInterpolation", "cp1", "cp2", "intlCache", "Map", "getNumberFormat", "locale", "cache<PERSON>ey", "JSON", "stringify", "formatter", "Intl", "NumberFormat", "formatNumber", "num", "format", "getRightToLeftAdapter", "rectX", "<PERSON><PERSON><PERSON><PERSON>", "xPlus", "leftForLtr", "itemWidth", "getLeftToRightAdapter", "_itemWidth", "getRtlAdapter", "overrideTextDirection", "direction", "original", "getPropertyPriority", "setProperty", "prevTextDirection", "restoreTextDirection", "propertyFn", "between", "compare", "normalize", "normalizeSegment", "getSegment", "segment", "bounds", "startBound", "endBound", "_boundSegment", "inside", "subStart", "prevValue", "startIsBefore", "endIsBefore", "shouldStart", "shouldStop", "_boundSegments", "segments", "sub", "findStartAndEnd", "solidSegments", "cur", "stop", "_computeSegments", "segmentOptions", "_loop", "splitByStyles", "completeLoop", "_fullLoop", "doSplitByStyles", "chartContext", "_chart", "baseStyle", "readStyle", "_datasetIndex", "prevStyle", "addStyle", "st", "dir", "p0", "p0DataIndex", "p1DataIndex", "styleChanged", "borderCapStyle", "borderDash", "borderDashOffset", "borderJoinStyle", "$", "_", "a$", "a2", "a3", "a4", "a5", "a6", "a7", "a8", "a9", "aA", "aB", "aC", "aD", "aE", "aF", "aG", "aH", "aI", "aJ", "aK", "aL", "aM", "aN", "aO", "aP", "aQ", "aR", "aS", "aT", "aU", "aV", "aW", "aX", "aY", "aZ", "a_", "aa", "ab", "ac", "ad", "ae", "af", "ag", "ah", "ai", "aj", "ak", "al", "am", "an", "ao", "ap", "aq", "ar", "as", "at", "au", "av", "aw", "ax", "ay", "az", "b0", "b1", "b2", "b3", "b4", "b5", "q", "u", "z"], "sources": ["/Users/<USER>/wubin/augment_project/cursor_ statistics_project/node_modules/chart.js/dist/chunks/helpers.segment.mjs"], "sourcesContent": ["/*!\n * Chart.js v3.9.1\n * https://www.chartjs.org\n * (c) 2022 Chart.js Contributors\n * Released under the MIT License\n */\nfunction noop() {}\nconst uid = (function() {\n  let id = 0;\n  return function() {\n    return id++;\n  };\n}());\nfunction isNullOrUndef(value) {\n  return value === null || typeof value === 'undefined';\n}\nfunction isArray(value) {\n  if (Array.isArray && Array.isArray(value)) {\n    return true;\n  }\n  const type = Object.prototype.toString.call(value);\n  if (type.slice(0, 7) === '[object' && type.slice(-6) === 'Array]') {\n    return true;\n  }\n  return false;\n}\nfunction isObject(value) {\n  return value !== null && Object.prototype.toString.call(value) === '[object Object]';\n}\nconst isNumberFinite = (value) => (typeof value === 'number' || value instanceof Number) && isFinite(+value);\nfunction finiteOrDefault(value, defaultValue) {\n  return isNumberFinite(value) ? value : defaultValue;\n}\nfunction valueOrDefault(value, defaultValue) {\n  return typeof value === 'undefined' ? defaultValue : value;\n}\nconst toPercentage = (value, dimension) =>\n  typeof value === 'string' && value.endsWith('%') ?\n    parseFloat(value) / 100\n    : value / dimension;\nconst toDimension = (value, dimension) =>\n  typeof value === 'string' && value.endsWith('%') ?\n    parseFloat(value) / 100 * dimension\n    : +value;\nfunction callback(fn, args, thisArg) {\n  if (fn && typeof fn.call === 'function') {\n    return fn.apply(thisArg, args);\n  }\n}\nfunction each(loopable, fn, thisArg, reverse) {\n  let i, len, keys;\n  if (isArray(loopable)) {\n    len = loopable.length;\n    if (reverse) {\n      for (i = len - 1; i >= 0; i--) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    } else {\n      for (i = 0; i < len; i++) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    }\n  } else if (isObject(loopable)) {\n    keys = Object.keys(loopable);\n    len = keys.length;\n    for (i = 0; i < len; i++) {\n      fn.call(thisArg, loopable[keys[i]], keys[i]);\n    }\n  }\n}\nfunction _elementsEqual(a0, a1) {\n  let i, ilen, v0, v1;\n  if (!a0 || !a1 || a0.length !== a1.length) {\n    return false;\n  }\n  for (i = 0, ilen = a0.length; i < ilen; ++i) {\n    v0 = a0[i];\n    v1 = a1[i];\n    if (v0.datasetIndex !== v1.datasetIndex || v0.index !== v1.index) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction clone$1(source) {\n  if (isArray(source)) {\n    return source.map(clone$1);\n  }\n  if (isObject(source)) {\n    const target = Object.create(null);\n    const keys = Object.keys(source);\n    const klen = keys.length;\n    let k = 0;\n    for (; k < klen; ++k) {\n      target[keys[k]] = clone$1(source[keys[k]]);\n    }\n    return target;\n  }\n  return source;\n}\nfunction isValidKey(key) {\n  return ['__proto__', 'prototype', 'constructor'].indexOf(key) === -1;\n}\nfunction _merger(key, target, source, options) {\n  if (!isValidKey(key)) {\n    return;\n  }\n  const tval = target[key];\n  const sval = source[key];\n  if (isObject(tval) && isObject(sval)) {\n    merge(tval, sval, options);\n  } else {\n    target[key] = clone$1(sval);\n  }\n}\nfunction merge(target, source, options) {\n  const sources = isArray(source) ? source : [source];\n  const ilen = sources.length;\n  if (!isObject(target)) {\n    return target;\n  }\n  options = options || {};\n  const merger = options.merger || _merger;\n  for (let i = 0; i < ilen; ++i) {\n    source = sources[i];\n    if (!isObject(source)) {\n      continue;\n    }\n    const keys = Object.keys(source);\n    for (let k = 0, klen = keys.length; k < klen; ++k) {\n      merger(keys[k], target, source, options);\n    }\n  }\n  return target;\n}\nfunction mergeIf(target, source) {\n  return merge(target, source, {merger: _mergerIf});\n}\nfunction _mergerIf(key, target, source) {\n  if (!isValidKey(key)) {\n    return;\n  }\n  const tval = target[key];\n  const sval = source[key];\n  if (isObject(tval) && isObject(sval)) {\n    mergeIf(tval, sval);\n  } else if (!Object.prototype.hasOwnProperty.call(target, key)) {\n    target[key] = clone$1(sval);\n  }\n}\nfunction _deprecated(scope, value, previous, current) {\n  if (value !== undefined) {\n    console.warn(scope + ': \"' + previous +\n\t\t\t'\" is deprecated. Please use \"' + current + '\" instead');\n  }\n}\nconst keyResolvers = {\n  '': v => v,\n  x: o => o.x,\n  y: o => o.y\n};\nfunction resolveObjectKey(obj, key) {\n  const resolver = keyResolvers[key] || (keyResolvers[key] = _getKeyResolver(key));\n  return resolver(obj);\n}\nfunction _getKeyResolver(key) {\n  const keys = _splitKey(key);\n  return obj => {\n    for (const k of keys) {\n      if (k === '') {\n        break;\n      }\n      obj = obj && obj[k];\n    }\n    return obj;\n  };\n}\nfunction _splitKey(key) {\n  const parts = key.split('.');\n  const keys = [];\n  let tmp = '';\n  for (const part of parts) {\n    tmp += part;\n    if (tmp.endsWith('\\\\')) {\n      tmp = tmp.slice(0, -1) + '.';\n    } else {\n      keys.push(tmp);\n      tmp = '';\n    }\n  }\n  return keys;\n}\nfunction _capitalize(str) {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\nconst defined = (value) => typeof value !== 'undefined';\nconst isFunction = (value) => typeof value === 'function';\nconst setsEqual = (a, b) => {\n  if (a.size !== b.size) {\n    return false;\n  }\n  for (const item of a) {\n    if (!b.has(item)) {\n      return false;\n    }\n  }\n  return true;\n};\nfunction _isClickEvent(e) {\n  return e.type === 'mouseup' || e.type === 'click' || e.type === 'contextmenu';\n}\n\nconst PI = Math.PI;\nconst TAU = 2 * PI;\nconst PITAU = TAU + PI;\nconst INFINITY = Number.POSITIVE_INFINITY;\nconst RAD_PER_DEG = PI / 180;\nconst HALF_PI = PI / 2;\nconst QUARTER_PI = PI / 4;\nconst TWO_THIRDS_PI = PI * 2 / 3;\nconst log10 = Math.log10;\nconst sign = Math.sign;\nfunction niceNum(range) {\n  const roundedRange = Math.round(range);\n  range = almostEquals(range, roundedRange, range / 1000) ? roundedRange : range;\n  const niceRange = Math.pow(10, Math.floor(log10(range)));\n  const fraction = range / niceRange;\n  const niceFraction = fraction <= 1 ? 1 : fraction <= 2 ? 2 : fraction <= 5 ? 5 : 10;\n  return niceFraction * niceRange;\n}\nfunction _factorize(value) {\n  const result = [];\n  const sqrt = Math.sqrt(value);\n  let i;\n  for (i = 1; i < sqrt; i++) {\n    if (value % i === 0) {\n      result.push(i);\n      result.push(value / i);\n    }\n  }\n  if (sqrt === (sqrt | 0)) {\n    result.push(sqrt);\n  }\n  result.sort((a, b) => a - b).pop();\n  return result;\n}\nfunction isNumber(n) {\n  return !isNaN(parseFloat(n)) && isFinite(n);\n}\nfunction almostEquals(x, y, epsilon) {\n  return Math.abs(x - y) < epsilon;\n}\nfunction almostWhole(x, epsilon) {\n  const rounded = Math.round(x);\n  return ((rounded - epsilon) <= x) && ((rounded + epsilon) >= x);\n}\nfunction _setMinAndMaxByKey(array, target, property) {\n  let i, ilen, value;\n  for (i = 0, ilen = array.length; i < ilen; i++) {\n    value = array[i][property];\n    if (!isNaN(value)) {\n      target.min = Math.min(target.min, value);\n      target.max = Math.max(target.max, value);\n    }\n  }\n}\nfunction toRadians(degrees) {\n  return degrees * (PI / 180);\n}\nfunction toDegrees(radians) {\n  return radians * (180 / PI);\n}\nfunction _decimalPlaces(x) {\n  if (!isNumberFinite(x)) {\n    return;\n  }\n  let e = 1;\n  let p = 0;\n  while (Math.round(x * e) / e !== x) {\n    e *= 10;\n    p++;\n  }\n  return p;\n}\nfunction getAngleFromPoint(centrePoint, anglePoint) {\n  const distanceFromXCenter = anglePoint.x - centrePoint.x;\n  const distanceFromYCenter = anglePoint.y - centrePoint.y;\n  const radialDistanceFromCenter = Math.sqrt(distanceFromXCenter * distanceFromXCenter + distanceFromYCenter * distanceFromYCenter);\n  let angle = Math.atan2(distanceFromYCenter, distanceFromXCenter);\n  if (angle < (-0.5 * PI)) {\n    angle += TAU;\n  }\n  return {\n    angle,\n    distance: radialDistanceFromCenter\n  };\n}\nfunction distanceBetweenPoints(pt1, pt2) {\n  return Math.sqrt(Math.pow(pt2.x - pt1.x, 2) + Math.pow(pt2.y - pt1.y, 2));\n}\nfunction _angleDiff(a, b) {\n  return (a - b + PITAU) % TAU - PI;\n}\nfunction _normalizeAngle(a) {\n  return (a % TAU + TAU) % TAU;\n}\nfunction _angleBetween(angle, start, end, sameAngleIsFullCircle) {\n  const a = _normalizeAngle(angle);\n  const s = _normalizeAngle(start);\n  const e = _normalizeAngle(end);\n  const angleToStart = _normalizeAngle(s - a);\n  const angleToEnd = _normalizeAngle(e - a);\n  const startToAngle = _normalizeAngle(a - s);\n  const endToAngle = _normalizeAngle(a - e);\n  return a === s || a === e || (sameAngleIsFullCircle && s === e)\n    || (angleToStart > angleToEnd && startToAngle < endToAngle);\n}\nfunction _limitValue(value, min, max) {\n  return Math.max(min, Math.min(max, value));\n}\nfunction _int16Range(value) {\n  return _limitValue(value, -32768, 32767);\n}\nfunction _isBetween(value, start, end, epsilon = 1e-6) {\n  return value >= Math.min(start, end) - epsilon && value <= Math.max(start, end) + epsilon;\n}\n\nfunction _lookup(table, value, cmp) {\n  cmp = cmp || ((index) => table[index] < value);\n  let hi = table.length - 1;\n  let lo = 0;\n  let mid;\n  while (hi - lo > 1) {\n    mid = (lo + hi) >> 1;\n    if (cmp(mid)) {\n      lo = mid;\n    } else {\n      hi = mid;\n    }\n  }\n  return {lo, hi};\n}\nconst _lookupByKey = (table, key, value, last) =>\n  _lookup(table, value, last\n    ? index => table[index][key] <= value\n    : index => table[index][key] < value);\nconst _rlookupByKey = (table, key, value) =>\n  _lookup(table, value, index => table[index][key] >= value);\nfunction _filterBetween(values, min, max) {\n  let start = 0;\n  let end = values.length;\n  while (start < end && values[start] < min) {\n    start++;\n  }\n  while (end > start && values[end - 1] > max) {\n    end--;\n  }\n  return start > 0 || end < values.length\n    ? values.slice(start, end)\n    : values;\n}\nconst arrayEvents = ['push', 'pop', 'shift', 'splice', 'unshift'];\nfunction listenArrayEvents(array, listener) {\n  if (array._chartjs) {\n    array._chartjs.listeners.push(listener);\n    return;\n  }\n  Object.defineProperty(array, '_chartjs', {\n    configurable: true,\n    enumerable: false,\n    value: {\n      listeners: [listener]\n    }\n  });\n  arrayEvents.forEach((key) => {\n    const method = '_onData' + _capitalize(key);\n    const base = array[key];\n    Object.defineProperty(array, key, {\n      configurable: true,\n      enumerable: false,\n      value(...args) {\n        const res = base.apply(this, args);\n        array._chartjs.listeners.forEach((object) => {\n          if (typeof object[method] === 'function') {\n            object[method](...args);\n          }\n        });\n        return res;\n      }\n    });\n  });\n}\nfunction unlistenArrayEvents(array, listener) {\n  const stub = array._chartjs;\n  if (!stub) {\n    return;\n  }\n  const listeners = stub.listeners;\n  const index = listeners.indexOf(listener);\n  if (index !== -1) {\n    listeners.splice(index, 1);\n  }\n  if (listeners.length > 0) {\n    return;\n  }\n  arrayEvents.forEach((key) => {\n    delete array[key];\n  });\n  delete array._chartjs;\n}\nfunction _arrayUnique(items) {\n  const set = new Set();\n  let i, ilen;\n  for (i = 0, ilen = items.length; i < ilen; ++i) {\n    set.add(items[i]);\n  }\n  if (set.size === ilen) {\n    return items;\n  }\n  return Array.from(set);\n}\n\nfunction fontString(pixelSize, fontStyle, fontFamily) {\n  return fontStyle + ' ' + pixelSize + 'px ' + fontFamily;\n}\nconst requestAnimFrame = (function() {\n  if (typeof window === 'undefined') {\n    return function(callback) {\n      return callback();\n    };\n  }\n  return window.requestAnimationFrame;\n}());\nfunction throttled(fn, thisArg, updateFn) {\n  const updateArgs = updateFn || ((args) => Array.prototype.slice.call(args));\n  let ticking = false;\n  let args = [];\n  return function(...rest) {\n    args = updateArgs(rest);\n    if (!ticking) {\n      ticking = true;\n      requestAnimFrame.call(window, () => {\n        ticking = false;\n        fn.apply(thisArg, args);\n      });\n    }\n  };\n}\nfunction debounce(fn, delay) {\n  let timeout;\n  return function(...args) {\n    if (delay) {\n      clearTimeout(timeout);\n      timeout = setTimeout(fn, delay, args);\n    } else {\n      fn.apply(this, args);\n    }\n    return delay;\n  };\n}\nconst _toLeftRightCenter = (align) => align === 'start' ? 'left' : align === 'end' ? 'right' : 'center';\nconst _alignStartEnd = (align, start, end) => align === 'start' ? start : align === 'end' ? end : (start + end) / 2;\nconst _textX = (align, left, right, rtl) => {\n  const check = rtl ? 'left' : 'right';\n  return align === check ? right : align === 'center' ? (left + right) / 2 : left;\n};\nfunction _getStartAndCountOfVisiblePoints(meta, points, animationsDisabled) {\n  const pointCount = points.length;\n  let start = 0;\n  let count = pointCount;\n  if (meta._sorted) {\n    const {iScale, _parsed} = meta;\n    const axis = iScale.axis;\n    const {min, max, minDefined, maxDefined} = iScale.getUserBounds();\n    if (minDefined) {\n      start = _limitValue(Math.min(\n        _lookupByKey(_parsed, iScale.axis, min).lo,\n        animationsDisabled ? pointCount : _lookupByKey(points, axis, iScale.getPixelForValue(min)).lo),\n      0, pointCount - 1);\n    }\n    if (maxDefined) {\n      count = _limitValue(Math.max(\n        _lookupByKey(_parsed, iScale.axis, max, true).hi + 1,\n        animationsDisabled ? 0 : _lookupByKey(points, axis, iScale.getPixelForValue(max), true).hi + 1),\n      start, pointCount) - start;\n    } else {\n      count = pointCount - start;\n    }\n  }\n  return {start, count};\n}\nfunction _scaleRangesChanged(meta) {\n  const {xScale, yScale, _scaleRanges} = meta;\n  const newRanges = {\n    xmin: xScale.min,\n    xmax: xScale.max,\n    ymin: yScale.min,\n    ymax: yScale.max\n  };\n  if (!_scaleRanges) {\n    meta._scaleRanges = newRanges;\n    return true;\n  }\n  const changed = _scaleRanges.xmin !== xScale.min\n\t\t|| _scaleRanges.xmax !== xScale.max\n\t\t|| _scaleRanges.ymin !== yScale.min\n\t\t|| _scaleRanges.ymax !== yScale.max;\n  Object.assign(_scaleRanges, newRanges);\n  return changed;\n}\n\nconst atEdge = (t) => t === 0 || t === 1;\nconst elasticIn = (t, s, p) => -(Math.pow(2, 10 * (t -= 1)) * Math.sin((t - s) * TAU / p));\nconst elasticOut = (t, s, p) => Math.pow(2, -10 * t) * Math.sin((t - s) * TAU / p) + 1;\nconst effects = {\n  linear: t => t,\n  easeInQuad: t => t * t,\n  easeOutQuad: t => -t * (t - 2),\n  easeInOutQuad: t => ((t /= 0.5) < 1)\n    ? 0.5 * t * t\n    : -0.5 * ((--t) * (t - 2) - 1),\n  easeInCubic: t => t * t * t,\n  easeOutCubic: t => (t -= 1) * t * t + 1,\n  easeInOutCubic: t => ((t /= 0.5) < 1)\n    ? 0.5 * t * t * t\n    : 0.5 * ((t -= 2) * t * t + 2),\n  easeInQuart: t => t * t * t * t,\n  easeOutQuart: t => -((t -= 1) * t * t * t - 1),\n  easeInOutQuart: t => ((t /= 0.5) < 1)\n    ? 0.5 * t * t * t * t\n    : -0.5 * ((t -= 2) * t * t * t - 2),\n  easeInQuint: t => t * t * t * t * t,\n  easeOutQuint: t => (t -= 1) * t * t * t * t + 1,\n  easeInOutQuint: t => ((t /= 0.5) < 1)\n    ? 0.5 * t * t * t * t * t\n    : 0.5 * ((t -= 2) * t * t * t * t + 2),\n  easeInSine: t => -Math.cos(t * HALF_PI) + 1,\n  easeOutSine: t => Math.sin(t * HALF_PI),\n  easeInOutSine: t => -0.5 * (Math.cos(PI * t) - 1),\n  easeInExpo: t => (t === 0) ? 0 : Math.pow(2, 10 * (t - 1)),\n  easeOutExpo: t => (t === 1) ? 1 : -Math.pow(2, -10 * t) + 1,\n  easeInOutExpo: t => atEdge(t) ? t : t < 0.5\n    ? 0.5 * Math.pow(2, 10 * (t * 2 - 1))\n    : 0.5 * (-Math.pow(2, -10 * (t * 2 - 1)) + 2),\n  easeInCirc: t => (t >= 1) ? t : -(Math.sqrt(1 - t * t) - 1),\n  easeOutCirc: t => Math.sqrt(1 - (t -= 1) * t),\n  easeInOutCirc: t => ((t /= 0.5) < 1)\n    ? -0.5 * (Math.sqrt(1 - t * t) - 1)\n    : 0.5 * (Math.sqrt(1 - (t -= 2) * t) + 1),\n  easeInElastic: t => atEdge(t) ? t : elasticIn(t, 0.075, 0.3),\n  easeOutElastic: t => atEdge(t) ? t : elasticOut(t, 0.075, 0.3),\n  easeInOutElastic(t) {\n    const s = 0.1125;\n    const p = 0.45;\n    return atEdge(t) ? t :\n      t < 0.5\n        ? 0.5 * elasticIn(t * 2, s, p)\n        : 0.5 + 0.5 * elasticOut(t * 2 - 1, s, p);\n  },\n  easeInBack(t) {\n    const s = 1.70158;\n    return t * t * ((s + 1) * t - s);\n  },\n  easeOutBack(t) {\n    const s = 1.70158;\n    return (t -= 1) * t * ((s + 1) * t + s) + 1;\n  },\n  easeInOutBack(t) {\n    let s = 1.70158;\n    if ((t /= 0.5) < 1) {\n      return 0.5 * (t * t * (((s *= (1.525)) + 1) * t - s));\n    }\n    return 0.5 * ((t -= 2) * t * (((s *= (1.525)) + 1) * t + s) + 2);\n  },\n  easeInBounce: t => 1 - effects.easeOutBounce(1 - t),\n  easeOutBounce(t) {\n    const m = 7.5625;\n    const d = 2.75;\n    if (t < (1 / d)) {\n      return m * t * t;\n    }\n    if (t < (2 / d)) {\n      return m * (t -= (1.5 / d)) * t + 0.75;\n    }\n    if (t < (2.5 / d)) {\n      return m * (t -= (2.25 / d)) * t + 0.9375;\n    }\n    return m * (t -= (2.625 / d)) * t + 0.984375;\n  },\n  easeInOutBounce: t => (t < 0.5)\n    ? effects.easeInBounce(t * 2) * 0.5\n    : effects.easeOutBounce(t * 2 - 1) * 0.5 + 0.5,\n};\n\n/*!\n * @kurkle/color v0.2.1\n * https://github.com/kurkle/color#readme\n * (c) 2022 Jukka Kurkela\n * Released under the MIT License\n */\nfunction round(v) {\n  return v + 0.5 | 0;\n}\nconst lim = (v, l, h) => Math.max(Math.min(v, h), l);\nfunction p2b(v) {\n  return lim(round(v * 2.55), 0, 255);\n}\nfunction n2b(v) {\n  return lim(round(v * 255), 0, 255);\n}\nfunction b2n(v) {\n  return lim(round(v / 2.55) / 100, 0, 1);\n}\nfunction n2p(v) {\n  return lim(round(v * 100), 0, 100);\n}\nconst map$1 = {0: 0, 1: 1, 2: 2, 3: 3, 4: 4, 5: 5, 6: 6, 7: 7, 8: 8, 9: 9, A: 10, B: 11, C: 12, D: 13, E: 14, F: 15, a: 10, b: 11, c: 12, d: 13, e: 14, f: 15};\nconst hex = [...'0123456789ABCDEF'];\nconst h1 = b => hex[b & 0xF];\nconst h2 = b => hex[(b & 0xF0) >> 4] + hex[b & 0xF];\nconst eq = b => ((b & 0xF0) >> 4) === (b & 0xF);\nconst isShort = v => eq(v.r) && eq(v.g) && eq(v.b) && eq(v.a);\nfunction hexParse(str) {\n  var len = str.length;\n  var ret;\n  if (str[0] === '#') {\n    if (len === 4 || len === 5) {\n      ret = {\n        r: 255 & map$1[str[1]] * 17,\n        g: 255 & map$1[str[2]] * 17,\n        b: 255 & map$1[str[3]] * 17,\n        a: len === 5 ? map$1[str[4]] * 17 : 255\n      };\n    } else if (len === 7 || len === 9) {\n      ret = {\n        r: map$1[str[1]] << 4 | map$1[str[2]],\n        g: map$1[str[3]] << 4 | map$1[str[4]],\n        b: map$1[str[5]] << 4 | map$1[str[6]],\n        a: len === 9 ? (map$1[str[7]] << 4 | map$1[str[8]]) : 255\n      };\n    }\n  }\n  return ret;\n}\nconst alpha = (a, f) => a < 255 ? f(a) : '';\nfunction hexString(v) {\n  var f = isShort(v) ? h1 : h2;\n  return v\n    ? '#' + f(v.r) + f(v.g) + f(v.b) + alpha(v.a, f)\n    : undefined;\n}\nconst HUE_RE = /^(hsla?|hwb|hsv)\\(\\s*([-+.e\\d]+)(?:deg)?[\\s,]+([-+.e\\d]+)%[\\s,]+([-+.e\\d]+)%(?:[\\s,]+([-+.e\\d]+)(%)?)?\\s*\\)$/;\nfunction hsl2rgbn(h, s, l) {\n  const a = s * Math.min(l, 1 - l);\n  const f = (n, k = (n + h / 30) % 12) => l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);\n  return [f(0), f(8), f(4)];\n}\nfunction hsv2rgbn(h, s, v) {\n  const f = (n, k = (n + h / 60) % 6) => v - v * s * Math.max(Math.min(k, 4 - k, 1), 0);\n  return [f(5), f(3), f(1)];\n}\nfunction hwb2rgbn(h, w, b) {\n  const rgb = hsl2rgbn(h, 1, 0.5);\n  let i;\n  if (w + b > 1) {\n    i = 1 / (w + b);\n    w *= i;\n    b *= i;\n  }\n  for (i = 0; i < 3; i++) {\n    rgb[i] *= 1 - w - b;\n    rgb[i] += w;\n  }\n  return rgb;\n}\nfunction hueValue(r, g, b, d, max) {\n  if (r === max) {\n    return ((g - b) / d) + (g < b ? 6 : 0);\n  }\n  if (g === max) {\n    return (b - r) / d + 2;\n  }\n  return (r - g) / d + 4;\n}\nfunction rgb2hsl(v) {\n  const range = 255;\n  const r = v.r / range;\n  const g = v.g / range;\n  const b = v.b / range;\n  const max = Math.max(r, g, b);\n  const min = Math.min(r, g, b);\n  const l = (max + min) / 2;\n  let h, s, d;\n  if (max !== min) {\n    d = max - min;\n    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n    h = hueValue(r, g, b, d, max);\n    h = h * 60 + 0.5;\n  }\n  return [h | 0, s || 0, l];\n}\nfunction calln(f, a, b, c) {\n  return (\n    Array.isArray(a)\n      ? f(a[0], a[1], a[2])\n      : f(a, b, c)\n  ).map(n2b);\n}\nfunction hsl2rgb(h, s, l) {\n  return calln(hsl2rgbn, h, s, l);\n}\nfunction hwb2rgb(h, w, b) {\n  return calln(hwb2rgbn, h, w, b);\n}\nfunction hsv2rgb(h, s, v) {\n  return calln(hsv2rgbn, h, s, v);\n}\nfunction hue(h) {\n  return (h % 360 + 360) % 360;\n}\nfunction hueParse(str) {\n  const m = HUE_RE.exec(str);\n  let a = 255;\n  let v;\n  if (!m) {\n    return;\n  }\n  if (m[5] !== v) {\n    a = m[6] ? p2b(+m[5]) : n2b(+m[5]);\n  }\n  const h = hue(+m[2]);\n  const p1 = +m[3] / 100;\n  const p2 = +m[4] / 100;\n  if (m[1] === 'hwb') {\n    v = hwb2rgb(h, p1, p2);\n  } else if (m[1] === 'hsv') {\n    v = hsv2rgb(h, p1, p2);\n  } else {\n    v = hsl2rgb(h, p1, p2);\n  }\n  return {\n    r: v[0],\n    g: v[1],\n    b: v[2],\n    a: a\n  };\n}\nfunction rotate(v, deg) {\n  var h = rgb2hsl(v);\n  h[0] = hue(h[0] + deg);\n  h = hsl2rgb(h);\n  v.r = h[0];\n  v.g = h[1];\n  v.b = h[2];\n}\nfunction hslString(v) {\n  if (!v) {\n    return;\n  }\n  const a = rgb2hsl(v);\n  const h = a[0];\n  const s = n2p(a[1]);\n  const l = n2p(a[2]);\n  return v.a < 255\n    ? `hsla(${h}, ${s}%, ${l}%, ${b2n(v.a)})`\n    : `hsl(${h}, ${s}%, ${l}%)`;\n}\nconst map = {\n  x: 'dark',\n  Z: 'light',\n  Y: 're',\n  X: 'blu',\n  W: 'gr',\n  V: 'medium',\n  U: 'slate',\n  A: 'ee',\n  T: 'ol',\n  S: 'or',\n  B: 'ra',\n  C: 'lateg',\n  D: 'ights',\n  R: 'in',\n  Q: 'turquois',\n  E: 'hi',\n  P: 'ro',\n  O: 'al',\n  N: 'le',\n  M: 'de',\n  L: 'yello',\n  F: 'en',\n  K: 'ch',\n  G: 'arks',\n  H: 'ea',\n  I: 'ightg',\n  J: 'wh'\n};\nconst names$1 = {\n  OiceXe: 'f0f8ff',\n  antiquewEte: 'faebd7',\n  aqua: 'ffff',\n  aquamarRe: '7fffd4',\n  azuY: 'f0ffff',\n  beige: 'f5f5dc',\n  bisque: 'ffe4c4',\n  black: '0',\n  blanKedOmond: 'ffebcd',\n  Xe: 'ff',\n  XeviTet: '8a2be2',\n  bPwn: 'a52a2a',\n  burlywood: 'deb887',\n  caMtXe: '5f9ea0',\n  KartYuse: '7fff00',\n  KocTate: 'd2691e',\n  cSO: 'ff7f50',\n  cSnflowerXe: '6495ed',\n  cSnsilk: 'fff8dc',\n  crimson: 'dc143c',\n  cyan: 'ffff',\n  xXe: '8b',\n  xcyan: '8b8b',\n  xgTMnPd: 'b8860b',\n  xWay: 'a9a9a9',\n  xgYF: '6400',\n  xgYy: 'a9a9a9',\n  xkhaki: 'bdb76b',\n  xmagFta: '8b008b',\n  xTivegYF: '556b2f',\n  xSange: 'ff8c00',\n  xScEd: '9932cc',\n  xYd: '8b0000',\n  xsOmon: 'e9967a',\n  xsHgYF: '8fbc8f',\n  xUXe: '483d8b',\n  xUWay: '2f4f4f',\n  xUgYy: '2f4f4f',\n  xQe: 'ced1',\n  xviTet: '9400d3',\n  dAppRk: 'ff1493',\n  dApskyXe: 'bfff',\n  dimWay: '696969',\n  dimgYy: '696969',\n  dodgerXe: '1e90ff',\n  fiYbrick: 'b22222',\n  flSOwEte: 'fffaf0',\n  foYstWAn: '228b22',\n  fuKsia: 'ff00ff',\n  gaRsbSo: 'dcdcdc',\n  ghostwEte: 'f8f8ff',\n  gTd: 'ffd700',\n  gTMnPd: 'daa520',\n  Way: '808080',\n  gYF: '8000',\n  gYFLw: 'adff2f',\n  gYy: '808080',\n  honeyMw: 'f0fff0',\n  hotpRk: 'ff69b4',\n  RdianYd: 'cd5c5c',\n  Rdigo: '4b0082',\n  ivSy: 'fffff0',\n  khaki: 'f0e68c',\n  lavFMr: 'e6e6fa',\n  lavFMrXsh: 'fff0f5',\n  lawngYF: '7cfc00',\n  NmoncEffon: 'fffacd',\n  ZXe: 'add8e6',\n  ZcSO: 'f08080',\n  Zcyan: 'e0ffff',\n  ZgTMnPdLw: 'fafad2',\n  ZWay: 'd3d3d3',\n  ZgYF: '90ee90',\n  ZgYy: 'd3d3d3',\n  ZpRk: 'ffb6c1',\n  ZsOmon: 'ffa07a',\n  ZsHgYF: '20b2aa',\n  ZskyXe: '87cefa',\n  ZUWay: '778899',\n  ZUgYy: '778899',\n  ZstAlXe: 'b0c4de',\n  ZLw: 'ffffe0',\n  lime: 'ff00',\n  limegYF: '32cd32',\n  lRF: 'faf0e6',\n  magFta: 'ff00ff',\n  maPon: '800000',\n  VaquamarRe: '66cdaa',\n  VXe: 'cd',\n  VScEd: 'ba55d3',\n  VpurpN: '9370db',\n  VsHgYF: '3cb371',\n  VUXe: '7b68ee',\n  VsprRggYF: 'fa9a',\n  VQe: '48d1cc',\n  VviTetYd: 'c71585',\n  midnightXe: '191970',\n  mRtcYam: 'f5fffa',\n  mistyPse: 'ffe4e1',\n  moccasR: 'ffe4b5',\n  navajowEte: 'ffdead',\n  navy: '80',\n  Tdlace: 'fdf5e6',\n  Tive: '808000',\n  TivedBb: '6b8e23',\n  Sange: 'ffa500',\n  SangeYd: 'ff4500',\n  ScEd: 'da70d6',\n  pOegTMnPd: 'eee8aa',\n  pOegYF: '98fb98',\n  pOeQe: 'afeeee',\n  pOeviTetYd: 'db7093',\n  papayawEp: 'ffefd5',\n  pHKpuff: 'ffdab9',\n  peru: 'cd853f',\n  pRk: 'ffc0cb',\n  plum: 'dda0dd',\n  powMrXe: 'b0e0e6',\n  purpN: '800080',\n  YbeccapurpN: '663399',\n  Yd: 'ff0000',\n  Psybrown: 'bc8f8f',\n  PyOXe: '4169e1',\n  saddNbPwn: '8b4513',\n  sOmon: 'fa8072',\n  sandybPwn: 'f4a460',\n  sHgYF: '2e8b57',\n  sHshell: 'fff5ee',\n  siFna: 'a0522d',\n  silver: 'c0c0c0',\n  skyXe: '87ceeb',\n  UXe: '6a5acd',\n  UWay: '708090',\n  UgYy: '708090',\n  snow: 'fffafa',\n  sprRggYF: 'ff7f',\n  stAlXe: '4682b4',\n  tan: 'd2b48c',\n  teO: '8080',\n  tEstN: 'd8bfd8',\n  tomato: 'ff6347',\n  Qe: '40e0d0',\n  viTet: 'ee82ee',\n  JHt: 'f5deb3',\n  wEte: 'ffffff',\n  wEtesmoke: 'f5f5f5',\n  Lw: 'ffff00',\n  LwgYF: '9acd32'\n};\nfunction unpack() {\n  const unpacked = {};\n  const keys = Object.keys(names$1);\n  const tkeys = Object.keys(map);\n  let i, j, k, ok, nk;\n  for (i = 0; i < keys.length; i++) {\n    ok = nk = keys[i];\n    for (j = 0; j < tkeys.length; j++) {\n      k = tkeys[j];\n      nk = nk.replace(k, map[k]);\n    }\n    k = parseInt(names$1[ok], 16);\n    unpacked[nk] = [k >> 16 & 0xFF, k >> 8 & 0xFF, k & 0xFF];\n  }\n  return unpacked;\n}\nlet names;\nfunction nameParse(str) {\n  if (!names) {\n    names = unpack();\n    names.transparent = [0, 0, 0, 0];\n  }\n  const a = names[str.toLowerCase()];\n  return a && {\n    r: a[0],\n    g: a[1],\n    b: a[2],\n    a: a.length === 4 ? a[3] : 255\n  };\n}\nconst RGB_RE = /^rgba?\\(\\s*([-+.\\d]+)(%)?[\\s,]+([-+.e\\d]+)(%)?[\\s,]+([-+.e\\d]+)(%)?(?:[\\s,/]+([-+.e\\d]+)(%)?)?\\s*\\)$/;\nfunction rgbParse(str) {\n  const m = RGB_RE.exec(str);\n  let a = 255;\n  let r, g, b;\n  if (!m) {\n    return;\n  }\n  if (m[7] !== r) {\n    const v = +m[7];\n    a = m[8] ? p2b(v) : lim(v * 255, 0, 255);\n  }\n  r = +m[1];\n  g = +m[3];\n  b = +m[5];\n  r = 255 & (m[2] ? p2b(r) : lim(r, 0, 255));\n  g = 255 & (m[4] ? p2b(g) : lim(g, 0, 255));\n  b = 255 & (m[6] ? p2b(b) : lim(b, 0, 255));\n  return {\n    r: r,\n    g: g,\n    b: b,\n    a: a\n  };\n}\nfunction rgbString(v) {\n  return v && (\n    v.a < 255\n      ? `rgba(${v.r}, ${v.g}, ${v.b}, ${b2n(v.a)})`\n      : `rgb(${v.r}, ${v.g}, ${v.b})`\n  );\n}\nconst to = v => v <= 0.0031308 ? v * 12.92 : Math.pow(v, 1.0 / 2.4) * 1.055 - 0.055;\nconst from = v => v <= 0.04045 ? v / 12.92 : Math.pow((v + 0.055) / 1.055, 2.4);\nfunction interpolate(rgb1, rgb2, t) {\n  const r = from(b2n(rgb1.r));\n  const g = from(b2n(rgb1.g));\n  const b = from(b2n(rgb1.b));\n  return {\n    r: n2b(to(r + t * (from(b2n(rgb2.r)) - r))),\n    g: n2b(to(g + t * (from(b2n(rgb2.g)) - g))),\n    b: n2b(to(b + t * (from(b2n(rgb2.b)) - b))),\n    a: rgb1.a + t * (rgb2.a - rgb1.a)\n  };\n}\nfunction modHSL(v, i, ratio) {\n  if (v) {\n    let tmp = rgb2hsl(v);\n    tmp[i] = Math.max(0, Math.min(tmp[i] + tmp[i] * ratio, i === 0 ? 360 : 1));\n    tmp = hsl2rgb(tmp);\n    v.r = tmp[0];\n    v.g = tmp[1];\n    v.b = tmp[2];\n  }\n}\nfunction clone(v, proto) {\n  return v ? Object.assign(proto || {}, v) : v;\n}\nfunction fromObject(input) {\n  var v = {r: 0, g: 0, b: 0, a: 255};\n  if (Array.isArray(input)) {\n    if (input.length >= 3) {\n      v = {r: input[0], g: input[1], b: input[2], a: 255};\n      if (input.length > 3) {\n        v.a = n2b(input[3]);\n      }\n    }\n  } else {\n    v = clone(input, {r: 0, g: 0, b: 0, a: 1});\n    v.a = n2b(v.a);\n  }\n  return v;\n}\nfunction functionParse(str) {\n  if (str.charAt(0) === 'r') {\n    return rgbParse(str);\n  }\n  return hueParse(str);\n}\nclass Color {\n  constructor(input) {\n    if (input instanceof Color) {\n      return input;\n    }\n    const type = typeof input;\n    let v;\n    if (type === 'object') {\n      v = fromObject(input);\n    } else if (type === 'string') {\n      v = hexParse(input) || nameParse(input) || functionParse(input);\n    }\n    this._rgb = v;\n    this._valid = !!v;\n  }\n  get valid() {\n    return this._valid;\n  }\n  get rgb() {\n    var v = clone(this._rgb);\n    if (v) {\n      v.a = b2n(v.a);\n    }\n    return v;\n  }\n  set rgb(obj) {\n    this._rgb = fromObject(obj);\n  }\n  rgbString() {\n    return this._valid ? rgbString(this._rgb) : undefined;\n  }\n  hexString() {\n    return this._valid ? hexString(this._rgb) : undefined;\n  }\n  hslString() {\n    return this._valid ? hslString(this._rgb) : undefined;\n  }\n  mix(color, weight) {\n    if (color) {\n      const c1 = this.rgb;\n      const c2 = color.rgb;\n      let w2;\n      const p = weight === w2 ? 0.5 : weight;\n      const w = 2 * p - 1;\n      const a = c1.a - c2.a;\n      const w1 = ((w * a === -1 ? w : (w + a) / (1 + w * a)) + 1) / 2.0;\n      w2 = 1 - w1;\n      c1.r = 0xFF & w1 * c1.r + w2 * c2.r + 0.5;\n      c1.g = 0xFF & w1 * c1.g + w2 * c2.g + 0.5;\n      c1.b = 0xFF & w1 * c1.b + w2 * c2.b + 0.5;\n      c1.a = p * c1.a + (1 - p) * c2.a;\n      this.rgb = c1;\n    }\n    return this;\n  }\n  interpolate(color, t) {\n    if (color) {\n      this._rgb = interpolate(this._rgb, color._rgb, t);\n    }\n    return this;\n  }\n  clone() {\n    return new Color(this.rgb);\n  }\n  alpha(a) {\n    this._rgb.a = n2b(a);\n    return this;\n  }\n  clearer(ratio) {\n    const rgb = this._rgb;\n    rgb.a *= 1 - ratio;\n    return this;\n  }\n  greyscale() {\n    const rgb = this._rgb;\n    const val = round(rgb.r * 0.3 + rgb.g * 0.59 + rgb.b * 0.11);\n    rgb.r = rgb.g = rgb.b = val;\n    return this;\n  }\n  opaquer(ratio) {\n    const rgb = this._rgb;\n    rgb.a *= 1 + ratio;\n    return this;\n  }\n  negate() {\n    const v = this._rgb;\n    v.r = 255 - v.r;\n    v.g = 255 - v.g;\n    v.b = 255 - v.b;\n    return this;\n  }\n  lighten(ratio) {\n    modHSL(this._rgb, 2, ratio);\n    return this;\n  }\n  darken(ratio) {\n    modHSL(this._rgb, 2, -ratio);\n    return this;\n  }\n  saturate(ratio) {\n    modHSL(this._rgb, 1, ratio);\n    return this;\n  }\n  desaturate(ratio) {\n    modHSL(this._rgb, 1, -ratio);\n    return this;\n  }\n  rotate(deg) {\n    rotate(this._rgb, deg);\n    return this;\n  }\n}\nfunction index_esm(input) {\n  return new Color(input);\n}\n\nfunction isPatternOrGradient(value) {\n  if (value && typeof value === 'object') {\n    const type = value.toString();\n    return type === '[object CanvasPattern]' || type === '[object CanvasGradient]';\n  }\n  return false;\n}\nfunction color(value) {\n  return isPatternOrGradient(value) ? value : index_esm(value);\n}\nfunction getHoverColor(value) {\n  return isPatternOrGradient(value)\n    ? value\n    : index_esm(value).saturate(0.5).darken(0.1).hexString();\n}\n\nconst overrides = Object.create(null);\nconst descriptors = Object.create(null);\nfunction getScope$1(node, key) {\n  if (!key) {\n    return node;\n  }\n  const keys = key.split('.');\n  for (let i = 0, n = keys.length; i < n; ++i) {\n    const k = keys[i];\n    node = node[k] || (node[k] = Object.create(null));\n  }\n  return node;\n}\nfunction set(root, scope, values) {\n  if (typeof scope === 'string') {\n    return merge(getScope$1(root, scope), values);\n  }\n  return merge(getScope$1(root, ''), scope);\n}\nclass Defaults {\n  constructor(_descriptors) {\n    this.animation = undefined;\n    this.backgroundColor = 'rgba(0,0,0,0.1)';\n    this.borderColor = 'rgba(0,0,0,0.1)';\n    this.color = '#666';\n    this.datasets = {};\n    this.devicePixelRatio = (context) => context.chart.platform.getDevicePixelRatio();\n    this.elements = {};\n    this.events = [\n      'mousemove',\n      'mouseout',\n      'click',\n      'touchstart',\n      'touchmove'\n    ];\n    this.font = {\n      family: \"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif\",\n      size: 12,\n      style: 'normal',\n      lineHeight: 1.2,\n      weight: null\n    };\n    this.hover = {};\n    this.hoverBackgroundColor = (ctx, options) => getHoverColor(options.backgroundColor);\n    this.hoverBorderColor = (ctx, options) => getHoverColor(options.borderColor);\n    this.hoverColor = (ctx, options) => getHoverColor(options.color);\n    this.indexAxis = 'x';\n    this.interaction = {\n      mode: 'nearest',\n      intersect: true,\n      includeInvisible: false\n    };\n    this.maintainAspectRatio = true;\n    this.onHover = null;\n    this.onClick = null;\n    this.parsing = true;\n    this.plugins = {};\n    this.responsive = true;\n    this.scale = undefined;\n    this.scales = {};\n    this.showLine = true;\n    this.drawActiveElementsOnTop = true;\n    this.describe(_descriptors);\n  }\n  set(scope, values) {\n    return set(this, scope, values);\n  }\n  get(scope) {\n    return getScope$1(this, scope);\n  }\n  describe(scope, values) {\n    return set(descriptors, scope, values);\n  }\n  override(scope, values) {\n    return set(overrides, scope, values);\n  }\n  route(scope, name, targetScope, targetName) {\n    const scopeObject = getScope$1(this, scope);\n    const targetScopeObject = getScope$1(this, targetScope);\n    const privateName = '_' + name;\n    Object.defineProperties(scopeObject, {\n      [privateName]: {\n        value: scopeObject[name],\n        writable: true\n      },\n      [name]: {\n        enumerable: true,\n        get() {\n          const local = this[privateName];\n          const target = targetScopeObject[targetName];\n          if (isObject(local)) {\n            return Object.assign({}, target, local);\n          }\n          return valueOrDefault(local, target);\n        },\n        set(value) {\n          this[privateName] = value;\n        }\n      }\n    });\n  }\n}\nvar defaults = new Defaults({\n  _scriptable: (name) => !name.startsWith('on'),\n  _indexable: (name) => name !== 'events',\n  hover: {\n    _fallback: 'interaction'\n  },\n  interaction: {\n    _scriptable: false,\n    _indexable: false,\n  }\n});\n\nfunction toFontString(font) {\n  if (!font || isNullOrUndef(font.size) || isNullOrUndef(font.family)) {\n    return null;\n  }\n  return (font.style ? font.style + ' ' : '')\n\t\t+ (font.weight ? font.weight + ' ' : '')\n\t\t+ font.size + 'px '\n\t\t+ font.family;\n}\nfunction _measureText(ctx, data, gc, longest, string) {\n  let textWidth = data[string];\n  if (!textWidth) {\n    textWidth = data[string] = ctx.measureText(string).width;\n    gc.push(string);\n  }\n  if (textWidth > longest) {\n    longest = textWidth;\n  }\n  return longest;\n}\nfunction _longestText(ctx, font, arrayOfThings, cache) {\n  cache = cache || {};\n  let data = cache.data = cache.data || {};\n  let gc = cache.garbageCollect = cache.garbageCollect || [];\n  if (cache.font !== font) {\n    data = cache.data = {};\n    gc = cache.garbageCollect = [];\n    cache.font = font;\n  }\n  ctx.save();\n  ctx.font = font;\n  let longest = 0;\n  const ilen = arrayOfThings.length;\n  let i, j, jlen, thing, nestedThing;\n  for (i = 0; i < ilen; i++) {\n    thing = arrayOfThings[i];\n    if (thing !== undefined && thing !== null && isArray(thing) !== true) {\n      longest = _measureText(ctx, data, gc, longest, thing);\n    } else if (isArray(thing)) {\n      for (j = 0, jlen = thing.length; j < jlen; j++) {\n        nestedThing = thing[j];\n        if (nestedThing !== undefined && nestedThing !== null && !isArray(nestedThing)) {\n          longest = _measureText(ctx, data, gc, longest, nestedThing);\n        }\n      }\n    }\n  }\n  ctx.restore();\n  const gcLen = gc.length / 2;\n  if (gcLen > arrayOfThings.length) {\n    for (i = 0; i < gcLen; i++) {\n      delete data[gc[i]];\n    }\n    gc.splice(0, gcLen);\n  }\n  return longest;\n}\nfunction _alignPixel(chart, pixel, width) {\n  const devicePixelRatio = chart.currentDevicePixelRatio;\n  const halfWidth = width !== 0 ? Math.max(width / 2, 0.5) : 0;\n  return Math.round((pixel - halfWidth) * devicePixelRatio) / devicePixelRatio + halfWidth;\n}\nfunction clearCanvas(canvas, ctx) {\n  ctx = ctx || canvas.getContext('2d');\n  ctx.save();\n  ctx.resetTransform();\n  ctx.clearRect(0, 0, canvas.width, canvas.height);\n  ctx.restore();\n}\nfunction drawPoint(ctx, options, x, y) {\n  drawPointLegend(ctx, options, x, y, null);\n}\nfunction drawPointLegend(ctx, options, x, y, w) {\n  let type, xOffset, yOffset, size, cornerRadius, width;\n  const style = options.pointStyle;\n  const rotation = options.rotation;\n  const radius = options.radius;\n  let rad = (rotation || 0) * RAD_PER_DEG;\n  if (style && typeof style === 'object') {\n    type = style.toString();\n    if (type === '[object HTMLImageElement]' || type === '[object HTMLCanvasElement]') {\n      ctx.save();\n      ctx.translate(x, y);\n      ctx.rotate(rad);\n      ctx.drawImage(style, -style.width / 2, -style.height / 2, style.width, style.height);\n      ctx.restore();\n      return;\n    }\n  }\n  if (isNaN(radius) || radius <= 0) {\n    return;\n  }\n  ctx.beginPath();\n  switch (style) {\n  default:\n    if (w) {\n      ctx.ellipse(x, y, w / 2, radius, 0, 0, TAU);\n    } else {\n      ctx.arc(x, y, radius, 0, TAU);\n    }\n    ctx.closePath();\n    break;\n  case 'triangle':\n    ctx.moveTo(x + Math.sin(rad) * radius, y - Math.cos(rad) * radius);\n    rad += TWO_THIRDS_PI;\n    ctx.lineTo(x + Math.sin(rad) * radius, y - Math.cos(rad) * radius);\n    rad += TWO_THIRDS_PI;\n    ctx.lineTo(x + Math.sin(rad) * radius, y - Math.cos(rad) * radius);\n    ctx.closePath();\n    break;\n  case 'rectRounded':\n    cornerRadius = radius * 0.516;\n    size = radius - cornerRadius;\n    xOffset = Math.cos(rad + QUARTER_PI) * size;\n    yOffset = Math.sin(rad + QUARTER_PI) * size;\n    ctx.arc(x - xOffset, y - yOffset, cornerRadius, rad - PI, rad - HALF_PI);\n    ctx.arc(x + yOffset, y - xOffset, cornerRadius, rad - HALF_PI, rad);\n    ctx.arc(x + xOffset, y + yOffset, cornerRadius, rad, rad + HALF_PI);\n    ctx.arc(x - yOffset, y + xOffset, cornerRadius, rad + HALF_PI, rad + PI);\n    ctx.closePath();\n    break;\n  case 'rect':\n    if (!rotation) {\n      size = Math.SQRT1_2 * radius;\n      width = w ? w / 2 : size;\n      ctx.rect(x - width, y - size, 2 * width, 2 * size);\n      break;\n    }\n    rad += QUARTER_PI;\n  case 'rectRot':\n    xOffset = Math.cos(rad) * radius;\n    yOffset = Math.sin(rad) * radius;\n    ctx.moveTo(x - xOffset, y - yOffset);\n    ctx.lineTo(x + yOffset, y - xOffset);\n    ctx.lineTo(x + xOffset, y + yOffset);\n    ctx.lineTo(x - yOffset, y + xOffset);\n    ctx.closePath();\n    break;\n  case 'crossRot':\n    rad += QUARTER_PI;\n  case 'cross':\n    xOffset = Math.cos(rad) * radius;\n    yOffset = Math.sin(rad) * radius;\n    ctx.moveTo(x - xOffset, y - yOffset);\n    ctx.lineTo(x + xOffset, y + yOffset);\n    ctx.moveTo(x + yOffset, y - xOffset);\n    ctx.lineTo(x - yOffset, y + xOffset);\n    break;\n  case 'star':\n    xOffset = Math.cos(rad) * radius;\n    yOffset = Math.sin(rad) * radius;\n    ctx.moveTo(x - xOffset, y - yOffset);\n    ctx.lineTo(x + xOffset, y + yOffset);\n    ctx.moveTo(x + yOffset, y - xOffset);\n    ctx.lineTo(x - yOffset, y + xOffset);\n    rad += QUARTER_PI;\n    xOffset = Math.cos(rad) * radius;\n    yOffset = Math.sin(rad) * radius;\n    ctx.moveTo(x - xOffset, y - yOffset);\n    ctx.lineTo(x + xOffset, y + yOffset);\n    ctx.moveTo(x + yOffset, y - xOffset);\n    ctx.lineTo(x - yOffset, y + xOffset);\n    break;\n  case 'line':\n    xOffset = w ? w / 2 : Math.cos(rad) * radius;\n    yOffset = Math.sin(rad) * radius;\n    ctx.moveTo(x - xOffset, y - yOffset);\n    ctx.lineTo(x + xOffset, y + yOffset);\n    break;\n  case 'dash':\n    ctx.moveTo(x, y);\n    ctx.lineTo(x + Math.cos(rad) * radius, y + Math.sin(rad) * radius);\n    break;\n  }\n  ctx.fill();\n  if (options.borderWidth > 0) {\n    ctx.stroke();\n  }\n}\nfunction _isPointInArea(point, area, margin) {\n  margin = margin || 0.5;\n  return !area || (point && point.x > area.left - margin && point.x < area.right + margin &&\n\t\tpoint.y > area.top - margin && point.y < area.bottom + margin);\n}\nfunction clipArea(ctx, area) {\n  ctx.save();\n  ctx.beginPath();\n  ctx.rect(area.left, area.top, area.right - area.left, area.bottom - area.top);\n  ctx.clip();\n}\nfunction unclipArea(ctx) {\n  ctx.restore();\n}\nfunction _steppedLineTo(ctx, previous, target, flip, mode) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n  if (mode === 'middle') {\n    const midpoint = (previous.x + target.x) / 2.0;\n    ctx.lineTo(midpoint, previous.y);\n    ctx.lineTo(midpoint, target.y);\n  } else if (mode === 'after' !== !!flip) {\n    ctx.lineTo(previous.x, target.y);\n  } else {\n    ctx.lineTo(target.x, previous.y);\n  }\n  ctx.lineTo(target.x, target.y);\n}\nfunction _bezierCurveTo(ctx, previous, target, flip) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n  ctx.bezierCurveTo(\n    flip ? previous.cp1x : previous.cp2x,\n    flip ? previous.cp1y : previous.cp2y,\n    flip ? target.cp2x : target.cp1x,\n    flip ? target.cp2y : target.cp1y,\n    target.x,\n    target.y);\n}\nfunction renderText(ctx, text, x, y, font, opts = {}) {\n  const lines = isArray(text) ? text : [text];\n  const stroke = opts.strokeWidth > 0 && opts.strokeColor !== '';\n  let i, line;\n  ctx.save();\n  ctx.font = font.string;\n  setRenderOpts(ctx, opts);\n  for (i = 0; i < lines.length; ++i) {\n    line = lines[i];\n    if (stroke) {\n      if (opts.strokeColor) {\n        ctx.strokeStyle = opts.strokeColor;\n      }\n      if (!isNullOrUndef(opts.strokeWidth)) {\n        ctx.lineWidth = opts.strokeWidth;\n      }\n      ctx.strokeText(line, x, y, opts.maxWidth);\n    }\n    ctx.fillText(line, x, y, opts.maxWidth);\n    decorateText(ctx, x, y, line, opts);\n    y += font.lineHeight;\n  }\n  ctx.restore();\n}\nfunction setRenderOpts(ctx, opts) {\n  if (opts.translation) {\n    ctx.translate(opts.translation[0], opts.translation[1]);\n  }\n  if (!isNullOrUndef(opts.rotation)) {\n    ctx.rotate(opts.rotation);\n  }\n  if (opts.color) {\n    ctx.fillStyle = opts.color;\n  }\n  if (opts.textAlign) {\n    ctx.textAlign = opts.textAlign;\n  }\n  if (opts.textBaseline) {\n    ctx.textBaseline = opts.textBaseline;\n  }\n}\nfunction decorateText(ctx, x, y, line, opts) {\n  if (opts.strikethrough || opts.underline) {\n    const metrics = ctx.measureText(line);\n    const left = x - metrics.actualBoundingBoxLeft;\n    const right = x + metrics.actualBoundingBoxRight;\n    const top = y - metrics.actualBoundingBoxAscent;\n    const bottom = y + metrics.actualBoundingBoxDescent;\n    const yDecoration = opts.strikethrough ? (top + bottom) / 2 : bottom;\n    ctx.strokeStyle = ctx.fillStyle;\n    ctx.beginPath();\n    ctx.lineWidth = opts.decorationWidth || 2;\n    ctx.moveTo(left, yDecoration);\n    ctx.lineTo(right, yDecoration);\n    ctx.stroke();\n  }\n}\nfunction addRoundedRectPath(ctx, rect) {\n  const {x, y, w, h, radius} = rect;\n  ctx.arc(x + radius.topLeft, y + radius.topLeft, radius.topLeft, -HALF_PI, PI, true);\n  ctx.lineTo(x, y + h - radius.bottomLeft);\n  ctx.arc(x + radius.bottomLeft, y + h - radius.bottomLeft, radius.bottomLeft, PI, HALF_PI, true);\n  ctx.lineTo(x + w - radius.bottomRight, y + h);\n  ctx.arc(x + w - radius.bottomRight, y + h - radius.bottomRight, radius.bottomRight, HALF_PI, 0, true);\n  ctx.lineTo(x + w, y + radius.topRight);\n  ctx.arc(x + w - radius.topRight, y + radius.topRight, radius.topRight, 0, -HALF_PI, true);\n  ctx.lineTo(x + radius.topLeft, y);\n}\n\nconst LINE_HEIGHT = new RegExp(/^(normal|(\\d+(?:\\.\\d+)?)(px|em|%)?)$/);\nconst FONT_STYLE = new RegExp(/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/);\nfunction toLineHeight(value, size) {\n  const matches = ('' + value).match(LINE_HEIGHT);\n  if (!matches || matches[1] === 'normal') {\n    return size * 1.2;\n  }\n  value = +matches[2];\n  switch (matches[3]) {\n  case 'px':\n    return value;\n  case '%':\n    value /= 100;\n    break;\n  }\n  return size * value;\n}\nconst numberOrZero = v => +v || 0;\nfunction _readValueToProps(value, props) {\n  const ret = {};\n  const objProps = isObject(props);\n  const keys = objProps ? Object.keys(props) : props;\n  const read = isObject(value)\n    ? objProps\n      ? prop => valueOrDefault(value[prop], value[props[prop]])\n      : prop => value[prop]\n    : () => value;\n  for (const prop of keys) {\n    ret[prop] = numberOrZero(read(prop));\n  }\n  return ret;\n}\nfunction toTRBL(value) {\n  return _readValueToProps(value, {top: 'y', right: 'x', bottom: 'y', left: 'x'});\n}\nfunction toTRBLCorners(value) {\n  return _readValueToProps(value, ['topLeft', 'topRight', 'bottomLeft', 'bottomRight']);\n}\nfunction toPadding(value) {\n  const obj = toTRBL(value);\n  obj.width = obj.left + obj.right;\n  obj.height = obj.top + obj.bottom;\n  return obj;\n}\nfunction toFont(options, fallback) {\n  options = options || {};\n  fallback = fallback || defaults.font;\n  let size = valueOrDefault(options.size, fallback.size);\n  if (typeof size === 'string') {\n    size = parseInt(size, 10);\n  }\n  let style = valueOrDefault(options.style, fallback.style);\n  if (style && !('' + style).match(FONT_STYLE)) {\n    console.warn('Invalid font style specified: \"' + style + '\"');\n    style = '';\n  }\n  const font = {\n    family: valueOrDefault(options.family, fallback.family),\n    lineHeight: toLineHeight(valueOrDefault(options.lineHeight, fallback.lineHeight), size),\n    size,\n    style,\n    weight: valueOrDefault(options.weight, fallback.weight),\n    string: ''\n  };\n  font.string = toFontString(font);\n  return font;\n}\nfunction resolve(inputs, context, index, info) {\n  let cacheable = true;\n  let i, ilen, value;\n  for (i = 0, ilen = inputs.length; i < ilen; ++i) {\n    value = inputs[i];\n    if (value === undefined) {\n      continue;\n    }\n    if (context !== undefined && typeof value === 'function') {\n      value = value(context);\n      cacheable = false;\n    }\n    if (index !== undefined && isArray(value)) {\n      value = value[index % value.length];\n      cacheable = false;\n    }\n    if (value !== undefined) {\n      if (info && !cacheable) {\n        info.cacheable = false;\n      }\n      return value;\n    }\n  }\n}\nfunction _addGrace(minmax, grace, beginAtZero) {\n  const {min, max} = minmax;\n  const change = toDimension(grace, (max - min) / 2);\n  const keepZero = (value, add) => beginAtZero && value === 0 ? 0 : value + add;\n  return {\n    min: keepZero(min, -Math.abs(change)),\n    max: keepZero(max, change)\n  };\n}\nfunction createContext(parentContext, context) {\n  return Object.assign(Object.create(parentContext), context);\n}\n\nfunction _createResolver(scopes, prefixes = [''], rootScopes = scopes, fallback, getTarget = () => scopes[0]) {\n  if (!defined(fallback)) {\n    fallback = _resolve('_fallback', scopes);\n  }\n  const cache = {\n    [Symbol.toStringTag]: 'Object',\n    _cacheable: true,\n    _scopes: scopes,\n    _rootScopes: rootScopes,\n    _fallback: fallback,\n    _getTarget: getTarget,\n    override: (scope) => _createResolver([scope, ...scopes], prefixes, rootScopes, fallback),\n  };\n  return new Proxy(cache, {\n    deleteProperty(target, prop) {\n      delete target[prop];\n      delete target._keys;\n      delete scopes[0][prop];\n      return true;\n    },\n    get(target, prop) {\n      return _cached(target, prop,\n        () => _resolveWithPrefixes(prop, prefixes, scopes, target));\n    },\n    getOwnPropertyDescriptor(target, prop) {\n      return Reflect.getOwnPropertyDescriptor(target._scopes[0], prop);\n    },\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(scopes[0]);\n    },\n    has(target, prop) {\n      return getKeysFromAllScopes(target).includes(prop);\n    },\n    ownKeys(target) {\n      return getKeysFromAllScopes(target);\n    },\n    set(target, prop, value) {\n      const storage = target._storage || (target._storage = getTarget());\n      target[prop] = storage[prop] = value;\n      delete target._keys;\n      return true;\n    }\n  });\n}\nfunction _attachContext(proxy, context, subProxy, descriptorDefaults) {\n  const cache = {\n    _cacheable: false,\n    _proxy: proxy,\n    _context: context,\n    _subProxy: subProxy,\n    _stack: new Set(),\n    _descriptors: _descriptors(proxy, descriptorDefaults),\n    setContext: (ctx) => _attachContext(proxy, ctx, subProxy, descriptorDefaults),\n    override: (scope) => _attachContext(proxy.override(scope), context, subProxy, descriptorDefaults)\n  };\n  return new Proxy(cache, {\n    deleteProperty(target, prop) {\n      delete target[prop];\n      delete proxy[prop];\n      return true;\n    },\n    get(target, prop, receiver) {\n      return _cached(target, prop,\n        () => _resolveWithContext(target, prop, receiver));\n    },\n    getOwnPropertyDescriptor(target, prop) {\n      return target._descriptors.allKeys\n        ? Reflect.has(proxy, prop) ? {enumerable: true, configurable: true} : undefined\n        : Reflect.getOwnPropertyDescriptor(proxy, prop);\n    },\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(proxy);\n    },\n    has(target, prop) {\n      return Reflect.has(proxy, prop);\n    },\n    ownKeys() {\n      return Reflect.ownKeys(proxy);\n    },\n    set(target, prop, value) {\n      proxy[prop] = value;\n      delete target[prop];\n      return true;\n    }\n  });\n}\nfunction _descriptors(proxy, defaults = {scriptable: true, indexable: true}) {\n  const {_scriptable = defaults.scriptable, _indexable = defaults.indexable, _allKeys = defaults.allKeys} = proxy;\n  return {\n    allKeys: _allKeys,\n    scriptable: _scriptable,\n    indexable: _indexable,\n    isScriptable: isFunction(_scriptable) ? _scriptable : () => _scriptable,\n    isIndexable: isFunction(_indexable) ? _indexable : () => _indexable\n  };\n}\nconst readKey = (prefix, name) => prefix ? prefix + _capitalize(name) : name;\nconst needsSubResolver = (prop, value) => isObject(value) && prop !== 'adapters' &&\n  (Object.getPrototypeOf(value) === null || value.constructor === Object);\nfunction _cached(target, prop, resolve) {\n  if (Object.prototype.hasOwnProperty.call(target, prop)) {\n    return target[prop];\n  }\n  const value = resolve();\n  target[prop] = value;\n  return value;\n}\nfunction _resolveWithContext(target, prop, receiver) {\n  const {_proxy, _context, _subProxy, _descriptors: descriptors} = target;\n  let value = _proxy[prop];\n  if (isFunction(value) && descriptors.isScriptable(prop)) {\n    value = _resolveScriptable(prop, value, target, receiver);\n  }\n  if (isArray(value) && value.length) {\n    value = _resolveArray(prop, value, target, descriptors.isIndexable);\n  }\n  if (needsSubResolver(prop, value)) {\n    value = _attachContext(value, _context, _subProxy && _subProxy[prop], descriptors);\n  }\n  return value;\n}\nfunction _resolveScriptable(prop, value, target, receiver) {\n  const {_proxy, _context, _subProxy, _stack} = target;\n  if (_stack.has(prop)) {\n    throw new Error('Recursion detected: ' + Array.from(_stack).join('->') + '->' + prop);\n  }\n  _stack.add(prop);\n  value = value(_context, _subProxy || receiver);\n  _stack.delete(prop);\n  if (needsSubResolver(prop, value)) {\n    value = createSubResolver(_proxy._scopes, _proxy, prop, value);\n  }\n  return value;\n}\nfunction _resolveArray(prop, value, target, isIndexable) {\n  const {_proxy, _context, _subProxy, _descriptors: descriptors} = target;\n  if (defined(_context.index) && isIndexable(prop)) {\n    value = value[_context.index % value.length];\n  } else if (isObject(value[0])) {\n    const arr = value;\n    const scopes = _proxy._scopes.filter(s => s !== arr);\n    value = [];\n    for (const item of arr) {\n      const resolver = createSubResolver(scopes, _proxy, prop, item);\n      value.push(_attachContext(resolver, _context, _subProxy && _subProxy[prop], descriptors));\n    }\n  }\n  return value;\n}\nfunction resolveFallback(fallback, prop, value) {\n  return isFunction(fallback) ? fallback(prop, value) : fallback;\n}\nconst getScope = (key, parent) => key === true ? parent\n  : typeof key === 'string' ? resolveObjectKey(parent, key) : undefined;\nfunction addScopes(set, parentScopes, key, parentFallback, value) {\n  for (const parent of parentScopes) {\n    const scope = getScope(key, parent);\n    if (scope) {\n      set.add(scope);\n      const fallback = resolveFallback(scope._fallback, key, value);\n      if (defined(fallback) && fallback !== key && fallback !== parentFallback) {\n        return fallback;\n      }\n    } else if (scope === false && defined(parentFallback) && key !== parentFallback) {\n      return null;\n    }\n  }\n  return false;\n}\nfunction createSubResolver(parentScopes, resolver, prop, value) {\n  const rootScopes = resolver._rootScopes;\n  const fallback = resolveFallback(resolver._fallback, prop, value);\n  const allScopes = [...parentScopes, ...rootScopes];\n  const set = new Set();\n  set.add(value);\n  let key = addScopesFromKey(set, allScopes, prop, fallback || prop, value);\n  if (key === null) {\n    return false;\n  }\n  if (defined(fallback) && fallback !== prop) {\n    key = addScopesFromKey(set, allScopes, fallback, key, value);\n    if (key === null) {\n      return false;\n    }\n  }\n  return _createResolver(Array.from(set), [''], rootScopes, fallback,\n    () => subGetTarget(resolver, prop, value));\n}\nfunction addScopesFromKey(set, allScopes, key, fallback, item) {\n  while (key) {\n    key = addScopes(set, allScopes, key, fallback, item);\n  }\n  return key;\n}\nfunction subGetTarget(resolver, prop, value) {\n  const parent = resolver._getTarget();\n  if (!(prop in parent)) {\n    parent[prop] = {};\n  }\n  const target = parent[prop];\n  if (isArray(target) && isObject(value)) {\n    return value;\n  }\n  return target;\n}\nfunction _resolveWithPrefixes(prop, prefixes, scopes, proxy) {\n  let value;\n  for (const prefix of prefixes) {\n    value = _resolve(readKey(prefix, prop), scopes);\n    if (defined(value)) {\n      return needsSubResolver(prop, value)\n        ? createSubResolver(scopes, proxy, prop, value)\n        : value;\n    }\n  }\n}\nfunction _resolve(key, scopes) {\n  for (const scope of scopes) {\n    if (!scope) {\n      continue;\n    }\n    const value = scope[key];\n    if (defined(value)) {\n      return value;\n    }\n  }\n}\nfunction getKeysFromAllScopes(target) {\n  let keys = target._keys;\n  if (!keys) {\n    keys = target._keys = resolveKeysFromAllScopes(target._scopes);\n  }\n  return keys;\n}\nfunction resolveKeysFromAllScopes(scopes) {\n  const set = new Set();\n  for (const scope of scopes) {\n    for (const key of Object.keys(scope).filter(k => !k.startsWith('_'))) {\n      set.add(key);\n    }\n  }\n  return Array.from(set);\n}\nfunction _parseObjectDataRadialScale(meta, data, start, count) {\n  const {iScale} = meta;\n  const {key = 'r'} = this._parsing;\n  const parsed = new Array(count);\n  let i, ilen, index, item;\n  for (i = 0, ilen = count; i < ilen; ++i) {\n    index = i + start;\n    item = data[index];\n    parsed[i] = {\n      r: iScale.parse(resolveObjectKey(item, key), index)\n    };\n  }\n  return parsed;\n}\n\nconst EPSILON = Number.EPSILON || 1e-14;\nconst getPoint = (points, i) => i < points.length && !points[i].skip && points[i];\nconst getValueAxis = (indexAxis) => indexAxis === 'x' ? 'y' : 'x';\nfunction splineCurve(firstPoint, middlePoint, afterPoint, t) {\n  const previous = firstPoint.skip ? middlePoint : firstPoint;\n  const current = middlePoint;\n  const next = afterPoint.skip ? middlePoint : afterPoint;\n  const d01 = distanceBetweenPoints(current, previous);\n  const d12 = distanceBetweenPoints(next, current);\n  let s01 = d01 / (d01 + d12);\n  let s12 = d12 / (d01 + d12);\n  s01 = isNaN(s01) ? 0 : s01;\n  s12 = isNaN(s12) ? 0 : s12;\n  const fa = t * s01;\n  const fb = t * s12;\n  return {\n    previous: {\n      x: current.x - fa * (next.x - previous.x),\n      y: current.y - fa * (next.y - previous.y)\n    },\n    next: {\n      x: current.x + fb * (next.x - previous.x),\n      y: current.y + fb * (next.y - previous.y)\n    }\n  };\n}\nfunction monotoneAdjust(points, deltaK, mK) {\n  const pointsLen = points.length;\n  let alphaK, betaK, tauK, squaredMagnitude, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n  for (let i = 0; i < pointsLen - 1; ++i) {\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent || !pointAfter) {\n      continue;\n    }\n    if (almostEquals(deltaK[i], 0, EPSILON)) {\n      mK[i] = mK[i + 1] = 0;\n      continue;\n    }\n    alphaK = mK[i] / deltaK[i];\n    betaK = mK[i + 1] / deltaK[i];\n    squaredMagnitude = Math.pow(alphaK, 2) + Math.pow(betaK, 2);\n    if (squaredMagnitude <= 9) {\n      continue;\n    }\n    tauK = 3 / Math.sqrt(squaredMagnitude);\n    mK[i] = alphaK * tauK * deltaK[i];\n    mK[i + 1] = betaK * tauK * deltaK[i];\n  }\n}\nfunction monotoneCompute(points, mK, indexAxis = 'x') {\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  let delta, pointBefore, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n  for (let i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent) {\n      continue;\n    }\n    const iPixel = pointCurrent[indexAxis];\n    const vPixel = pointCurrent[valueAxis];\n    if (pointBefore) {\n      delta = (iPixel - pointBefore[indexAxis]) / 3;\n      pointCurrent[`cp1${indexAxis}`] = iPixel - delta;\n      pointCurrent[`cp1${valueAxis}`] = vPixel - delta * mK[i];\n    }\n    if (pointAfter) {\n      delta = (pointAfter[indexAxis] - iPixel) / 3;\n      pointCurrent[`cp2${indexAxis}`] = iPixel + delta;\n      pointCurrent[`cp2${valueAxis}`] = vPixel + delta * mK[i];\n    }\n  }\n}\nfunction splineCurveMonotone(points, indexAxis = 'x') {\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  const deltaK = Array(pointsLen).fill(0);\n  const mK = Array(pointsLen);\n  let i, pointBefore, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n  for (i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent) {\n      continue;\n    }\n    if (pointAfter) {\n      const slopeDelta = pointAfter[indexAxis] - pointCurrent[indexAxis];\n      deltaK[i] = slopeDelta !== 0 ? (pointAfter[valueAxis] - pointCurrent[valueAxis]) / slopeDelta : 0;\n    }\n    mK[i] = !pointBefore ? deltaK[i]\n      : !pointAfter ? deltaK[i - 1]\n      : (sign(deltaK[i - 1]) !== sign(deltaK[i])) ? 0\n      : (deltaK[i - 1] + deltaK[i]) / 2;\n  }\n  monotoneAdjust(points, deltaK, mK);\n  monotoneCompute(points, mK, indexAxis);\n}\nfunction capControlPoint(pt, min, max) {\n  return Math.max(Math.min(pt, max), min);\n}\nfunction capBezierPoints(points, area) {\n  let i, ilen, point, inArea, inAreaPrev;\n  let inAreaNext = _isPointInArea(points[0], area);\n  for (i = 0, ilen = points.length; i < ilen; ++i) {\n    inAreaPrev = inArea;\n    inArea = inAreaNext;\n    inAreaNext = i < ilen - 1 && _isPointInArea(points[i + 1], area);\n    if (!inArea) {\n      continue;\n    }\n    point = points[i];\n    if (inAreaPrev) {\n      point.cp1x = capControlPoint(point.cp1x, area.left, area.right);\n      point.cp1y = capControlPoint(point.cp1y, area.top, area.bottom);\n    }\n    if (inAreaNext) {\n      point.cp2x = capControlPoint(point.cp2x, area.left, area.right);\n      point.cp2y = capControlPoint(point.cp2y, area.top, area.bottom);\n    }\n  }\n}\nfunction _updateBezierControlPoints(points, options, area, loop, indexAxis) {\n  let i, ilen, point, controlPoints;\n  if (options.spanGaps) {\n    points = points.filter((pt) => !pt.skip);\n  }\n  if (options.cubicInterpolationMode === 'monotone') {\n    splineCurveMonotone(points, indexAxis);\n  } else {\n    let prev = loop ? points[points.length - 1] : points[0];\n    for (i = 0, ilen = points.length; i < ilen; ++i) {\n      point = points[i];\n      controlPoints = splineCurve(\n        prev,\n        point,\n        points[Math.min(i + 1, ilen - (loop ? 0 : 1)) % ilen],\n        options.tension\n      );\n      point.cp1x = controlPoints.previous.x;\n      point.cp1y = controlPoints.previous.y;\n      point.cp2x = controlPoints.next.x;\n      point.cp2y = controlPoints.next.y;\n      prev = point;\n    }\n  }\n  if (options.capBezierPoints) {\n    capBezierPoints(points, area);\n  }\n}\n\nfunction _isDomSupported() {\n  return typeof window !== 'undefined' && typeof document !== 'undefined';\n}\nfunction _getParentNode(domNode) {\n  let parent = domNode.parentNode;\n  if (parent && parent.toString() === '[object ShadowRoot]') {\n    parent = parent.host;\n  }\n  return parent;\n}\nfunction parseMaxStyle(styleValue, node, parentProperty) {\n  let valueInPixels;\n  if (typeof styleValue === 'string') {\n    valueInPixels = parseInt(styleValue, 10);\n    if (styleValue.indexOf('%') !== -1) {\n      valueInPixels = valueInPixels / 100 * node.parentNode[parentProperty];\n    }\n  } else {\n    valueInPixels = styleValue;\n  }\n  return valueInPixels;\n}\nconst getComputedStyle = (element) => window.getComputedStyle(element, null);\nfunction getStyle(el, property) {\n  return getComputedStyle(el).getPropertyValue(property);\n}\nconst positions = ['top', 'right', 'bottom', 'left'];\nfunction getPositionedStyle(styles, style, suffix) {\n  const result = {};\n  suffix = suffix ? '-' + suffix : '';\n  for (let i = 0; i < 4; i++) {\n    const pos = positions[i];\n    result[pos] = parseFloat(styles[style + '-' + pos + suffix]) || 0;\n  }\n  result.width = result.left + result.right;\n  result.height = result.top + result.bottom;\n  return result;\n}\nconst useOffsetPos = (x, y, target) => (x > 0 || y > 0) && (!target || !target.shadowRoot);\nfunction getCanvasPosition(e, canvas) {\n  const touches = e.touches;\n  const source = touches && touches.length ? touches[0] : e;\n  const {offsetX, offsetY} = source;\n  let box = false;\n  let x, y;\n  if (useOffsetPos(offsetX, offsetY, e.target)) {\n    x = offsetX;\n    y = offsetY;\n  } else {\n    const rect = canvas.getBoundingClientRect();\n    x = source.clientX - rect.left;\n    y = source.clientY - rect.top;\n    box = true;\n  }\n  return {x, y, box};\n}\nfunction getRelativePosition(evt, chart) {\n  if ('native' in evt) {\n    return evt;\n  }\n  const {canvas, currentDevicePixelRatio} = chart;\n  const style = getComputedStyle(canvas);\n  const borderBox = style.boxSizing === 'border-box';\n  const paddings = getPositionedStyle(style, 'padding');\n  const borders = getPositionedStyle(style, 'border', 'width');\n  const {x, y, box} = getCanvasPosition(evt, canvas);\n  const xOffset = paddings.left + (box && borders.left);\n  const yOffset = paddings.top + (box && borders.top);\n  let {width, height} = chart;\n  if (borderBox) {\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n  return {\n    x: Math.round((x - xOffset) / width * canvas.width / currentDevicePixelRatio),\n    y: Math.round((y - yOffset) / height * canvas.height / currentDevicePixelRatio)\n  };\n}\nfunction getContainerSize(canvas, width, height) {\n  let maxWidth, maxHeight;\n  if (width === undefined || height === undefined) {\n    const container = _getParentNode(canvas);\n    if (!container) {\n      width = canvas.clientWidth;\n      height = canvas.clientHeight;\n    } else {\n      const rect = container.getBoundingClientRect();\n      const containerStyle = getComputedStyle(container);\n      const containerBorder = getPositionedStyle(containerStyle, 'border', 'width');\n      const containerPadding = getPositionedStyle(containerStyle, 'padding');\n      width = rect.width - containerPadding.width - containerBorder.width;\n      height = rect.height - containerPadding.height - containerBorder.height;\n      maxWidth = parseMaxStyle(containerStyle.maxWidth, container, 'clientWidth');\n      maxHeight = parseMaxStyle(containerStyle.maxHeight, container, 'clientHeight');\n    }\n  }\n  return {\n    width,\n    height,\n    maxWidth: maxWidth || INFINITY,\n    maxHeight: maxHeight || INFINITY\n  };\n}\nconst round1 = v => Math.round(v * 10) / 10;\nfunction getMaximumSize(canvas, bbWidth, bbHeight, aspectRatio) {\n  const style = getComputedStyle(canvas);\n  const margins = getPositionedStyle(style, 'margin');\n  const maxWidth = parseMaxStyle(style.maxWidth, canvas, 'clientWidth') || INFINITY;\n  const maxHeight = parseMaxStyle(style.maxHeight, canvas, 'clientHeight') || INFINITY;\n  const containerSize = getContainerSize(canvas, bbWidth, bbHeight);\n  let {width, height} = containerSize;\n  if (style.boxSizing === 'content-box') {\n    const borders = getPositionedStyle(style, 'border', 'width');\n    const paddings = getPositionedStyle(style, 'padding');\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n  width = Math.max(0, width - margins.width);\n  height = Math.max(0, aspectRatio ? Math.floor(width / aspectRatio) : height - margins.height);\n  width = round1(Math.min(width, maxWidth, containerSize.maxWidth));\n  height = round1(Math.min(height, maxHeight, containerSize.maxHeight));\n  if (width && !height) {\n    height = round1(width / 2);\n  }\n  return {\n    width,\n    height\n  };\n}\nfunction retinaScale(chart, forceRatio, forceStyle) {\n  const pixelRatio = forceRatio || 1;\n  const deviceHeight = Math.floor(chart.height * pixelRatio);\n  const deviceWidth = Math.floor(chart.width * pixelRatio);\n  chart.height = deviceHeight / pixelRatio;\n  chart.width = deviceWidth / pixelRatio;\n  const canvas = chart.canvas;\n  if (canvas.style && (forceStyle || (!canvas.style.height && !canvas.style.width))) {\n    canvas.style.height = `${chart.height}px`;\n    canvas.style.width = `${chart.width}px`;\n  }\n  if (chart.currentDevicePixelRatio !== pixelRatio\n      || canvas.height !== deviceHeight\n      || canvas.width !== deviceWidth) {\n    chart.currentDevicePixelRatio = pixelRatio;\n    canvas.height = deviceHeight;\n    canvas.width = deviceWidth;\n    chart.ctx.setTransform(pixelRatio, 0, 0, pixelRatio, 0, 0);\n    return true;\n  }\n  return false;\n}\nconst supportsEventListenerOptions = (function() {\n  let passiveSupported = false;\n  try {\n    const options = {\n      get passive() {\n        passiveSupported = true;\n        return false;\n      }\n    };\n    window.addEventListener('test', null, options);\n    window.removeEventListener('test', null, options);\n  } catch (e) {\n  }\n  return passiveSupported;\n}());\nfunction readUsedSize(element, property) {\n  const value = getStyle(element, property);\n  const matches = value && value.match(/^(\\d+)(\\.\\d+)?px$/);\n  return matches ? +matches[1] : undefined;\n}\n\nfunction _pointInLine(p1, p2, t, mode) {\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: p1.y + t * (p2.y - p1.y)\n  };\n}\nfunction _steppedInterpolation(p1, p2, t, mode) {\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: mode === 'middle' ? t < 0.5 ? p1.y : p2.y\n    : mode === 'after' ? t < 1 ? p1.y : p2.y\n    : t > 0 ? p2.y : p1.y\n  };\n}\nfunction _bezierInterpolation(p1, p2, t, mode) {\n  const cp1 = {x: p1.cp2x, y: p1.cp2y};\n  const cp2 = {x: p2.cp1x, y: p2.cp1y};\n  const a = _pointInLine(p1, cp1, t);\n  const b = _pointInLine(cp1, cp2, t);\n  const c = _pointInLine(cp2, p2, t);\n  const d = _pointInLine(a, b, t);\n  const e = _pointInLine(b, c, t);\n  return _pointInLine(d, e, t);\n}\n\nconst intlCache = new Map();\nfunction getNumberFormat(locale, options) {\n  options = options || {};\n  const cacheKey = locale + JSON.stringify(options);\n  let formatter = intlCache.get(cacheKey);\n  if (!formatter) {\n    formatter = new Intl.NumberFormat(locale, options);\n    intlCache.set(cacheKey, formatter);\n  }\n  return formatter;\n}\nfunction formatNumber(num, locale, options) {\n  return getNumberFormat(locale, options).format(num);\n}\n\nconst getRightToLeftAdapter = function(rectX, width) {\n  return {\n    x(x) {\n      return rectX + rectX + width - x;\n    },\n    setWidth(w) {\n      width = w;\n    },\n    textAlign(align) {\n      if (align === 'center') {\n        return align;\n      }\n      return align === 'right' ? 'left' : 'right';\n    },\n    xPlus(x, value) {\n      return x - value;\n    },\n    leftForLtr(x, itemWidth) {\n      return x - itemWidth;\n    },\n  };\n};\nconst getLeftToRightAdapter = function() {\n  return {\n    x(x) {\n      return x;\n    },\n    setWidth(w) {\n    },\n    textAlign(align) {\n      return align;\n    },\n    xPlus(x, value) {\n      return x + value;\n    },\n    leftForLtr(x, _itemWidth) {\n      return x;\n    },\n  };\n};\nfunction getRtlAdapter(rtl, rectX, width) {\n  return rtl ? getRightToLeftAdapter(rectX, width) : getLeftToRightAdapter();\n}\nfunction overrideTextDirection(ctx, direction) {\n  let style, original;\n  if (direction === 'ltr' || direction === 'rtl') {\n    style = ctx.canvas.style;\n    original = [\n      style.getPropertyValue('direction'),\n      style.getPropertyPriority('direction'),\n    ];\n    style.setProperty('direction', direction, 'important');\n    ctx.prevTextDirection = original;\n  }\n}\nfunction restoreTextDirection(ctx, original) {\n  if (original !== undefined) {\n    delete ctx.prevTextDirection;\n    ctx.canvas.style.setProperty('direction', original[0], original[1]);\n  }\n}\n\nfunction propertyFn(property) {\n  if (property === 'angle') {\n    return {\n      between: _angleBetween,\n      compare: _angleDiff,\n      normalize: _normalizeAngle,\n    };\n  }\n  return {\n    between: _isBetween,\n    compare: (a, b) => a - b,\n    normalize: x => x\n  };\n}\nfunction normalizeSegment({start, end, count, loop, style}) {\n  return {\n    start: start % count,\n    end: end % count,\n    loop: loop && (end - start + 1) % count === 0,\n    style\n  };\n}\nfunction getSegment(segment, points, bounds) {\n  const {property, start: startBound, end: endBound} = bounds;\n  const {between, normalize} = propertyFn(property);\n  const count = points.length;\n  let {start, end, loop} = segment;\n  let i, ilen;\n  if (loop) {\n    start += count;\n    end += count;\n    for (i = 0, ilen = count; i < ilen; ++i) {\n      if (!between(normalize(points[start % count][property]), startBound, endBound)) {\n        break;\n      }\n      start--;\n      end--;\n    }\n    start %= count;\n    end %= count;\n  }\n  if (end < start) {\n    end += count;\n  }\n  return {start, end, loop, style: segment.style};\n}\nfunction _boundSegment(segment, points, bounds) {\n  if (!bounds) {\n    return [segment];\n  }\n  const {property, start: startBound, end: endBound} = bounds;\n  const count = points.length;\n  const {compare, between, normalize} = propertyFn(property);\n  const {start, end, loop, style} = getSegment(segment, points, bounds);\n  const result = [];\n  let inside = false;\n  let subStart = null;\n  let value, point, prevValue;\n  const startIsBefore = () => between(startBound, prevValue, value) && compare(startBound, prevValue) !== 0;\n  const endIsBefore = () => compare(endBound, value) === 0 || between(endBound, prevValue, value);\n  const shouldStart = () => inside || startIsBefore();\n  const shouldStop = () => !inside || endIsBefore();\n  for (let i = start, prev = start; i <= end; ++i) {\n    point = points[i % count];\n    if (point.skip) {\n      continue;\n    }\n    value = normalize(point[property]);\n    if (value === prevValue) {\n      continue;\n    }\n    inside = between(value, startBound, endBound);\n    if (subStart === null && shouldStart()) {\n      subStart = compare(value, startBound) === 0 ? i : prev;\n    }\n    if (subStart !== null && shouldStop()) {\n      result.push(normalizeSegment({start: subStart, end: i, loop, count, style}));\n      subStart = null;\n    }\n    prev = i;\n    prevValue = value;\n  }\n  if (subStart !== null) {\n    result.push(normalizeSegment({start: subStart, end, loop, count, style}));\n  }\n  return result;\n}\nfunction _boundSegments(line, bounds) {\n  const result = [];\n  const segments = line.segments;\n  for (let i = 0; i < segments.length; i++) {\n    const sub = _boundSegment(segments[i], line.points, bounds);\n    if (sub.length) {\n      result.push(...sub);\n    }\n  }\n  return result;\n}\nfunction findStartAndEnd(points, count, loop, spanGaps) {\n  let start = 0;\n  let end = count - 1;\n  if (loop && !spanGaps) {\n    while (start < count && !points[start].skip) {\n      start++;\n    }\n  }\n  while (start < count && points[start].skip) {\n    start++;\n  }\n  start %= count;\n  if (loop) {\n    end += start;\n  }\n  while (end > start && points[end % count].skip) {\n    end--;\n  }\n  end %= count;\n  return {start, end};\n}\nfunction solidSegments(points, start, max, loop) {\n  const count = points.length;\n  const result = [];\n  let last = start;\n  let prev = points[start];\n  let end;\n  for (end = start + 1; end <= max; ++end) {\n    const cur = points[end % count];\n    if (cur.skip || cur.stop) {\n      if (!prev.skip) {\n        loop = false;\n        result.push({start: start % count, end: (end - 1) % count, loop});\n        start = last = cur.stop ? end : null;\n      }\n    } else {\n      last = end;\n      if (prev.skip) {\n        start = end;\n      }\n    }\n    prev = cur;\n  }\n  if (last !== null) {\n    result.push({start: start % count, end: last % count, loop});\n  }\n  return result;\n}\nfunction _computeSegments(line, segmentOptions) {\n  const points = line.points;\n  const spanGaps = line.options.spanGaps;\n  const count = points.length;\n  if (!count) {\n    return [];\n  }\n  const loop = !!line._loop;\n  const {start, end} = findStartAndEnd(points, count, loop, spanGaps);\n  if (spanGaps === true) {\n    return splitByStyles(line, [{start, end, loop}], points, segmentOptions);\n  }\n  const max = end < start ? end + count : end;\n  const completeLoop = !!line._fullLoop && start === 0 && end === count - 1;\n  return splitByStyles(line, solidSegments(points, start, max, completeLoop), points, segmentOptions);\n}\nfunction splitByStyles(line, segments, points, segmentOptions) {\n  if (!segmentOptions || !segmentOptions.setContext || !points) {\n    return segments;\n  }\n  return doSplitByStyles(line, segments, points, segmentOptions);\n}\nfunction doSplitByStyles(line, segments, points, segmentOptions) {\n  const chartContext = line._chart.getContext();\n  const baseStyle = readStyle(line.options);\n  const {_datasetIndex: datasetIndex, options: {spanGaps}} = line;\n  const count = points.length;\n  const result = [];\n  let prevStyle = baseStyle;\n  let start = segments[0].start;\n  let i = start;\n  function addStyle(s, e, l, st) {\n    const dir = spanGaps ? -1 : 1;\n    if (s === e) {\n      return;\n    }\n    s += count;\n    while (points[s % count].skip) {\n      s -= dir;\n    }\n    while (points[e % count].skip) {\n      e += dir;\n    }\n    if (s % count !== e % count) {\n      result.push({start: s % count, end: e % count, loop: l, style: st});\n      prevStyle = st;\n      start = e % count;\n    }\n  }\n  for (const segment of segments) {\n    start = spanGaps ? start : segment.start;\n    let prev = points[start % count];\n    let style;\n    for (i = start + 1; i <= segment.end; i++) {\n      const pt = points[i % count];\n      style = readStyle(segmentOptions.setContext(createContext(chartContext, {\n        type: 'segment',\n        p0: prev,\n        p1: pt,\n        p0DataIndex: (i - 1) % count,\n        p1DataIndex: i % count,\n        datasetIndex\n      })));\n      if (styleChanged(style, prevStyle)) {\n        addStyle(start, i - 1, segment.loop, prevStyle);\n      }\n      prev = pt;\n      prevStyle = style;\n    }\n    if (start < i - 1) {\n      addStyle(start, i - 1, segment.loop, prevStyle);\n    }\n  }\n  return result;\n}\nfunction readStyle(options) {\n  return {\n    backgroundColor: options.backgroundColor,\n    borderCapStyle: options.borderCapStyle,\n    borderDash: options.borderDash,\n    borderDashOffset: options.borderDashOffset,\n    borderJoinStyle: options.borderJoinStyle,\n    borderWidth: options.borderWidth,\n    borderColor: options.borderColor\n  };\n}\nfunction styleChanged(style, prevStyle) {\n  return prevStyle && JSON.stringify(style) !== JSON.stringify(prevStyle);\n}\n\nexport { _isPointInArea as $, _factorize as A, finiteOrDefault as B, callback as C, _addGrace as D, _limitValue as E, toDegrees as F, _measureText as G, HALF_PI as H, _int16Range as I, _alignPixel as J, toPadding as K, clipArea as L, renderText as M, unclipArea as N, toFont as O, PI as P, each as Q, _toLeftRightCenter as R, _alignStartEnd as S, TAU as T, overrides as U, merge as V, _capitalize as W, getRelativePosition as X, _rlookupByKey as Y, _lookupByKey as Z, _arrayUnique as _, resolve as a, toLineHeight as a$, getAngleFromPoint as a0, getMaximumSize as a1, _getParentNode as a2, readUsedSize as a3, throttled as a4, supportsEventListenerOptions as a5, _isDomSupported as a6, descriptors as a7, isFunction as a8, _attachContext as a9, getRtlAdapter as aA, overrideTextDirection as aB, _textX as aC, restoreTextDirection as aD, drawPointLegend as aE, noop as aF, distanceBetweenPoints as aG, _setMinAndMaxByKey as aH, niceNum as aI, almostWhole as aJ, almostEquals as aK, _decimalPlaces as aL, _longestText as aM, _filterBetween as aN, _lookup as aO, isPatternOrGradient as aP, getHoverColor as aQ, clone$1 as aR, _merger as aS, _mergerIf as aT, _deprecated as aU, _splitKey as aV, toFontString as aW, splineCurve as aX, splineCurveMonotone as aY, getStyle as aZ, fontString as a_, _createResolver as aa, _descriptors as ab, mergeIf as ac, uid as ad, debounce as ae, retinaScale as af, clearCanvas as ag, setsEqual as ah, _elementsEqual as ai, _isClickEvent as aj, _isBetween as ak, _readValueToProps as al, _updateBezierControlPoints as am, _computeSegments as an, _boundSegments as ao, _steppedInterpolation as ap, _bezierInterpolation as aq, _pointInLine as ar, _steppedLineTo as as, _bezierCurveTo as at, drawPoint as au, addRoundedRectPath as av, toTRBL as aw, toTRBLCorners as ax, _boundSegment as ay, _normalizeAngle as az, isArray as b, PITAU as b0, INFINITY as b1, RAD_PER_DEG as b2, QUARTER_PI as b3, TWO_THIRDS_PI as b4, _angleDiff as b5, color as c, defaults as d, effects as e, resolveObjectKey as f, isNumberFinite as g, createContext as h, isObject as i, defined as j, isNullOrUndef as k, listenArrayEvents as l, toPercentage as m, toDimension as n, formatNumber as o, _angleBetween as p, _getStartAndCountOfVisiblePoints as q, requestAnimFrame as r, sign as s, toRadians as t, unlistenArrayEvents as u, valueOrDefault as v, _scaleRangesChanged as w, isNumber as x, _parseObjectDataRadialScale as y, log10 as z };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,IAAIA,CAAA,EAAG,CAAC;AACjB,MAAMC,GAAG,GAAI,YAAW;EACtB,IAAIC,EAAE,GAAG,CAAC;EACV,OAAO,YAAW;IAChB,OAAOA,EAAE,EAAE;EACb,CAAC;AACH,CAAC,CAAC,CAAE;AACJ,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC5B,OAAOA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,WAAW;AACvD;AACA,SAASC,OAAOA,CAACD,KAAK,EAAE;EACtB,IAAIE,KAAK,CAACD,OAAO,IAAIC,KAAK,CAACD,OAAO,CAACD,KAAK,CAAC,EAAE;IACzC,OAAO,IAAI;EACb;EACA,MAAMG,IAAI,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACP,KAAK,CAAC;EAClD,IAAIG,IAAI,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,SAAS,IAAIL,IAAI,CAACK,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;IACjE,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd;AACA,SAASC,QAAQA,CAACT,KAAK,EAAE;EACvB,OAAOA,KAAK,KAAK,IAAI,IAAII,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACP,KAAK,CAAC,KAAK,iBAAiB;AACtF;AACA,MAAMU,cAAc,GAAIV,KAAK,IAAK,CAAC,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,YAAYW,MAAM,KAAKC,QAAQ,CAAC,CAACZ,KAAK,CAAC;AAC5G,SAASa,eAAeA,CAACb,KAAK,EAAEc,YAAY,EAAE;EAC5C,OAAOJ,cAAc,CAACV,KAAK,CAAC,GAAGA,KAAK,GAAGc,YAAY;AACrD;AACA,SAASC,cAAcA,CAACf,KAAK,EAAEc,YAAY,EAAE;EAC3C,OAAO,OAAOd,KAAK,KAAK,WAAW,GAAGc,YAAY,GAAGd,KAAK;AAC5D;AACA,MAAMgB,YAAY,GAAGA,CAAChB,KAAK,EAAEiB,SAAS,KACpC,OAAOjB,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACkB,QAAQ,CAAC,GAAG,CAAC,GAC9CC,UAAU,CAACnB,KAAK,CAAC,GAAG,GAAG,GACrBA,KAAK,GAAGiB,SAAS;AACvB,MAAMG,WAAW,GAAGA,CAACpB,KAAK,EAAEiB,SAAS,KACnC,OAAOjB,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACkB,QAAQ,CAAC,GAAG,CAAC,GAC9CC,UAAU,CAACnB,KAAK,CAAC,GAAG,GAAG,GAAGiB,SAAS,GACjC,CAACjB,KAAK;AACZ,SAASqB,QAAQA,CAACC,EAAE,EAAEC,IAAI,EAAEC,OAAO,EAAE;EACnC,IAAIF,EAAE,IAAI,OAAOA,EAAE,CAACf,IAAI,KAAK,UAAU,EAAE;IACvC,OAAOe,EAAE,CAACG,KAAK,CAACD,OAAO,EAAED,IAAI,CAAC;EAChC;AACF;AACA,SAASG,IAAIA,CAACC,QAAQ,EAAEL,EAAE,EAAEE,OAAO,EAAEI,OAAO,EAAE;EAC5C,IAAIC,CAAC,EAAEC,GAAG,EAAEC,IAAI;EAChB,IAAI9B,OAAO,CAAC0B,QAAQ,CAAC,EAAE;IACrBG,GAAG,GAAGH,QAAQ,CAACK,MAAM;IACrB,IAAIJ,OAAO,EAAE;MACX,KAAKC,CAAC,GAAGC,GAAG,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC7BP,EAAE,CAACf,IAAI,CAACiB,OAAO,EAAEG,QAAQ,CAACE,CAAC,CAAC,EAAEA,CAAC,CAAC;MAClC;IACF,CAAC,MAAM;MACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;QACxBP,EAAE,CAACf,IAAI,CAACiB,OAAO,EAAEG,QAAQ,CAACE,CAAC,CAAC,EAAEA,CAAC,CAAC;MAClC;IACF;EACF,CAAC,MAAM,IAAIpB,QAAQ,CAACkB,QAAQ,CAAC,EAAE;IAC7BI,IAAI,GAAG3B,MAAM,CAAC2B,IAAI,CAACJ,QAAQ,CAAC;IAC5BG,GAAG,GAAGC,IAAI,CAACC,MAAM;IACjB,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MACxBP,EAAE,CAACf,IAAI,CAACiB,OAAO,EAAEG,QAAQ,CAACI,IAAI,CAACF,CAAC,CAAC,CAAC,EAAEE,IAAI,CAACF,CAAC,CAAC,CAAC;IAC9C;EACF;AACF;AACA,SAASI,cAAcA,CAACC,EAAE,EAAEC,EAAE,EAAE;EAC9B,IAAIN,CAAC,EAAEO,IAAI,EAAEC,EAAE,EAAEC,EAAE;EACnB,IAAI,CAACJ,EAAE,IAAI,CAACC,EAAE,IAAID,EAAE,CAACF,MAAM,KAAKG,EAAE,CAACH,MAAM,EAAE;IACzC,OAAO,KAAK;EACd;EACA,KAAKH,CAAC,GAAG,CAAC,EAAEO,IAAI,GAAGF,EAAE,CAACF,MAAM,EAAEH,CAAC,GAAGO,IAAI,EAAE,EAAEP,CAAC,EAAE;IAC3CQ,EAAE,GAAGH,EAAE,CAACL,CAAC,CAAC;IACVS,EAAE,GAAGH,EAAE,CAACN,CAAC,CAAC;IACV,IAAIQ,EAAE,CAACE,YAAY,KAAKD,EAAE,CAACC,YAAY,IAAIF,EAAE,CAACG,KAAK,KAAKF,EAAE,CAACE,KAAK,EAAE;MAChE,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb;AACA,SAASC,OAAOA,CAACC,MAAM,EAAE;EACvB,IAAIzC,OAAO,CAACyC,MAAM,CAAC,EAAE;IACnB,OAAOA,MAAM,CAACC,GAAG,CAACF,OAAO,CAAC;EAC5B;EACA,IAAIhC,QAAQ,CAACiC,MAAM,CAAC,EAAE;IACpB,MAAME,MAAM,GAAGxC,MAAM,CAACyC,MAAM,CAAC,IAAI,CAAC;IAClC,MAAMd,IAAI,GAAG3B,MAAM,CAAC2B,IAAI,CAACW,MAAM,CAAC;IAChC,MAAMI,IAAI,GAAGf,IAAI,CAACC,MAAM;IACxB,IAAIe,CAAC,GAAG,CAAC;IACT,OAAOA,CAAC,GAAGD,IAAI,EAAE,EAAEC,CAAC,EAAE;MACpBH,MAAM,CAACb,IAAI,CAACgB,CAAC,CAAC,CAAC,GAAGN,OAAO,CAACC,MAAM,CAACX,IAAI,CAACgB,CAAC,CAAC,CAAC,CAAC;IAC5C;IACA,OAAOH,MAAM;EACf;EACA,OAAOF,MAAM;AACf;AACA,SAASM,UAAUA,CAACC,GAAG,EAAE;EACvB,OAAO,CAAC,WAAW,EAAE,WAAW,EAAE,aAAa,CAAC,CAACC,OAAO,CAACD,GAAG,CAAC,KAAK,CAAC,CAAC;AACtE;AACA,SAASE,OAAOA,CAACF,GAAG,EAAEL,MAAM,EAAEF,MAAM,EAAEU,OAAO,EAAE;EAC7C,IAAI,CAACJ,UAAU,CAACC,GAAG,CAAC,EAAE;IACpB;EACF;EACA,MAAMI,IAAI,GAAGT,MAAM,CAACK,GAAG,CAAC;EACxB,MAAMK,IAAI,GAAGZ,MAAM,CAACO,GAAG,CAAC;EACxB,IAAIxC,QAAQ,CAAC4C,IAAI,CAAC,IAAI5C,QAAQ,CAAC6C,IAAI,CAAC,EAAE;IACpCC,KAAK,CAACF,IAAI,EAAEC,IAAI,EAAEF,OAAO,CAAC;EAC5B,CAAC,MAAM;IACLR,MAAM,CAACK,GAAG,CAAC,GAAGR,OAAO,CAACa,IAAI,CAAC;EAC7B;AACF;AACA,SAASC,KAAKA,CAACX,MAAM,EAAEF,MAAM,EAAEU,OAAO,EAAE;EACtC,MAAMI,OAAO,GAAGvD,OAAO,CAACyC,MAAM,CAAC,GAAGA,MAAM,GAAG,CAACA,MAAM,CAAC;EACnD,MAAMN,IAAI,GAAGoB,OAAO,CAACxB,MAAM;EAC3B,IAAI,CAACvB,QAAQ,CAACmC,MAAM,CAAC,EAAE;IACrB,OAAOA,MAAM;EACf;EACAQ,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvB,MAAMK,MAAM,GAAGL,OAAO,CAACK,MAAM,IAAIN,OAAO;EACxC,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGO,IAAI,EAAE,EAAEP,CAAC,EAAE;IAC7Ba,MAAM,GAAGc,OAAO,CAAC3B,CAAC,CAAC;IACnB,IAAI,CAACpB,QAAQ,CAACiC,MAAM,CAAC,EAAE;MACrB;IACF;IACA,MAAMX,IAAI,GAAG3B,MAAM,CAAC2B,IAAI,CAACW,MAAM,CAAC;IAChC,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAED,IAAI,GAAGf,IAAI,CAACC,MAAM,EAAEe,CAAC,GAAGD,IAAI,EAAE,EAAEC,CAAC,EAAE;MACjDU,MAAM,CAAC1B,IAAI,CAACgB,CAAC,CAAC,EAAEH,MAAM,EAAEF,MAAM,EAAEU,OAAO,CAAC;IAC1C;EACF;EACA,OAAOR,MAAM;AACf;AACA,SAASc,OAAOA,CAACd,MAAM,EAAEF,MAAM,EAAE;EAC/B,OAAOa,KAAK,CAACX,MAAM,EAAEF,MAAM,EAAE;IAACe,MAAM,EAAEE;EAAS,CAAC,CAAC;AACnD;AACA,SAASA,SAASA,CAACV,GAAG,EAAEL,MAAM,EAAEF,MAAM,EAAE;EACtC,IAAI,CAACM,UAAU,CAACC,GAAG,CAAC,EAAE;IACpB;EACF;EACA,MAAMI,IAAI,GAAGT,MAAM,CAACK,GAAG,CAAC;EACxB,MAAMK,IAAI,GAAGZ,MAAM,CAACO,GAAG,CAAC;EACxB,IAAIxC,QAAQ,CAAC4C,IAAI,CAAC,IAAI5C,QAAQ,CAAC6C,IAAI,CAAC,EAAE;IACpCI,OAAO,CAACL,IAAI,EAAEC,IAAI,CAAC;EACrB,CAAC,MAAM,IAAI,CAAClD,MAAM,CAACC,SAAS,CAACuD,cAAc,CAACrD,IAAI,CAACqC,MAAM,EAAEK,GAAG,CAAC,EAAE;IAC7DL,MAAM,CAACK,GAAG,CAAC,GAAGR,OAAO,CAACa,IAAI,CAAC;EAC7B;AACF;AACA,SAASO,WAAWA,CAACC,KAAK,EAAE9D,KAAK,EAAE+D,QAAQ,EAAEC,OAAO,EAAE;EACpD,IAAIhE,KAAK,KAAKiE,SAAS,EAAE;IACvBC,OAAO,CAACC,IAAI,CAACL,KAAK,GAAG,KAAK,GAAGC,QAAQ,GACtC,+BAA+B,GAAGC,OAAO,GAAG,WAAW,CAAC;EACzD;AACF;AACA,MAAMI,YAAY,GAAG;EACnB,EAAE,EAAEC,CAAC,IAAIA,CAAC;EACVC,CAAC,EAAEC,CAAC,IAAIA,CAAC,CAACD,CAAC;EACXE,CAAC,EAAED,CAAC,IAAIA,CAAC,CAACC;AACZ,CAAC;AACD,SAASC,gBAAgBA,CAACC,GAAG,EAAEzB,GAAG,EAAE;EAClC,MAAM0B,QAAQ,GAAGP,YAAY,CAACnB,GAAG,CAAC,KAAKmB,YAAY,CAACnB,GAAG,CAAC,GAAG2B,eAAe,CAAC3B,GAAG,CAAC,CAAC;EAChF,OAAO0B,QAAQ,CAACD,GAAG,CAAC;AACtB;AACA,SAASE,eAAeA,CAAC3B,GAAG,EAAE;EAC5B,MAAMlB,IAAI,GAAG8C,SAAS,CAAC5B,GAAG,CAAC;EAC3B,OAAOyB,GAAG,IAAI;IACZ,KAAK,MAAM3B,CAAC,IAAIhB,IAAI,EAAE;MACpB,IAAIgB,CAAC,KAAK,EAAE,EAAE;QACZ;MACF;MACA2B,GAAG,GAAGA,GAAG,IAAIA,GAAG,CAAC3B,CAAC,CAAC;IACrB;IACA,OAAO2B,GAAG;EACZ,CAAC;AACH;AACA,SAASG,SAASA,CAAC5B,GAAG,EAAE;EACtB,MAAM6B,KAAK,GAAG7B,GAAG,CAAC8B,KAAK,CAAC,GAAG,CAAC;EAC5B,MAAMhD,IAAI,GAAG,EAAE;EACf,IAAIiD,GAAG,GAAG,EAAE;EACZ,KAAK,MAAMC,IAAI,IAAIH,KAAK,EAAE;IACxBE,GAAG,IAAIC,IAAI;IACX,IAAID,GAAG,CAAC9D,QAAQ,CAAC,IAAI,CAAC,EAAE;MACtB8D,GAAG,GAAGA,GAAG,CAACxE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG;IAC9B,CAAC,MAAM;MACLuB,IAAI,CAACmD,IAAI,CAACF,GAAG,CAAC;MACdA,GAAG,GAAG,EAAE;IACV;EACF;EACA,OAAOjD,IAAI;AACb;AACA,SAASoD,WAAWA,CAACC,GAAG,EAAE;EACxB,OAAOA,GAAG,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,GAAG,CAAC5E,KAAK,CAAC,CAAC,CAAC;AACnD;AACA,MAAM+E,OAAO,GAAIvF,KAAK,IAAK,OAAOA,KAAK,KAAK,WAAW;AACvD,MAAMwF,UAAU,GAAIxF,KAAK,IAAK,OAAOA,KAAK,KAAK,UAAU;AACzD,MAAMyF,SAAS,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAK;EAC1B,IAAID,CAAC,CAACE,IAAI,KAAKD,CAAC,CAACC,IAAI,EAAE;IACrB,OAAO,KAAK;EACd;EACA,KAAK,MAAMC,IAAI,IAAIH,CAAC,EAAE;IACpB,IAAI,CAACC,CAAC,CAACG,GAAG,CAACD,IAAI,CAAC,EAAE;MAChB,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb,CAAC;AACD,SAASE,aAAaA,CAACC,CAAC,EAAE;EACxB,OAAOA,CAAC,CAAC7F,IAAI,KAAK,SAAS,IAAI6F,CAAC,CAAC7F,IAAI,KAAK,OAAO,IAAI6F,CAAC,CAAC7F,IAAI,KAAK,aAAa;AAC/E;AAEA,MAAM8F,EAAE,GAAGC,IAAI,CAACD,EAAE;AAClB,MAAME,GAAG,GAAG,CAAC,GAAGF,EAAE;AAClB,MAAMG,KAAK,GAAGD,GAAG,GAAGF,EAAE;AACtB,MAAMI,QAAQ,GAAG1F,MAAM,CAAC2F,iBAAiB;AACzC,MAAMC,WAAW,GAAGN,EAAE,GAAG,GAAG;AAC5B,MAAMO,OAAO,GAAGP,EAAE,GAAG,CAAC;AACtB,MAAMQ,UAAU,GAAGR,EAAE,GAAG,CAAC;AACzB,MAAMS,aAAa,GAAGT,EAAE,GAAG,CAAC,GAAG,CAAC;AAChC,MAAMU,KAAK,GAAGT,IAAI,CAACS,KAAK;AACxB,MAAMC,IAAI,GAAGV,IAAI,CAACU,IAAI;AACtB,SAASC,OAAOA,CAACC,KAAK,EAAE;EACtB,MAAMC,YAAY,GAAGb,IAAI,CAACc,KAAK,CAACF,KAAK,CAAC;EACtCA,KAAK,GAAGG,YAAY,CAACH,KAAK,EAAEC,YAAY,EAAED,KAAK,GAAG,IAAI,CAAC,GAAGC,YAAY,GAAGD,KAAK;EAC9E,MAAMI,SAAS,GAAGhB,IAAI,CAACiB,GAAG,CAAC,EAAE,EAAEjB,IAAI,CAACkB,KAAK,CAACT,KAAK,CAACG,KAAK,CAAC,CAAC,CAAC;EACxD,MAAMO,QAAQ,GAAGP,KAAK,GAAGI,SAAS;EAClC,MAAMI,YAAY,GAAGD,QAAQ,IAAI,CAAC,GAAG,CAAC,GAAGA,QAAQ,IAAI,CAAC,GAAG,CAAC,GAAGA,QAAQ,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE;EACnF,OAAOC,YAAY,GAAGJ,SAAS;AACjC;AACA,SAASK,UAAUA,CAACvH,KAAK,EAAE;EACzB,MAAMwH,MAAM,GAAG,EAAE;EACjB,MAAMC,IAAI,GAAGvB,IAAI,CAACuB,IAAI,CAACzH,KAAK,CAAC;EAC7B,IAAI6B,CAAC;EACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4F,IAAI,EAAE5F,CAAC,EAAE,EAAE;IACzB,IAAI7B,KAAK,GAAG6B,CAAC,KAAK,CAAC,EAAE;MACnB2F,MAAM,CAACtC,IAAI,CAACrD,CAAC,CAAC;MACd2F,MAAM,CAACtC,IAAI,CAAClF,KAAK,GAAG6B,CAAC,CAAC;IACxB;EACF;EACA,IAAI4F,IAAI,MAAMA,IAAI,GAAG,CAAC,CAAC,EAAE;IACvBD,MAAM,CAACtC,IAAI,CAACuC,IAAI,CAAC;EACnB;EACAD,MAAM,CAACE,IAAI,CAAC,CAAChC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC,CAACgC,GAAG,CAAC,CAAC;EAClC,OAAOH,MAAM;AACf;AACA,SAASI,QAAQA,CAACC,CAAC,EAAE;EACnB,OAAO,CAACC,KAAK,CAAC3G,UAAU,CAAC0G,CAAC,CAAC,CAAC,IAAIjH,QAAQ,CAACiH,CAAC,CAAC;AAC7C;AACA,SAASZ,YAAYA,CAAC3C,CAAC,EAAEE,CAAC,EAAEuD,OAAO,EAAE;EACnC,OAAO7B,IAAI,CAAC8B,GAAG,CAAC1D,CAAC,GAAGE,CAAC,CAAC,GAAGuD,OAAO;AAClC;AACA,SAASE,WAAWA,CAAC3D,CAAC,EAAEyD,OAAO,EAAE;EAC/B,MAAMG,OAAO,GAAGhC,IAAI,CAACc,KAAK,CAAC1C,CAAC,CAAC;EAC7B,OAAS4D,OAAO,GAAGH,OAAO,IAAKzD,CAAC,IAAO4D,OAAO,GAAGH,OAAO,IAAKzD,CAAE;AACjE;AACA,SAAS6D,kBAAkBA,CAACC,KAAK,EAAExF,MAAM,EAAEyF,QAAQ,EAAE;EACnD,IAAIxG,CAAC,EAAEO,IAAI,EAAEpC,KAAK;EAClB,KAAK6B,CAAC,GAAG,CAAC,EAAEO,IAAI,GAAGgG,KAAK,CAACpG,MAAM,EAAEH,CAAC,GAAGO,IAAI,EAAEP,CAAC,EAAE,EAAE;IAC9C7B,KAAK,GAAGoI,KAAK,CAACvG,CAAC,CAAC,CAACwG,QAAQ,CAAC;IAC1B,IAAI,CAACP,KAAK,CAAC9H,KAAK,CAAC,EAAE;MACjB4C,MAAM,CAAC0F,GAAG,GAAGpC,IAAI,CAACoC,GAAG,CAAC1F,MAAM,CAAC0F,GAAG,EAAEtI,KAAK,CAAC;MACxC4C,MAAM,CAAC2F,GAAG,GAAGrC,IAAI,CAACqC,GAAG,CAAC3F,MAAM,CAAC2F,GAAG,EAAEvI,KAAK,CAAC;IAC1C;EACF;AACF;AACA,SAASwI,SAASA,CAACC,OAAO,EAAE;EAC1B,OAAOA,OAAO,IAAIxC,EAAE,GAAG,GAAG,CAAC;AAC7B;AACA,SAASyC,SAASA,CAACC,OAAO,EAAE;EAC1B,OAAOA,OAAO,IAAI,GAAG,GAAG1C,EAAE,CAAC;AAC7B;AACA,SAAS2C,cAAcA,CAACtE,CAAC,EAAE;EACzB,IAAI,CAAC5D,cAAc,CAAC4D,CAAC,CAAC,EAAE;IACtB;EACF;EACA,IAAI0B,CAAC,GAAG,CAAC;EACT,IAAI6C,CAAC,GAAG,CAAC;EACT,OAAO3C,IAAI,CAACc,KAAK,CAAC1C,CAAC,GAAG0B,CAAC,CAAC,GAAGA,CAAC,KAAK1B,CAAC,EAAE;IAClC0B,CAAC,IAAI,EAAE;IACP6C,CAAC,EAAE;EACL;EACA,OAAOA,CAAC;AACV;AACA,SAASC,iBAAiBA,CAACC,WAAW,EAAEC,UAAU,EAAE;EAClD,MAAMC,mBAAmB,GAAGD,UAAU,CAAC1E,CAAC,GAAGyE,WAAW,CAACzE,CAAC;EACxD,MAAM4E,mBAAmB,GAAGF,UAAU,CAACxE,CAAC,GAAGuE,WAAW,CAACvE,CAAC;EACxD,MAAM2E,wBAAwB,GAAGjD,IAAI,CAACuB,IAAI,CAACwB,mBAAmB,GAAGA,mBAAmB,GAAGC,mBAAmB,GAAGA,mBAAmB,CAAC;EACjI,IAAIE,KAAK,GAAGlD,IAAI,CAACmD,KAAK,CAACH,mBAAmB,EAAED,mBAAmB,CAAC;EAChE,IAAIG,KAAK,GAAI,CAAC,GAAG,GAAGnD,EAAG,EAAE;IACvBmD,KAAK,IAAIjD,GAAG;EACd;EACA,OAAO;IACLiD,KAAK;IACLE,QAAQ,EAAEH;EACZ,CAAC;AACH;AACA,SAASI,qBAAqBA,CAACC,GAAG,EAAEC,GAAG,EAAE;EACvC,OAAOvD,IAAI,CAACuB,IAAI,CAACvB,IAAI,CAACiB,GAAG,CAACsC,GAAG,CAACnF,CAAC,GAAGkF,GAAG,CAAClF,CAAC,EAAE,CAAC,CAAC,GAAG4B,IAAI,CAACiB,GAAG,CAACsC,GAAG,CAACjF,CAAC,GAAGgF,GAAG,CAAChF,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3E;AACA,SAASkF,UAAUA,CAAChE,CAAC,EAAEC,CAAC,EAAE;EACxB,OAAO,CAACD,CAAC,GAAGC,CAAC,GAAGS,KAAK,IAAID,GAAG,GAAGF,EAAE;AACnC;AACA,SAAS0D,eAAeA,CAACjE,CAAC,EAAE;EAC1B,OAAO,CAACA,CAAC,GAAGS,GAAG,GAAGA,GAAG,IAAIA,GAAG;AAC9B;AACA,SAASyD,aAAaA,CAACR,KAAK,EAAES,KAAK,EAAEC,GAAG,EAAEC,qBAAqB,EAAE;EAC/D,MAAMrE,CAAC,GAAGiE,eAAe,CAACP,KAAK,CAAC;EAChC,MAAMY,CAAC,GAAGL,eAAe,CAACE,KAAK,CAAC;EAChC,MAAM7D,CAAC,GAAG2D,eAAe,CAACG,GAAG,CAAC;EAC9B,MAAMG,YAAY,GAAGN,eAAe,CAACK,CAAC,GAAGtE,CAAC,CAAC;EAC3C,MAAMwE,UAAU,GAAGP,eAAe,CAAC3D,CAAC,GAAGN,CAAC,CAAC;EACzC,MAAMyE,YAAY,GAAGR,eAAe,CAACjE,CAAC,GAAGsE,CAAC,CAAC;EAC3C,MAAMI,UAAU,GAAGT,eAAe,CAACjE,CAAC,GAAGM,CAAC,CAAC;EACzC,OAAON,CAAC,KAAKsE,CAAC,IAAItE,CAAC,KAAKM,CAAC,IAAK+D,qBAAqB,IAAIC,CAAC,KAAKhE,CAAE,IACzDiE,YAAY,GAAGC,UAAU,IAAIC,YAAY,GAAGC,UAAW;AAC/D;AACA,SAASC,WAAWA,CAACrK,KAAK,EAAEsI,GAAG,EAAEC,GAAG,EAAE;EACpC,OAAOrC,IAAI,CAACqC,GAAG,CAACD,GAAG,EAAEpC,IAAI,CAACoC,GAAG,CAACC,GAAG,EAAEvI,KAAK,CAAC,CAAC;AAC5C;AACA,SAASsK,WAAWA,CAACtK,KAAK,EAAE;EAC1B,OAAOqK,WAAW,CAACrK,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;AAC1C;AACA,SAASuK,UAAUA,CAACvK,KAAK,EAAE6J,KAAK,EAAEC,GAAG,EAAE/B,OAAO,GAAG,IAAI,EAAE;EACrD,OAAO/H,KAAK,IAAIkG,IAAI,CAACoC,GAAG,CAACuB,KAAK,EAAEC,GAAG,CAAC,GAAG/B,OAAO,IAAI/H,KAAK,IAAIkG,IAAI,CAACqC,GAAG,CAACsB,KAAK,EAAEC,GAAG,CAAC,GAAG/B,OAAO;AAC3F;AAEA,SAASyC,OAAOA,CAACC,KAAK,EAAEzK,KAAK,EAAE0K,GAAG,EAAE;EAClCA,GAAG,GAAGA,GAAG,KAAMlI,KAAK,IAAKiI,KAAK,CAACjI,KAAK,CAAC,GAAGxC,KAAK,CAAC;EAC9C,IAAI2K,EAAE,GAAGF,KAAK,CAACzI,MAAM,GAAG,CAAC;EACzB,IAAI4I,EAAE,GAAG,CAAC;EACV,IAAIC,GAAG;EACP,OAAOF,EAAE,GAAGC,EAAE,GAAG,CAAC,EAAE;IAClBC,GAAG,GAAID,EAAE,GAAGD,EAAE,IAAK,CAAC;IACpB,IAAID,GAAG,CAACG,GAAG,CAAC,EAAE;MACZD,EAAE,GAAGC,GAAG;IACV,CAAC,MAAM;MACLF,EAAE,GAAGE,GAAG;IACV;EACF;EACA,OAAO;IAACD,EAAE;IAAED;EAAE,CAAC;AACjB;AACA,MAAMG,YAAY,GAAGA,CAACL,KAAK,EAAExH,GAAG,EAAEjD,KAAK,EAAE+K,IAAI,KAC3CP,OAAO,CAACC,KAAK,EAAEzK,KAAK,EAAE+K,IAAI,GACtBvI,KAAK,IAAIiI,KAAK,CAACjI,KAAK,CAAC,CAACS,GAAG,CAAC,IAAIjD,KAAK,GACnCwC,KAAK,IAAIiI,KAAK,CAACjI,KAAK,CAAC,CAACS,GAAG,CAAC,GAAGjD,KAAK,CAAC;AACzC,MAAMgL,aAAa,GAAGA,CAACP,KAAK,EAAExH,GAAG,EAAEjD,KAAK,KACtCwK,OAAO,CAACC,KAAK,EAAEzK,KAAK,EAAEwC,KAAK,IAAIiI,KAAK,CAACjI,KAAK,CAAC,CAACS,GAAG,CAAC,IAAIjD,KAAK,CAAC;AAC5D,SAASiL,cAAcA,CAACC,MAAM,EAAE5C,GAAG,EAAEC,GAAG,EAAE;EACxC,IAAIsB,KAAK,GAAG,CAAC;EACb,IAAIC,GAAG,GAAGoB,MAAM,CAAClJ,MAAM;EACvB,OAAO6H,KAAK,GAAGC,GAAG,IAAIoB,MAAM,CAACrB,KAAK,CAAC,GAAGvB,GAAG,EAAE;IACzCuB,KAAK,EAAE;EACT;EACA,OAAOC,GAAG,GAAGD,KAAK,IAAIqB,MAAM,CAACpB,GAAG,GAAG,CAAC,CAAC,GAAGvB,GAAG,EAAE;IAC3CuB,GAAG,EAAE;EACP;EACA,OAAOD,KAAK,GAAG,CAAC,IAAIC,GAAG,GAAGoB,MAAM,CAAClJ,MAAM,GACnCkJ,MAAM,CAAC1K,KAAK,CAACqJ,KAAK,EAAEC,GAAG,CAAC,GACxBoB,MAAM;AACZ;AACA,MAAMC,WAAW,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC;AACjE,SAASC,iBAAiBA,CAAChD,KAAK,EAAEiD,QAAQ,EAAE;EAC1C,IAAIjD,KAAK,CAACkD,QAAQ,EAAE;IAClBlD,KAAK,CAACkD,QAAQ,CAACC,SAAS,CAACrG,IAAI,CAACmG,QAAQ,CAAC;IACvC;EACF;EACAjL,MAAM,CAACoL,cAAc,CAACpD,KAAK,EAAE,UAAU,EAAE;IACvCqD,YAAY,EAAE,IAAI;IAClBC,UAAU,EAAE,KAAK;IACjB1L,KAAK,EAAE;MACLuL,SAAS,EAAE,CAACF,QAAQ;IACtB;EACF,CAAC,CAAC;EACFF,WAAW,CAACQ,OAAO,CAAE1I,GAAG,IAAK;IAC3B,MAAM2I,MAAM,GAAG,SAAS,GAAGzG,WAAW,CAAClC,GAAG,CAAC;IAC3C,MAAM4I,IAAI,GAAGzD,KAAK,CAACnF,GAAG,CAAC;IACvB7C,MAAM,CAACoL,cAAc,CAACpD,KAAK,EAAEnF,GAAG,EAAE;MAChCwI,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,KAAK;MACjB1L,KAAKA,CAAC,GAAGuB,IAAI,EAAE;QACb,MAAMuK,GAAG,GAAGD,IAAI,CAACpK,KAAK,CAAC,IAAI,EAAEF,IAAI,CAAC;QAClC6G,KAAK,CAACkD,QAAQ,CAACC,SAAS,CAACI,OAAO,CAAEI,MAAM,IAAK;UAC3C,IAAI,OAAOA,MAAM,CAACH,MAAM,CAAC,KAAK,UAAU,EAAE;YACxCG,MAAM,CAACH,MAAM,CAAC,CAAC,GAAGrK,IAAI,CAAC;UACzB;QACF,CAAC,CAAC;QACF,OAAOuK,GAAG;MACZ;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACA,SAASE,mBAAmBA,CAAC5D,KAAK,EAAEiD,QAAQ,EAAE;EAC5C,MAAMY,IAAI,GAAG7D,KAAK,CAACkD,QAAQ;EAC3B,IAAI,CAACW,IAAI,EAAE;IACT;EACF;EACA,MAAMV,SAAS,GAAGU,IAAI,CAACV,SAAS;EAChC,MAAM/I,KAAK,GAAG+I,SAAS,CAACrI,OAAO,CAACmI,QAAQ,CAAC;EACzC,IAAI7I,KAAK,KAAK,CAAC,CAAC,EAAE;IAChB+I,SAAS,CAACW,MAAM,CAAC1J,KAAK,EAAE,CAAC,CAAC;EAC5B;EACA,IAAI+I,SAAS,CAACvJ,MAAM,GAAG,CAAC,EAAE;IACxB;EACF;EACAmJ,WAAW,CAACQ,OAAO,CAAE1I,GAAG,IAAK;IAC3B,OAAOmF,KAAK,CAACnF,GAAG,CAAC;EACnB,CAAC,CAAC;EACF,OAAOmF,KAAK,CAACkD,QAAQ;AACvB;AACA,SAASa,YAAYA,CAACC,KAAK,EAAE;EAC3B,MAAMC,GAAG,GAAG,IAAIC,GAAG,CAAC,CAAC;EACrB,IAAIzK,CAAC,EAAEO,IAAI;EACX,KAAKP,CAAC,GAAG,CAAC,EAAEO,IAAI,GAAGgK,KAAK,CAACpK,MAAM,EAAEH,CAAC,GAAGO,IAAI,EAAE,EAAEP,CAAC,EAAE;IAC9CwK,GAAG,CAACE,GAAG,CAACH,KAAK,CAACvK,CAAC,CAAC,CAAC;EACnB;EACA,IAAIwK,GAAG,CAACzG,IAAI,KAAKxD,IAAI,EAAE;IACrB,OAAOgK,KAAK;EACd;EACA,OAAOlM,KAAK,CAACsM,IAAI,CAACH,GAAG,CAAC;AACxB;AAEA,SAASI,UAAUA,CAACC,SAAS,EAAEC,SAAS,EAAEC,UAAU,EAAE;EACpD,OAAOD,SAAS,GAAG,GAAG,GAAGD,SAAS,GAAG,KAAK,GAAGE,UAAU;AACzD;AACA,MAAMC,gBAAgB,GAAI,YAAW;EACnC,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IACjC,OAAO,UAASzL,QAAQ,EAAE;MACxB,OAAOA,QAAQ,CAAC,CAAC;IACnB,CAAC;EACH;EACA,OAAOyL,MAAM,CAACC,qBAAqB;AACrC,CAAC,CAAC,CAAE;AACJ,SAASC,SAASA,CAAC1L,EAAE,EAAEE,OAAO,EAAEyL,QAAQ,EAAE;EACxC,MAAMC,UAAU,GAAGD,QAAQ,KAAM1L,IAAI,IAAKrB,KAAK,CAACG,SAAS,CAACG,KAAK,CAACD,IAAI,CAACgB,IAAI,CAAC,CAAC;EAC3E,IAAI4L,OAAO,GAAG,KAAK;EACnB,IAAI5L,IAAI,GAAG,EAAE;EACb,OAAO,UAAS,GAAG6L,IAAI,EAAE;IACvB7L,IAAI,GAAG2L,UAAU,CAACE,IAAI,CAAC;IACvB,IAAI,CAACD,OAAO,EAAE;MACZA,OAAO,GAAG,IAAI;MACdN,gBAAgB,CAACtM,IAAI,CAACuM,MAAM,EAAE,MAAM;QAClCK,OAAO,GAAG,KAAK;QACf7L,EAAE,CAACG,KAAK,CAACD,OAAO,EAAED,IAAI,CAAC;MACzB,CAAC,CAAC;IACJ;EACF,CAAC;AACH;AACA,SAAS8L,QAAQA,CAAC/L,EAAE,EAAEgM,KAAK,EAAE;EAC3B,IAAIC,OAAO;EACX,OAAO,UAAS,GAAGhM,IAAI,EAAE;IACvB,IAAI+L,KAAK,EAAE;MACTE,YAAY,CAACD,OAAO,CAAC;MACrBA,OAAO,GAAGE,UAAU,CAACnM,EAAE,EAAEgM,KAAK,EAAE/L,IAAI,CAAC;IACvC,CAAC,MAAM;MACLD,EAAE,CAACG,KAAK,CAAC,IAAI,EAAEF,IAAI,CAAC;IACtB;IACA,OAAO+L,KAAK;EACd,CAAC;AACH;AACA,MAAMI,kBAAkB,GAAIC,KAAK,IAAKA,KAAK,KAAK,OAAO,GAAG,MAAM,GAAGA,KAAK,KAAK,KAAK,GAAG,OAAO,GAAG,QAAQ;AACvG,MAAMC,cAAc,GAAGA,CAACD,KAAK,EAAE9D,KAAK,EAAEC,GAAG,KAAK6D,KAAK,KAAK,OAAO,GAAG9D,KAAK,GAAG8D,KAAK,KAAK,KAAK,GAAG7D,GAAG,GAAG,CAACD,KAAK,GAAGC,GAAG,IAAI,CAAC;AACnH,MAAM+D,MAAM,GAAGA,CAACF,KAAK,EAAEG,IAAI,EAAEC,KAAK,EAAEC,GAAG,KAAK;EAC1C,MAAMC,KAAK,GAAGD,GAAG,GAAG,MAAM,GAAG,OAAO;EACpC,OAAOL,KAAK,KAAKM,KAAK,GAAGF,KAAK,GAAGJ,KAAK,KAAK,QAAQ,GAAG,CAACG,IAAI,GAAGC,KAAK,IAAI,CAAC,GAAGD,IAAI;AACjF,CAAC;AACD,SAASI,gCAAgCA,CAACC,IAAI,EAAEC,MAAM,EAAEC,kBAAkB,EAAE;EAC1E,MAAMC,UAAU,GAAGF,MAAM,CAACpM,MAAM;EAChC,IAAI6H,KAAK,GAAG,CAAC;EACb,IAAI0E,KAAK,GAAGD,UAAU;EACtB,IAAIH,IAAI,CAACK,OAAO,EAAE;IAChB,MAAM;MAACC,MAAM;MAAEC;IAAO,CAAC,GAAGP,IAAI;IAC9B,MAAMQ,IAAI,GAAGF,MAAM,CAACE,IAAI;IACxB,MAAM;MAACrG,GAAG;MAAEC,GAAG;MAAEqG,UAAU;MAAEC;IAAU,CAAC,GAAGJ,MAAM,CAACK,aAAa,CAAC,CAAC;IACjE,IAAIF,UAAU,EAAE;MACd/E,KAAK,GAAGQ,WAAW,CAACnE,IAAI,CAACoC,GAAG,CAC1BwC,YAAY,CAAC4D,OAAO,EAAED,MAAM,CAACE,IAAI,EAAErG,GAAG,CAAC,CAACsC,EAAE,EAC1CyD,kBAAkB,GAAGC,UAAU,GAAGxD,YAAY,CAACsD,MAAM,EAAEO,IAAI,EAAEF,MAAM,CAACM,gBAAgB,CAACzG,GAAG,CAAC,CAAC,CAACsC,EAAE,CAAC,EAChG,CAAC,EAAE0D,UAAU,GAAG,CAAC,CAAC;IACpB;IACA,IAAIO,UAAU,EAAE;MACdN,KAAK,GAAGlE,WAAW,CAACnE,IAAI,CAACqC,GAAG,CAC1BuC,YAAY,CAAC4D,OAAO,EAAED,MAAM,CAACE,IAAI,EAAEpG,GAAG,EAAE,IAAI,CAAC,CAACoC,EAAE,GAAG,CAAC,EACpD0D,kBAAkB,GAAG,CAAC,GAAGvD,YAAY,CAACsD,MAAM,EAAEO,IAAI,EAAEF,MAAM,CAACM,gBAAgB,CAACxG,GAAG,CAAC,EAAE,IAAI,CAAC,CAACoC,EAAE,GAAG,CAAC,CAAC,EACjGd,KAAK,EAAEyE,UAAU,CAAC,GAAGzE,KAAK;IAC5B,CAAC,MAAM;MACL0E,KAAK,GAAGD,UAAU,GAAGzE,KAAK;IAC5B;EACF;EACA,OAAO;IAACA,KAAK;IAAE0E;EAAK,CAAC;AACvB;AACA,SAASS,mBAAmBA,CAACb,IAAI,EAAE;EACjC,MAAM;IAACc,MAAM;IAAEC,MAAM;IAAEC;EAAY,CAAC,GAAGhB,IAAI;EAC3C,MAAMiB,SAAS,GAAG;IAChBC,IAAI,EAAEJ,MAAM,CAAC3G,GAAG;IAChBgH,IAAI,EAAEL,MAAM,CAAC1G,GAAG;IAChBgH,IAAI,EAAEL,MAAM,CAAC5G,GAAG;IAChBkH,IAAI,EAAEN,MAAM,CAAC3G;EACf,CAAC;EACD,IAAI,CAAC4G,YAAY,EAAE;IACjBhB,IAAI,CAACgB,YAAY,GAAGC,SAAS;IAC7B,OAAO,IAAI;EACb;EACA,MAAMK,OAAO,GAAGN,YAAY,CAACE,IAAI,KAAKJ,MAAM,CAAC3G,GAAG,IAC7C6G,YAAY,CAACG,IAAI,KAAKL,MAAM,CAAC1G,GAAG,IAChC4G,YAAY,CAACI,IAAI,KAAKL,MAAM,CAAC5G,GAAG,IAChC6G,YAAY,CAACK,IAAI,KAAKN,MAAM,CAAC3G,GAAG;EACnCnI,MAAM,CAACsP,MAAM,CAACP,YAAY,EAAEC,SAAS,CAAC;EACtC,OAAOK,OAAO;AAChB;AAEA,MAAME,MAAM,GAAIC,CAAC,IAAKA,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC;AACxC,MAAMC,SAAS,GAAGA,CAACD,CAAC,EAAE5F,CAAC,EAAEnB,CAAC,KAAK,EAAE3C,IAAI,CAACiB,GAAG,CAAC,CAAC,EAAE,EAAE,IAAIyI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG1J,IAAI,CAAC4J,GAAG,CAAC,CAACF,CAAC,GAAG5F,CAAC,IAAI7D,GAAG,GAAG0C,CAAC,CAAC,CAAC;AAC1F,MAAMkH,UAAU,GAAGA,CAACH,CAAC,EAAE5F,CAAC,EAAEnB,CAAC,KAAK3C,IAAI,CAACiB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,GAAGyI,CAAC,CAAC,GAAG1J,IAAI,CAAC4J,GAAG,CAAC,CAACF,CAAC,GAAG5F,CAAC,IAAI7D,GAAG,GAAG0C,CAAC,CAAC,GAAG,CAAC;AACtF,MAAMmH,OAAO,GAAG;EACdC,MAAM,EAAEL,CAAC,IAAIA,CAAC;EACdM,UAAU,EAAEN,CAAC,IAAIA,CAAC,GAAGA,CAAC;EACtBO,WAAW,EAAEP,CAAC,IAAI,CAACA,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC;EAC9BQ,aAAa,EAAER,CAAC,IAAK,CAACA,CAAC,IAAI,GAAG,IAAI,CAAC,GAC/B,GAAG,GAAGA,CAAC,GAAGA,CAAC,GACX,CAAC,GAAG,IAAK,EAAEA,CAAC,IAAKA,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;EAChCS,WAAW,EAAET,CAAC,IAAIA,CAAC,GAAGA,CAAC,GAAGA,CAAC;EAC3BU,YAAY,EAAEV,CAAC,IAAI,CAACA,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGA,CAAC,GAAG,CAAC;EACvCW,cAAc,EAAEX,CAAC,IAAK,CAACA,CAAC,IAAI,GAAG,IAAI,CAAC,GAChC,GAAG,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GACf,GAAG,IAAI,CAACA,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC;EAChCY,WAAW,EAAEZ,CAAC,IAAIA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC;EAC/Ba,YAAY,EAAEb,CAAC,IAAI,EAAE,CAACA,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC;EAC9Cc,cAAc,EAAEd,CAAC,IAAK,CAACA,CAAC,IAAI,GAAG,IAAI,CAAC,GAChC,GAAG,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GACnB,CAAC,GAAG,IAAI,CAACA,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC;EACrCe,WAAW,EAAEf,CAAC,IAAIA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC;EACnCgB,YAAY,EAAEhB,CAAC,IAAI,CAACA,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC;EAC/CiB,cAAc,EAAEjB,CAAC,IAAK,CAACA,CAAC,IAAI,GAAG,IAAI,CAAC,GAChC,GAAG,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GACvB,GAAG,IAAI,CAACA,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC;EACxCkB,UAAU,EAAElB,CAAC,IAAI,CAAC1J,IAAI,CAAC6K,GAAG,CAACnB,CAAC,GAAGpJ,OAAO,CAAC,GAAG,CAAC;EAC3CwK,WAAW,EAAEpB,CAAC,IAAI1J,IAAI,CAAC4J,GAAG,CAACF,CAAC,GAAGpJ,OAAO,CAAC;EACvCyK,aAAa,EAAErB,CAAC,IAAI,CAAC,GAAG,IAAI1J,IAAI,CAAC6K,GAAG,CAAC9K,EAAE,GAAG2J,CAAC,CAAC,GAAG,CAAC,CAAC;EACjDsB,UAAU,EAAEtB,CAAC,IAAKA,CAAC,KAAK,CAAC,GAAI,CAAC,GAAG1J,IAAI,CAACiB,GAAG,CAAC,CAAC,EAAE,EAAE,IAAIyI,CAAC,GAAG,CAAC,CAAC,CAAC;EAC1DuB,WAAW,EAAEvB,CAAC,IAAKA,CAAC,KAAK,CAAC,GAAI,CAAC,GAAG,CAAC1J,IAAI,CAACiB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,GAAGyI,CAAC,CAAC,GAAG,CAAC;EAC3DwB,aAAa,EAAExB,CAAC,IAAID,MAAM,CAACC,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,GAAG,GACvC,GAAG,GAAG1J,IAAI,CAACiB,GAAG,CAAC,CAAC,EAAE,EAAE,IAAIyI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GACnC,GAAG,IAAI,CAAC1J,IAAI,CAACiB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,IAAIyI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAC/CyB,UAAU,EAAEzB,CAAC,IAAKA,CAAC,IAAI,CAAC,GAAIA,CAAC,GAAG,EAAE1J,IAAI,CAACuB,IAAI,CAAC,CAAC,GAAGmI,CAAC,GAAGA,CAAC,CAAC,GAAG,CAAC,CAAC;EAC3D0B,WAAW,EAAE1B,CAAC,IAAI1J,IAAI,CAACuB,IAAI,CAAC,CAAC,GAAG,CAACmI,CAAC,IAAI,CAAC,IAAIA,CAAC,CAAC;EAC7C2B,aAAa,EAAE3B,CAAC,IAAK,CAACA,CAAC,IAAI,GAAG,IAAI,CAAC,GAC/B,CAAC,GAAG,IAAI1J,IAAI,CAACuB,IAAI,CAAC,CAAC,GAAGmI,CAAC,GAAGA,CAAC,CAAC,GAAG,CAAC,CAAC,GACjC,GAAG,IAAI1J,IAAI,CAACuB,IAAI,CAAC,CAAC,GAAG,CAACmI,CAAC,IAAI,CAAC,IAAIA,CAAC,CAAC,GAAG,CAAC,CAAC;EAC3C4B,aAAa,EAAE5B,CAAC,IAAID,MAAM,CAACC,CAAC,CAAC,GAAGA,CAAC,GAAGC,SAAS,CAACD,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC;EAC5D6B,cAAc,EAAE7B,CAAC,IAAID,MAAM,CAACC,CAAC,CAAC,GAAGA,CAAC,GAAGG,UAAU,CAACH,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC;EAC9D8B,gBAAgBA,CAAC9B,CAAC,EAAE;IAClB,MAAM5F,CAAC,GAAG,MAAM;IAChB,MAAMnB,CAAC,GAAG,IAAI;IACd,OAAO8G,MAAM,CAACC,CAAC,CAAC,GAAGA,CAAC,GAClBA,CAAC,GAAG,GAAG,GACH,GAAG,GAAGC,SAAS,CAACD,CAAC,GAAG,CAAC,EAAE5F,CAAC,EAAEnB,CAAC,CAAC,GAC5B,GAAG,GAAG,GAAG,GAAGkH,UAAU,CAACH,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE5F,CAAC,EAAEnB,CAAC,CAAC;EAC/C,CAAC;EACD8I,UAAUA,CAAC/B,CAAC,EAAE;IACZ,MAAM5F,CAAC,GAAG,OAAO;IACjB,OAAO4F,CAAC,GAAGA,CAAC,IAAI,CAAC5F,CAAC,GAAG,CAAC,IAAI4F,CAAC,GAAG5F,CAAC,CAAC;EAClC,CAAC;EACD4H,WAAWA,CAAChC,CAAC,EAAE;IACb,MAAM5F,CAAC,GAAG,OAAO;IACjB,OAAO,CAAC4F,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,CAAC5F,CAAC,GAAG,CAAC,IAAI4F,CAAC,GAAG5F,CAAC,CAAC,GAAG,CAAC;EAC7C,CAAC;EACD6H,aAAaA,CAACjC,CAAC,EAAE;IACf,IAAI5F,CAAC,GAAG,OAAO;IACf,IAAI,CAAC4F,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE;MAClB,OAAO,GAAG,IAAIA,CAAC,GAAGA,CAAC,IAAI,CAAC,CAAC5F,CAAC,IAAK,KAAM,IAAI,CAAC,IAAI4F,CAAC,GAAG5F,CAAC,CAAC,CAAC;IACvD;IACA,OAAO,GAAG,IAAI,CAAC4F,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,CAAC,CAAC5F,CAAC,IAAK,KAAM,IAAI,CAAC,IAAI4F,CAAC,GAAG5F,CAAC,CAAC,GAAG,CAAC,CAAC;EAClE,CAAC;EACD8H,YAAY,EAAElC,CAAC,IAAI,CAAC,GAAGI,OAAO,CAAC+B,aAAa,CAAC,CAAC,GAAGnC,CAAC,CAAC;EACnDmC,aAAaA,CAACnC,CAAC,EAAE;IACf,MAAMoC,CAAC,GAAG,MAAM;IAChB,MAAMC,CAAC,GAAG,IAAI;IACd,IAAIrC,CAAC,GAAI,CAAC,GAAGqC,CAAE,EAAE;MACf,OAAOD,CAAC,GAAGpC,CAAC,GAAGA,CAAC;IAClB;IACA,IAAIA,CAAC,GAAI,CAAC,GAAGqC,CAAE,EAAE;MACf,OAAOD,CAAC,IAAIpC,CAAC,IAAK,GAAG,GAAGqC,CAAE,CAAC,GAAGrC,CAAC,GAAG,IAAI;IACxC;IACA,IAAIA,CAAC,GAAI,GAAG,GAAGqC,CAAE,EAAE;MACjB,OAAOD,CAAC,IAAIpC,CAAC,IAAK,IAAI,GAAGqC,CAAE,CAAC,GAAGrC,CAAC,GAAG,MAAM;IAC3C;IACA,OAAOoC,CAAC,IAAIpC,CAAC,IAAK,KAAK,GAAGqC,CAAE,CAAC,GAAGrC,CAAC,GAAG,QAAQ;EAC9C,CAAC;EACDsC,eAAe,EAAEtC,CAAC,IAAKA,CAAC,GAAG,GAAG,GAC1BI,OAAO,CAAC8B,YAAY,CAAClC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GACjCI,OAAO,CAAC+B,aAAa,CAACnC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG;AAC/C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,SAAS5I,KAAKA,CAAC3C,CAAC,EAAE;EAChB,OAAOA,CAAC,GAAG,GAAG,GAAG,CAAC;AACpB;AACA,MAAM8N,GAAG,GAAGA,CAAC9N,CAAC,EAAE+N,CAAC,EAAEC,CAAC,KAAKnM,IAAI,CAACqC,GAAG,CAACrC,IAAI,CAACoC,GAAG,CAACjE,CAAC,EAAEgO,CAAC,CAAC,EAAED,CAAC,CAAC;AACpD,SAASE,GAAGA,CAACjO,CAAC,EAAE;EACd,OAAO8N,GAAG,CAACnL,KAAK,CAAC3C,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;AACrC;AACA,SAASkO,GAAGA,CAAClO,CAAC,EAAE;EACd,OAAO8N,GAAG,CAACnL,KAAK,CAAC3C,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;AACpC;AACA,SAASmO,GAAGA,CAACnO,CAAC,EAAE;EACd,OAAO8N,GAAG,CAACnL,KAAK,CAAC3C,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AACzC;AACA,SAASoO,GAAGA,CAACpO,CAAC,EAAE;EACd,OAAO8N,GAAG,CAACnL,KAAK,CAAC3C,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;AACpC;AACA,MAAMqO,KAAK,GAAG;EAAC,CAAC,EAAE,CAAC;EAAE,CAAC,EAAE,CAAC;EAAE,CAAC,EAAE,CAAC;EAAE,CAAC,EAAE,CAAC;EAAE,CAAC,EAAE,CAAC;EAAE,CAAC,EAAE,CAAC;EAAE,CAAC,EAAE,CAAC;EAAE,CAAC,EAAE,CAAC;EAAE,CAAC,EAAE,CAAC;EAAE,CAAC,EAAE,CAAC;EAAEC,CAAC,EAAE,EAAE;EAAEC,CAAC,EAAE,EAAE;EAAEC,CAAC,EAAE,EAAE;EAAEC,CAAC,EAAE,EAAE;EAAEC,CAAC,EAAE,EAAE;EAAEC,CAAC,EAAE,EAAE;EAAEtN,CAAC,EAAE,EAAE;EAAEC,CAAC,EAAE,EAAE;EAAEsN,CAAC,EAAE,EAAE;EAAEhB,CAAC,EAAE,EAAE;EAAEjM,CAAC,EAAE,EAAE;EAAEkN,CAAC,EAAE;AAAE,CAAC;AAC9J,MAAMC,GAAG,GAAG,CAAC,GAAG,kBAAkB,CAAC;AACnC,MAAMC,EAAE,GAAGzN,CAAC,IAAIwN,GAAG,CAACxN,CAAC,GAAG,GAAG,CAAC;AAC5B,MAAM0N,EAAE,GAAG1N,CAAC,IAAIwN,GAAG,CAAC,CAACxN,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,GAAGwN,GAAG,CAACxN,CAAC,GAAG,GAAG,CAAC;AACnD,MAAM2N,EAAE,GAAG3N,CAAC,IAAK,CAACA,CAAC,GAAG,IAAI,KAAK,CAAC,MAAOA,CAAC,GAAG,GAAG,CAAC;AAC/C,MAAM4N,OAAO,GAAGlP,CAAC,IAAIiP,EAAE,CAACjP,CAAC,CAACmP,CAAC,CAAC,IAAIF,EAAE,CAACjP,CAAC,CAACoP,CAAC,CAAC,IAAIH,EAAE,CAACjP,CAAC,CAACsB,CAAC,CAAC,IAAI2N,EAAE,CAACjP,CAAC,CAACqB,CAAC,CAAC;AAC7D,SAASgO,QAAQA,CAACtO,GAAG,EAAE;EACrB,IAAItD,GAAG,GAAGsD,GAAG,CAACpD,MAAM;EACpB,IAAI2R,GAAG;EACP,IAAIvO,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAClB,IAAItD,GAAG,KAAK,CAAC,IAAIA,GAAG,KAAK,CAAC,EAAE;MAC1B6R,GAAG,GAAG;QACJH,CAAC,EAAE,GAAG,GAAGd,KAAK,CAACtN,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;QAC3BqO,CAAC,EAAE,GAAG,GAAGf,KAAK,CAACtN,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;QAC3BO,CAAC,EAAE,GAAG,GAAG+M,KAAK,CAACtN,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;QAC3BM,CAAC,EAAE5D,GAAG,KAAK,CAAC,GAAG4Q,KAAK,CAACtN,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG;MACtC,CAAC;IACH,CAAC,MAAM,IAAItD,GAAG,KAAK,CAAC,IAAIA,GAAG,KAAK,CAAC,EAAE;MACjC6R,GAAG,GAAG;QACJH,CAAC,EAAEd,KAAK,CAACtN,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGsN,KAAK,CAACtN,GAAG,CAAC,CAAC,CAAC,CAAC;QACrCqO,CAAC,EAAEf,KAAK,CAACtN,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGsN,KAAK,CAACtN,GAAG,CAAC,CAAC,CAAC,CAAC;QACrCO,CAAC,EAAE+M,KAAK,CAACtN,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGsN,KAAK,CAACtN,GAAG,CAAC,CAAC,CAAC,CAAC;QACrCM,CAAC,EAAE5D,GAAG,KAAK,CAAC,GAAI4Q,KAAK,CAACtN,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGsN,KAAK,CAACtN,GAAG,CAAC,CAAC,CAAC,CAAC,GAAI;MACxD,CAAC;IACH;EACF;EACA,OAAOuO,GAAG;AACZ;AACA,MAAMC,KAAK,GAAGA,CAAClO,CAAC,EAAEwN,CAAC,KAAKxN,CAAC,GAAG,GAAG,GAAGwN,CAAC,CAACxN,CAAC,CAAC,GAAG,EAAE;AAC3C,SAASmO,SAASA,CAACxP,CAAC,EAAE;EACpB,IAAI6O,CAAC,GAAGK,OAAO,CAAClP,CAAC,CAAC,GAAG+O,EAAE,GAAGC,EAAE;EAC5B,OAAOhP,CAAC,GACJ,GAAG,GAAG6O,CAAC,CAAC7O,CAAC,CAACmP,CAAC,CAAC,GAAGN,CAAC,CAAC7O,CAAC,CAACoP,CAAC,CAAC,GAAGP,CAAC,CAAC7O,CAAC,CAACsB,CAAC,CAAC,GAAGiO,KAAK,CAACvP,CAAC,CAACqB,CAAC,EAAEwN,CAAC,CAAC,GAC9CjP,SAAS;AACf;AACA,MAAM6P,MAAM,GAAG,8GAA8G;AAC7H,SAASC,QAAQA,CAAC1B,CAAC,EAAErI,CAAC,EAAEoI,CAAC,EAAE;EACzB,MAAM1M,CAAC,GAAGsE,CAAC,GAAG9D,IAAI,CAACoC,GAAG,CAAC8J,CAAC,EAAE,CAAC,GAAGA,CAAC,CAAC;EAChC,MAAMc,CAAC,GAAGA,CAACrL,CAAC,EAAE9E,CAAC,GAAG,CAAC8E,CAAC,GAAGwK,CAAC,GAAG,EAAE,IAAI,EAAE,KAAKD,CAAC,GAAG1M,CAAC,GAAGQ,IAAI,CAACqC,GAAG,CAACrC,IAAI,CAACoC,GAAG,CAACvF,CAAC,GAAG,CAAC,EAAE,CAAC,GAAGA,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACvF,OAAO,CAACmQ,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3B;AACA,SAASc,QAAQA,CAAC3B,CAAC,EAAErI,CAAC,EAAE3F,CAAC,EAAE;EACzB,MAAM6O,CAAC,GAAGA,CAACrL,CAAC,EAAE9E,CAAC,GAAG,CAAC8E,CAAC,GAAGwK,CAAC,GAAG,EAAE,IAAI,CAAC,KAAKhO,CAAC,GAAGA,CAAC,GAAG2F,CAAC,GAAG9D,IAAI,CAACqC,GAAG,CAACrC,IAAI,CAACoC,GAAG,CAACvF,CAAC,EAAE,CAAC,GAAGA,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EACrF,OAAO,CAACmQ,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3B;AACA,SAASe,QAAQA,CAAC5B,CAAC,EAAE6B,CAAC,EAAEvO,CAAC,EAAE;EACzB,MAAMwO,GAAG,GAAGJ,QAAQ,CAAC1B,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;EAC/B,IAAIxQ,CAAC;EACL,IAAIqS,CAAC,GAAGvO,CAAC,GAAG,CAAC,EAAE;IACb9D,CAAC,GAAG,CAAC,IAAIqS,CAAC,GAAGvO,CAAC,CAAC;IACfuO,CAAC,IAAIrS,CAAC;IACN8D,CAAC,IAAI9D,CAAC;EACR;EACA,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;IACtBsS,GAAG,CAACtS,CAAC,CAAC,IAAI,CAAC,GAAGqS,CAAC,GAAGvO,CAAC;IACnBwO,GAAG,CAACtS,CAAC,CAAC,IAAIqS,CAAC;EACb;EACA,OAAOC,GAAG;AACZ;AACA,SAASC,QAAQA,CAACZ,CAAC,EAAEC,CAAC,EAAE9N,CAAC,EAAEsM,CAAC,EAAE1J,GAAG,EAAE;EACjC,IAAIiL,CAAC,KAAKjL,GAAG,EAAE;IACb,OAAQ,CAACkL,CAAC,GAAG9N,CAAC,IAAIsM,CAAC,IAAKwB,CAAC,GAAG9N,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACxC;EACA,IAAI8N,CAAC,KAAKlL,GAAG,EAAE;IACb,OAAO,CAAC5C,CAAC,GAAG6N,CAAC,IAAIvB,CAAC,GAAG,CAAC;EACxB;EACA,OAAO,CAACuB,CAAC,GAAGC,CAAC,IAAIxB,CAAC,GAAG,CAAC;AACxB;AACA,SAASoC,OAAOA,CAAChQ,CAAC,EAAE;EAClB,MAAMyC,KAAK,GAAG,GAAG;EACjB,MAAM0M,CAAC,GAAGnP,CAAC,CAACmP,CAAC,GAAG1M,KAAK;EACrB,MAAM2M,CAAC,GAAGpP,CAAC,CAACoP,CAAC,GAAG3M,KAAK;EACrB,MAAMnB,CAAC,GAAGtB,CAAC,CAACsB,CAAC,GAAGmB,KAAK;EACrB,MAAMyB,GAAG,GAAGrC,IAAI,CAACqC,GAAG,CAACiL,CAAC,EAAEC,CAAC,EAAE9N,CAAC,CAAC;EAC7B,MAAM2C,GAAG,GAAGpC,IAAI,CAACoC,GAAG,CAACkL,CAAC,EAAEC,CAAC,EAAE9N,CAAC,CAAC;EAC7B,MAAMyM,CAAC,GAAG,CAAC7J,GAAG,GAAGD,GAAG,IAAI,CAAC;EACzB,IAAI+J,CAAC,EAAErI,CAAC,EAAEiI,CAAC;EACX,IAAI1J,GAAG,KAAKD,GAAG,EAAE;IACf2J,CAAC,GAAG1J,GAAG,GAAGD,GAAG;IACb0B,CAAC,GAAGoI,CAAC,GAAG,GAAG,GAAGH,CAAC,IAAI,CAAC,GAAG1J,GAAG,GAAGD,GAAG,CAAC,GAAG2J,CAAC,IAAI1J,GAAG,GAAGD,GAAG,CAAC;IACnD+J,CAAC,GAAG+B,QAAQ,CAACZ,CAAC,EAAEC,CAAC,EAAE9N,CAAC,EAAEsM,CAAC,EAAE1J,GAAG,CAAC;IAC7B8J,CAAC,GAAGA,CAAC,GAAG,EAAE,GAAG,GAAG;EAClB;EACA,OAAO,CAACA,CAAC,GAAG,CAAC,EAAErI,CAAC,IAAI,CAAC,EAAEoI,CAAC,CAAC;AAC3B;AACA,SAASkC,KAAKA,CAACpB,CAAC,EAAExN,CAAC,EAAEC,CAAC,EAAEsN,CAAC,EAAE;EACzB,OAAO,CACL/S,KAAK,CAACD,OAAO,CAACyF,CAAC,CAAC,GACZwN,CAAC,CAACxN,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,GACnBwN,CAAC,CAACxN,CAAC,EAAEC,CAAC,EAAEsN,CAAC,CAAC,EACdtQ,GAAG,CAAC4P,GAAG,CAAC;AACZ;AACA,SAASgC,OAAOA,CAAClC,CAAC,EAAErI,CAAC,EAAEoI,CAAC,EAAE;EACxB,OAAOkC,KAAK,CAACP,QAAQ,EAAE1B,CAAC,EAAErI,CAAC,EAAEoI,CAAC,CAAC;AACjC;AACA,SAASoC,OAAOA,CAACnC,CAAC,EAAE6B,CAAC,EAAEvO,CAAC,EAAE;EACxB,OAAO2O,KAAK,CAACL,QAAQ,EAAE5B,CAAC,EAAE6B,CAAC,EAAEvO,CAAC,CAAC;AACjC;AACA,SAAS8O,OAAOA,CAACpC,CAAC,EAAErI,CAAC,EAAE3F,CAAC,EAAE;EACxB,OAAOiQ,KAAK,CAACN,QAAQ,EAAE3B,CAAC,EAAErI,CAAC,EAAE3F,CAAC,CAAC;AACjC;AACA,SAASqQ,GAAGA,CAACrC,CAAC,EAAE;EACd,OAAO,CAACA,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG;AAC9B;AACA,SAASsC,QAAQA,CAACvP,GAAG,EAAE;EACrB,MAAM4M,CAAC,GAAG8B,MAAM,CAACc,IAAI,CAACxP,GAAG,CAAC;EAC1B,IAAIM,CAAC,GAAG,GAAG;EACX,IAAIrB,CAAC;EACL,IAAI,CAAC2N,CAAC,EAAE;IACN;EACF;EACA,IAAIA,CAAC,CAAC,CAAC,CAAC,KAAK3N,CAAC,EAAE;IACdqB,CAAC,GAAGsM,CAAC,CAAC,CAAC,CAAC,GAAGM,GAAG,CAAC,CAACN,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGO,GAAG,CAAC,CAACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACpC;EACA,MAAMK,CAAC,GAAGqC,GAAG,CAAC,CAAC1C,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB,MAAM6C,EAAE,GAAG,CAAC7C,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG;EACtB,MAAM8C,EAAE,GAAG,CAAC9C,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG;EACtB,IAAIA,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;IAClB3N,CAAC,GAAGmQ,OAAO,CAACnC,CAAC,EAAEwC,EAAE,EAAEC,EAAE,CAAC;EACxB,CAAC,MAAM,IAAI9C,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;IACzB3N,CAAC,GAAGoQ,OAAO,CAACpC,CAAC,EAAEwC,EAAE,EAAEC,EAAE,CAAC;EACxB,CAAC,MAAM;IACLzQ,CAAC,GAAGkQ,OAAO,CAAClC,CAAC,EAAEwC,EAAE,EAAEC,EAAE,CAAC;EACxB;EACA,OAAO;IACLtB,CAAC,EAAEnP,CAAC,CAAC,CAAC,CAAC;IACPoP,CAAC,EAAEpP,CAAC,CAAC,CAAC,CAAC;IACPsB,CAAC,EAAEtB,CAAC,CAAC,CAAC,CAAC;IACPqB,CAAC,EAAEA;EACL,CAAC;AACH;AACA,SAASqP,MAAMA,CAAC1Q,CAAC,EAAE2Q,GAAG,EAAE;EACtB,IAAI3C,CAAC,GAAGgC,OAAO,CAAChQ,CAAC,CAAC;EAClBgO,CAAC,CAAC,CAAC,CAAC,GAAGqC,GAAG,CAACrC,CAAC,CAAC,CAAC,CAAC,GAAG2C,GAAG,CAAC;EACtB3C,CAAC,GAAGkC,OAAO,CAAClC,CAAC,CAAC;EACdhO,CAAC,CAACmP,CAAC,GAAGnB,CAAC,CAAC,CAAC,CAAC;EACVhO,CAAC,CAACoP,CAAC,GAAGpB,CAAC,CAAC,CAAC,CAAC;EACVhO,CAAC,CAACsB,CAAC,GAAG0M,CAAC,CAAC,CAAC,CAAC;AACZ;AACA,SAAS4C,SAASA,CAAC5Q,CAAC,EAAE;EACpB,IAAI,CAACA,CAAC,EAAE;IACN;EACF;EACA,MAAMqB,CAAC,GAAG2O,OAAO,CAAChQ,CAAC,CAAC;EACpB,MAAMgO,CAAC,GAAG3M,CAAC,CAAC,CAAC,CAAC;EACd,MAAMsE,CAAC,GAAGyI,GAAG,CAAC/M,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,MAAM0M,CAAC,GAAGK,GAAG,CAAC/M,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,OAAOrB,CAAC,CAACqB,CAAC,GAAG,GAAG,GACZ,QAAQ2M,CAAC,KAAKrI,CAAC,MAAMoI,CAAC,MAAMI,GAAG,CAACnO,CAAC,CAACqB,CAAC,CAAC,GAAG,GACvC,OAAO2M,CAAC,KAAKrI,CAAC,MAAMoI,CAAC,IAAI;AAC/B;AACA,MAAMzP,GAAG,GAAG;EACV2B,CAAC,EAAE,MAAM;EACT4Q,CAAC,EAAE,OAAO;EACVC,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,KAAK;EACRC,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,QAAQ;EACXC,CAAC,EAAE,OAAO;EACV5C,CAAC,EAAE,IAAI;EACP6C,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,IAAI;EACP7C,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,OAAO;EACVC,CAAC,EAAE,OAAO;EACV4C,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,UAAU;EACb5C,CAAC,EAAE,IAAI;EACP6C,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,OAAO;EACVhD,CAAC,EAAE,IAAI;EACPiD,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,MAAM;EACTC,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,OAAO;EACVC,CAAC,EAAE;AACL,CAAC;AACD,MAAMC,OAAO,GAAG;EACdC,MAAM,EAAE,QAAQ;EAChBC,WAAW,EAAE,QAAQ;EACrBC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,QAAQ;EACnBC,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE,QAAQ;EACfC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,GAAG;EACVC,YAAY,EAAE,QAAQ;EACtBC,EAAE,EAAE,IAAI;EACRC,OAAO,EAAE,QAAQ;EACjBC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,QAAQ;EACnBC,MAAM,EAAE,QAAQ;EAChBC,QAAQ,EAAE,QAAQ;EAClBC,OAAO,EAAE,QAAQ;EACjBC,GAAG,EAAE,QAAQ;EACbC,WAAW,EAAE,QAAQ;EACrBC,OAAO,EAAE,QAAQ;EACjBC,OAAO,EAAE,QAAQ;EACjBC,IAAI,EAAE,MAAM;EACZC,GAAG,EAAE,IAAI;EACTC,KAAK,EAAE,MAAM;EACbC,OAAO,EAAE,QAAQ;EACjBC,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,QAAQ;EACdC,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE,QAAQ;EACjBC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,QAAQ;EACfC,GAAG,EAAE,QAAQ;EACbC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,QAAQ;EACfC,GAAG,EAAE,MAAM;EACXC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,QAAQ,EAAE,MAAM;EAChBC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,QAAQ,EAAE,QAAQ;EAClBC,QAAQ,EAAE,QAAQ;EAClBC,QAAQ,EAAE,QAAQ;EAClBC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE,QAAQ;EACjBC,SAAS,EAAE,QAAQ;EACnBC,GAAG,EAAE,QAAQ;EACbC,MAAM,EAAE,QAAQ;EAChBC,GAAG,EAAE,QAAQ;EACbC,GAAG,EAAE,MAAM;EACXC,KAAK,EAAE,QAAQ;EACfC,GAAG,EAAE,QAAQ;EACbC,OAAO,EAAE,QAAQ;EACjBC,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE,QAAQ;EACjBC,KAAK,EAAE,QAAQ;EACfC,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE,QAAQ;EACfC,MAAM,EAAE,QAAQ;EAChBC,SAAS,EAAE,QAAQ;EACnBC,OAAO,EAAE,QAAQ;EACjBC,UAAU,EAAE,QAAQ;EACpBC,GAAG,EAAE,QAAQ;EACbC,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE,QAAQ;EACfC,SAAS,EAAE,QAAQ;EACnBC,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,QAAQ;EACdC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,QAAQ;EACfC,OAAO,EAAE,QAAQ;EACjBC,GAAG,EAAE,QAAQ;EACbC,IAAI,EAAE,MAAM;EACZC,OAAO,EAAE,QAAQ;EACjBC,GAAG,EAAE,QAAQ;EACbC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,QAAQ;EACfC,UAAU,EAAE,QAAQ;EACpBC,GAAG,EAAE,IAAI;EACTC,KAAK,EAAE,QAAQ;EACfC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,MAAM;EACjBC,GAAG,EAAE,QAAQ;EACbC,QAAQ,EAAE,QAAQ;EAClBC,UAAU,EAAE,QAAQ;EACpBC,OAAO,EAAE,QAAQ;EACjBC,QAAQ,EAAE,QAAQ;EAClBC,OAAO,EAAE,QAAQ;EACjBC,UAAU,EAAE,QAAQ;EACpBC,IAAI,EAAE,IAAI;EACVC,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE,QAAQ;EACdC,OAAO,EAAE,QAAQ;EACjBC,KAAK,EAAE,QAAQ;EACfC,OAAO,EAAE,QAAQ;EACjBC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,QAAQ;EACnBC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,QAAQ;EACfC,UAAU,EAAE,QAAQ;EACpBC,SAAS,EAAE,QAAQ;EACnBC,OAAO,EAAE,QAAQ;EACjBC,IAAI,EAAE,QAAQ;EACdC,GAAG,EAAE,QAAQ;EACbC,IAAI,EAAE,QAAQ;EACdC,OAAO,EAAE,QAAQ;EACjBC,KAAK,EAAE,QAAQ;EACfC,WAAW,EAAE,QAAQ;EACrBC,EAAE,EAAE,QAAQ;EACZC,QAAQ,EAAE,QAAQ;EAClBC,KAAK,EAAE,QAAQ;EACfC,SAAS,EAAE,QAAQ;EACnBC,KAAK,EAAE,QAAQ;EACfC,SAAS,EAAE,QAAQ;EACnBC,KAAK,EAAE,QAAQ;EACfC,OAAO,EAAE,QAAQ;EACjBC,KAAK,EAAE,QAAQ;EACfC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,QAAQ;EACfC,GAAG,EAAE,QAAQ;EACbC,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,MAAM;EAChBC,MAAM,EAAE,QAAQ;EAChBC,GAAG,EAAE,QAAQ;EACbC,GAAG,EAAE,MAAM;EACXC,KAAK,EAAE,QAAQ;EACfC,MAAM,EAAE,QAAQ;EAChBC,EAAE,EAAE,QAAQ;EACZC,KAAK,EAAE,QAAQ;EACfC,GAAG,EAAE,QAAQ;EACbC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,QAAQ;EACnBC,EAAE,EAAE,QAAQ;EACZC,KAAK,EAAE;AACT,CAAC;AACD,SAASC,MAAMA,CAAA,EAAG;EAChB,MAAMC,QAAQ,GAAG,CAAC,CAAC;EACnB,MAAM7d,IAAI,GAAG3B,MAAM,CAAC2B,IAAI,CAACuU,OAAO,CAAC;EACjC,MAAMuJ,KAAK,GAAGzf,MAAM,CAAC2B,IAAI,CAACY,GAAG,CAAC;EAC9B,IAAId,CAAC,EAAEie,CAAC,EAAE/c,CAAC,EAAEgd,EAAE,EAAEC,EAAE;EACnB,KAAKne,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,IAAI,CAACC,MAAM,EAAEH,CAAC,EAAE,EAAE;IAChCke,EAAE,GAAGC,EAAE,GAAGje,IAAI,CAACF,CAAC,CAAC;IACjB,KAAKie,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAAC7d,MAAM,EAAE8d,CAAC,EAAE,EAAE;MACjC/c,CAAC,GAAG8c,KAAK,CAACC,CAAC,CAAC;MACZE,EAAE,GAAGA,EAAE,CAACC,OAAO,CAACld,CAAC,EAAEJ,GAAG,CAACI,CAAC,CAAC,CAAC;IAC5B;IACAA,CAAC,GAAGmd,QAAQ,CAAC5J,OAAO,CAACyJ,EAAE,CAAC,EAAE,EAAE,CAAC;IAC7BH,QAAQ,CAACI,EAAE,CAAC,GAAG,CAACjd,CAAC,IAAI,EAAE,GAAG,IAAI,EAAEA,CAAC,IAAI,CAAC,GAAG,IAAI,EAAEA,CAAC,GAAG,IAAI,CAAC;EAC1D;EACA,OAAO6c,QAAQ;AACjB;AACA,IAAIO,KAAK;AACT,SAASC,SAASA,CAAChb,GAAG,EAAE;EACtB,IAAI,CAAC+a,KAAK,EAAE;IACVA,KAAK,GAAGR,MAAM,CAAC,CAAC;IAChBQ,KAAK,CAACE,WAAW,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAClC;EACA,MAAM3a,CAAC,GAAGya,KAAK,CAAC/a,GAAG,CAACkb,WAAW,CAAC,CAAC,CAAC;EAClC,OAAO5a,CAAC,IAAI;IACV8N,CAAC,EAAE9N,CAAC,CAAC,CAAC,CAAC;IACP+N,CAAC,EAAE/N,CAAC,CAAC,CAAC,CAAC;IACPC,CAAC,EAAED,CAAC,CAAC,CAAC,CAAC;IACPA,CAAC,EAAEA,CAAC,CAAC1D,MAAM,KAAK,CAAC,GAAG0D,CAAC,CAAC,CAAC,CAAC,GAAG;EAC7B,CAAC;AACH;AACA,MAAM6a,MAAM,GAAG,sGAAsG;AACrH,SAASC,QAAQA,CAACpb,GAAG,EAAE;EACrB,MAAM4M,CAAC,GAAGuO,MAAM,CAAC3L,IAAI,CAACxP,GAAG,CAAC;EAC1B,IAAIM,CAAC,GAAG,GAAG;EACX,IAAI8N,CAAC,EAAEC,CAAC,EAAE9N,CAAC;EACX,IAAI,CAACqM,CAAC,EAAE;IACN;EACF;EACA,IAAIA,CAAC,CAAC,CAAC,CAAC,KAAKwB,CAAC,EAAE;IACd,MAAMnP,CAAC,GAAG,CAAC2N,CAAC,CAAC,CAAC,CAAC;IACftM,CAAC,GAAGsM,CAAC,CAAC,CAAC,CAAC,GAAGM,GAAG,CAACjO,CAAC,CAAC,GAAG8N,GAAG,CAAC9N,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;EAC1C;EACAmP,CAAC,GAAG,CAACxB,CAAC,CAAC,CAAC,CAAC;EACTyB,CAAC,GAAG,CAACzB,CAAC,CAAC,CAAC,CAAC;EACTrM,CAAC,GAAG,CAACqM,CAAC,CAAC,CAAC,CAAC;EACTwB,CAAC,GAAG,GAAG,IAAIxB,CAAC,CAAC,CAAC,CAAC,GAAGM,GAAG,CAACkB,CAAC,CAAC,GAAGrB,GAAG,CAACqB,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;EAC1CC,CAAC,GAAG,GAAG,IAAIzB,CAAC,CAAC,CAAC,CAAC,GAAGM,GAAG,CAACmB,CAAC,CAAC,GAAGtB,GAAG,CAACsB,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;EAC1C9N,CAAC,GAAG,GAAG,IAAIqM,CAAC,CAAC,CAAC,CAAC,GAAGM,GAAG,CAAC3M,CAAC,CAAC,GAAGwM,GAAG,CAACxM,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;EAC1C,OAAO;IACL6N,CAAC,EAAEA,CAAC;IACJC,CAAC,EAAEA,CAAC;IACJ9N,CAAC,EAAEA,CAAC;IACJD,CAAC,EAAEA;EACL,CAAC;AACH;AACA,SAAS+a,SAASA,CAACpc,CAAC,EAAE;EACpB,OAAOA,CAAC,KACNA,CAAC,CAACqB,CAAC,GAAG,GAAG,GACL,QAAQrB,CAAC,CAACmP,CAAC,KAAKnP,CAAC,CAACoP,CAAC,KAAKpP,CAAC,CAACsB,CAAC,KAAK6M,GAAG,CAACnO,CAAC,CAACqB,CAAC,CAAC,GAAG,GAC3C,OAAOrB,CAAC,CAACmP,CAAC,KAAKnP,CAAC,CAACoP,CAAC,KAAKpP,CAAC,CAACsB,CAAC,GAAG,CAClC;AACH;AACA,MAAM+a,EAAE,GAAGrc,CAAC,IAAIA,CAAC,IAAI,SAAS,GAAGA,CAAC,GAAG,KAAK,GAAG6B,IAAI,CAACiB,GAAG,CAAC9C,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,GAAG,KAAK,GAAG,KAAK;AACnF,MAAMmI,IAAI,GAAGnI,CAAC,IAAIA,CAAC,IAAI,OAAO,GAAGA,CAAC,GAAG,KAAK,GAAG6B,IAAI,CAACiB,GAAG,CAAC,CAAC9C,CAAC,GAAG,KAAK,IAAI,KAAK,EAAE,GAAG,CAAC;AAC/E,SAASsc,WAAWA,CAACC,IAAI,EAAEC,IAAI,EAAEjR,CAAC,EAAE;EAClC,MAAM4D,CAAC,GAAGhH,IAAI,CAACgG,GAAG,CAACoO,IAAI,CAACpN,CAAC,CAAC,CAAC;EAC3B,MAAMC,CAAC,GAAGjH,IAAI,CAACgG,GAAG,CAACoO,IAAI,CAACnN,CAAC,CAAC,CAAC;EAC3B,MAAM9N,CAAC,GAAG6G,IAAI,CAACgG,GAAG,CAACoO,IAAI,CAACjb,CAAC,CAAC,CAAC;EAC3B,OAAO;IACL6N,CAAC,EAAEjB,GAAG,CAACmO,EAAE,CAAClN,CAAC,GAAG5D,CAAC,IAAIpD,IAAI,CAACgG,GAAG,CAACqO,IAAI,CAACrN,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC;IAC3CC,CAAC,EAAElB,GAAG,CAACmO,EAAE,CAACjN,CAAC,GAAG7D,CAAC,IAAIpD,IAAI,CAACgG,GAAG,CAACqO,IAAI,CAACpN,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC;IAC3C9N,CAAC,EAAE4M,GAAG,CAACmO,EAAE,CAAC/a,CAAC,GAAGiK,CAAC,IAAIpD,IAAI,CAACgG,GAAG,CAACqO,IAAI,CAAClb,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC;IAC3CD,CAAC,EAAEkb,IAAI,CAAClb,CAAC,GAAGkK,CAAC,IAAIiR,IAAI,CAACnb,CAAC,GAAGkb,IAAI,CAAClb,CAAC;EAClC,CAAC;AACH;AACA,SAASob,MAAMA,CAACzc,CAAC,EAAExC,CAAC,EAAEkf,KAAK,EAAE;EAC3B,IAAI1c,CAAC,EAAE;IACL,IAAIW,GAAG,GAAGqP,OAAO,CAAChQ,CAAC,CAAC;IACpBW,GAAG,CAACnD,CAAC,CAAC,GAAGqE,IAAI,CAACqC,GAAG,CAAC,CAAC,EAAErC,IAAI,CAACoC,GAAG,CAACtD,GAAG,CAACnD,CAAC,CAAC,GAAGmD,GAAG,CAACnD,CAAC,CAAC,GAAGkf,KAAK,EAAElf,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;IAC1EmD,GAAG,GAAGuP,OAAO,CAACvP,GAAG,CAAC;IAClBX,CAAC,CAACmP,CAAC,GAAGxO,GAAG,CAAC,CAAC,CAAC;IACZX,CAAC,CAACoP,CAAC,GAAGzO,GAAG,CAAC,CAAC,CAAC;IACZX,CAAC,CAACsB,CAAC,GAAGX,GAAG,CAAC,CAAC,CAAC;EACd;AACF;AACA,SAASgc,KAAKA,CAAC3c,CAAC,EAAE4c,KAAK,EAAE;EACvB,OAAO5c,CAAC,GAAGjE,MAAM,CAACsP,MAAM,CAACuR,KAAK,IAAI,CAAC,CAAC,EAAE5c,CAAC,CAAC,GAAGA,CAAC;AAC9C;AACA,SAAS6c,UAAUA,CAACC,KAAK,EAAE;EACzB,IAAI9c,CAAC,GAAG;IAACmP,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAE9N,CAAC,EAAE,CAAC;IAAED,CAAC,EAAE;EAAG,CAAC;EAClC,IAAIxF,KAAK,CAACD,OAAO,CAACkhB,KAAK,CAAC,EAAE;IACxB,IAAIA,KAAK,CAACnf,MAAM,IAAI,CAAC,EAAE;MACrBqC,CAAC,GAAG;QAACmP,CAAC,EAAE2N,KAAK,CAAC,CAAC,CAAC;QAAE1N,CAAC,EAAE0N,KAAK,CAAC,CAAC,CAAC;QAAExb,CAAC,EAAEwb,KAAK,CAAC,CAAC,CAAC;QAAEzb,CAAC,EAAE;MAAG,CAAC;MACnD,IAAIyb,KAAK,CAACnf,MAAM,GAAG,CAAC,EAAE;QACpBqC,CAAC,CAACqB,CAAC,GAAG6M,GAAG,CAAC4O,KAAK,CAAC,CAAC,CAAC,CAAC;MACrB;IACF;EACF,CAAC,MAAM;IACL9c,CAAC,GAAG2c,KAAK,CAACG,KAAK,EAAE;MAAC3N,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAE9N,CAAC,EAAE,CAAC;MAAED,CAAC,EAAE;IAAC,CAAC,CAAC;IAC1CrB,CAAC,CAACqB,CAAC,GAAG6M,GAAG,CAAClO,CAAC,CAACqB,CAAC,CAAC;EAChB;EACA,OAAOrB,CAAC;AACV;AACA,SAAS+c,aAAaA,CAAChc,GAAG,EAAE;EAC1B,IAAIA,GAAG,CAACC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACzB,OAAOmb,QAAQ,CAACpb,GAAG,CAAC;EACtB;EACA,OAAOuP,QAAQ,CAACvP,GAAG,CAAC;AACtB;AACA,MAAMic,KAAK,CAAC;EACVC,WAAWA,CAACH,KAAK,EAAE;IACjB,IAAIA,KAAK,YAAYE,KAAK,EAAE;MAC1B,OAAOF,KAAK;IACd;IACA,MAAMhhB,IAAI,GAAG,OAAOghB,KAAK;IACzB,IAAI9c,CAAC;IACL,IAAIlE,IAAI,KAAK,QAAQ,EAAE;MACrBkE,CAAC,GAAG6c,UAAU,CAACC,KAAK,CAAC;IACvB,CAAC,MAAM,IAAIhhB,IAAI,KAAK,QAAQ,EAAE;MAC5BkE,CAAC,GAAGqP,QAAQ,CAACyN,KAAK,CAAC,IAAIf,SAAS,CAACe,KAAK,CAAC,IAAIC,aAAa,CAACD,KAAK,CAAC;IACjE;IACA,IAAI,CAACI,IAAI,GAAGld,CAAC;IACb,IAAI,CAACmd,MAAM,GAAG,CAAC,CAACnd,CAAC;EACnB;EACA,IAAIod,KAAKA,CAAA,EAAG;IACV,OAAO,IAAI,CAACD,MAAM;EACpB;EACA,IAAIrN,GAAGA,CAAA,EAAG;IACR,IAAI9P,CAAC,GAAG2c,KAAK,CAAC,IAAI,CAACO,IAAI,CAAC;IACxB,IAAIld,CAAC,EAAE;MACLA,CAAC,CAACqB,CAAC,GAAG8M,GAAG,CAACnO,CAAC,CAACqB,CAAC,CAAC;IAChB;IACA,OAAOrB,CAAC;EACV;EACA,IAAI8P,GAAGA,CAACzP,GAAG,EAAE;IACX,IAAI,CAAC6c,IAAI,GAAGL,UAAU,CAACxc,GAAG,CAAC;EAC7B;EACA+b,SAASA,CAAA,EAAG;IACV,OAAO,IAAI,CAACe,MAAM,GAAGf,SAAS,CAAC,IAAI,CAACc,IAAI,CAAC,GAAGtd,SAAS;EACvD;EACA4P,SAASA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC2N,MAAM,GAAG3N,SAAS,CAAC,IAAI,CAAC0N,IAAI,CAAC,GAAGtd,SAAS;EACvD;EACAgR,SAASA,CAAA,EAAG;IACV,OAAO,IAAI,CAACuM,MAAM,GAAGvM,SAAS,CAAC,IAAI,CAACsM,IAAI,CAAC,GAAGtd,SAAS;EACvD;EACAyd,GAAGA,CAACC,KAAK,EAAEC,MAAM,EAAE;IACjB,IAAID,KAAK,EAAE;MACT,MAAME,EAAE,GAAG,IAAI,CAAC1N,GAAG;MACnB,MAAM2N,EAAE,GAAGH,KAAK,CAACxN,GAAG;MACpB,IAAI4N,EAAE;MACN,MAAMlZ,CAAC,GAAG+Y,MAAM,KAAKG,EAAE,GAAG,GAAG,GAAGH,MAAM;MACtC,MAAM1N,CAAC,GAAG,CAAC,GAAGrL,CAAC,GAAG,CAAC;MACnB,MAAMnD,CAAC,GAAGmc,EAAE,CAACnc,CAAC,GAAGoc,EAAE,CAACpc,CAAC;MACrB,MAAMsc,EAAE,GAAG,CAAC,CAAC9N,CAAC,GAAGxO,CAAC,KAAK,CAAC,CAAC,GAAGwO,CAAC,GAAG,CAACA,CAAC,GAAGxO,CAAC,KAAK,CAAC,GAAGwO,CAAC,GAAGxO,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG;MACjEqc,EAAE,GAAG,CAAC,GAAGC,EAAE;MACXH,EAAE,CAACrO,CAAC,GAAG,IAAI,GAAGwO,EAAE,GAAGH,EAAE,CAACrO,CAAC,GAAGuO,EAAE,GAAGD,EAAE,CAACtO,CAAC,GAAG,GAAG;MACzCqO,EAAE,CAACpO,CAAC,GAAG,IAAI,GAAGuO,EAAE,GAAGH,EAAE,CAACpO,CAAC,GAAGsO,EAAE,GAAGD,EAAE,CAACrO,CAAC,GAAG,GAAG;MACzCoO,EAAE,CAAClc,CAAC,GAAG,IAAI,GAAGqc,EAAE,GAAGH,EAAE,CAAClc,CAAC,GAAGoc,EAAE,GAAGD,EAAE,CAACnc,CAAC,GAAG,GAAG;MACzCkc,EAAE,CAACnc,CAAC,GAAGmD,CAAC,GAAGgZ,EAAE,CAACnc,CAAC,GAAG,CAAC,CAAC,GAAGmD,CAAC,IAAIiZ,EAAE,CAACpc,CAAC;MAChC,IAAI,CAACyO,GAAG,GAAG0N,EAAE;IACf;IACA,OAAO,IAAI;EACb;EACAlB,WAAWA,CAACgB,KAAK,EAAE/R,CAAC,EAAE;IACpB,IAAI+R,KAAK,EAAE;MACT,IAAI,CAACJ,IAAI,GAAGZ,WAAW,CAAC,IAAI,CAACY,IAAI,EAAEI,KAAK,CAACJ,IAAI,EAAE3R,CAAC,CAAC;IACnD;IACA,OAAO,IAAI;EACb;EACAoR,KAAKA,CAAA,EAAG;IACN,OAAO,IAAIK,KAAK,CAAC,IAAI,CAAClN,GAAG,CAAC;EAC5B;EACAP,KAAKA,CAAClO,CAAC,EAAE;IACP,IAAI,CAAC6b,IAAI,CAAC7b,CAAC,GAAG6M,GAAG,CAAC7M,CAAC,CAAC;IACpB,OAAO,IAAI;EACb;EACAuc,OAAOA,CAAClB,KAAK,EAAE;IACb,MAAM5M,GAAG,GAAG,IAAI,CAACoN,IAAI;IACrBpN,GAAG,CAACzO,CAAC,IAAI,CAAC,GAAGqb,KAAK;IAClB,OAAO,IAAI;EACb;EACAmB,SAASA,CAAA,EAAG;IACV,MAAM/N,GAAG,GAAG,IAAI,CAACoN,IAAI;IACrB,MAAMY,GAAG,GAAGnb,KAAK,CAACmN,GAAG,CAACX,CAAC,GAAG,GAAG,GAAGW,GAAG,CAACV,CAAC,GAAG,IAAI,GAAGU,GAAG,CAACxO,CAAC,GAAG,IAAI,CAAC;IAC5DwO,GAAG,CAACX,CAAC,GAAGW,GAAG,CAACV,CAAC,GAAGU,GAAG,CAACxO,CAAC,GAAGwc,GAAG;IAC3B,OAAO,IAAI;EACb;EACAC,OAAOA,CAACrB,KAAK,EAAE;IACb,MAAM5M,GAAG,GAAG,IAAI,CAACoN,IAAI;IACrBpN,GAAG,CAACzO,CAAC,IAAI,CAAC,GAAGqb,KAAK;IAClB,OAAO,IAAI;EACb;EACAsB,MAAMA,CAAA,EAAG;IACP,MAAMhe,CAAC,GAAG,IAAI,CAACkd,IAAI;IACnBld,CAAC,CAACmP,CAAC,GAAG,GAAG,GAAGnP,CAAC,CAACmP,CAAC;IACfnP,CAAC,CAACoP,CAAC,GAAG,GAAG,GAAGpP,CAAC,CAACoP,CAAC;IACfpP,CAAC,CAACsB,CAAC,GAAG,GAAG,GAAGtB,CAAC,CAACsB,CAAC;IACf,OAAO,IAAI;EACb;EACA2c,OAAOA,CAACvB,KAAK,EAAE;IACbD,MAAM,CAAC,IAAI,CAACS,IAAI,EAAE,CAAC,EAAER,KAAK,CAAC;IAC3B,OAAO,IAAI;EACb;EACAwB,MAAMA,CAACxB,KAAK,EAAE;IACZD,MAAM,CAAC,IAAI,CAACS,IAAI,EAAE,CAAC,EAAE,CAACR,KAAK,CAAC;IAC5B,OAAO,IAAI;EACb;EACAyB,QAAQA,CAACzB,KAAK,EAAE;IACdD,MAAM,CAAC,IAAI,CAACS,IAAI,EAAE,CAAC,EAAER,KAAK,CAAC;IAC3B,OAAO,IAAI;EACb;EACA0B,UAAUA,CAAC1B,KAAK,EAAE;IAChBD,MAAM,CAAC,IAAI,CAACS,IAAI,EAAE,CAAC,EAAE,CAACR,KAAK,CAAC;IAC5B,OAAO,IAAI;EACb;EACAhM,MAAMA,CAACC,GAAG,EAAE;IACVD,MAAM,CAAC,IAAI,CAACwM,IAAI,EAAEvM,GAAG,CAAC;IACtB,OAAO,IAAI;EACb;AACF;AACA,SAAS0N,SAASA,CAACvB,KAAK,EAAE;EACxB,OAAO,IAAIE,KAAK,CAACF,KAAK,CAAC;AACzB;AAEA,SAASwB,mBAAmBA,CAAC3iB,KAAK,EAAE;EAClC,IAAIA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IACtC,MAAMG,IAAI,GAAGH,KAAK,CAACM,QAAQ,CAAC,CAAC;IAC7B,OAAOH,IAAI,KAAK,wBAAwB,IAAIA,IAAI,KAAK,yBAAyB;EAChF;EACA,OAAO,KAAK;AACd;AACA,SAASwhB,KAAKA,CAAC3hB,KAAK,EAAE;EACpB,OAAO2iB,mBAAmB,CAAC3iB,KAAK,CAAC,GAAGA,KAAK,GAAG0iB,SAAS,CAAC1iB,KAAK,CAAC;AAC9D;AACA,SAAS4iB,aAAaA,CAAC5iB,KAAK,EAAE;EAC5B,OAAO2iB,mBAAmB,CAAC3iB,KAAK,CAAC,GAC7BA,KAAK,GACL0iB,SAAS,CAAC1iB,KAAK,CAAC,CAACwiB,QAAQ,CAAC,GAAG,CAAC,CAACD,MAAM,CAAC,GAAG,CAAC,CAAC1O,SAAS,CAAC,CAAC;AAC5D;AAEA,MAAMgP,SAAS,GAAGziB,MAAM,CAACyC,MAAM,CAAC,IAAI,CAAC;AACrC,MAAMigB,WAAW,GAAG1iB,MAAM,CAACyC,MAAM,CAAC,IAAI,CAAC;AACvC,SAASkgB,UAAUA,CAACC,IAAI,EAAE/f,GAAG,EAAE;EAC7B,IAAI,CAACA,GAAG,EAAE;IACR,OAAO+f,IAAI;EACb;EACA,MAAMjhB,IAAI,GAAGkB,GAAG,CAAC8B,KAAK,CAAC,GAAG,CAAC;EAC3B,KAAK,IAAIlD,CAAC,GAAG,CAAC,EAAEgG,CAAC,GAAG9F,IAAI,CAACC,MAAM,EAAEH,CAAC,GAAGgG,CAAC,EAAE,EAAEhG,CAAC,EAAE;IAC3C,MAAMkB,CAAC,GAAGhB,IAAI,CAACF,CAAC,CAAC;IACjBmhB,IAAI,GAAGA,IAAI,CAACjgB,CAAC,CAAC,KAAKigB,IAAI,CAACjgB,CAAC,CAAC,GAAG3C,MAAM,CAACyC,MAAM,CAAC,IAAI,CAAC,CAAC;EACnD;EACA,OAAOmgB,IAAI;AACb;AACA,SAAS3W,GAAGA,CAAC4W,IAAI,EAAEnf,KAAK,EAAEoH,MAAM,EAAE;EAChC,IAAI,OAAOpH,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAOP,KAAK,CAACwf,UAAU,CAACE,IAAI,EAAEnf,KAAK,CAAC,EAAEoH,MAAM,CAAC;EAC/C;EACA,OAAO3H,KAAK,CAACwf,UAAU,CAACE,IAAI,EAAE,EAAE,CAAC,EAAEnf,KAAK,CAAC;AAC3C;AACA,MAAMof,QAAQ,CAAC;EACb5B,WAAWA,CAAC6B,YAAY,EAAE;IACxB,IAAI,CAACC,SAAS,GAAGnf,SAAS;IAC1B,IAAI,CAACof,eAAe,GAAG,iBAAiB;IACxC,IAAI,CAACC,WAAW,GAAG,iBAAiB;IACpC,IAAI,CAAC3B,KAAK,GAAG,MAAM;IACnB,IAAI,CAAC4B,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,CAACC,gBAAgB,GAAIC,OAAO,IAAKA,OAAO,CAACC,KAAK,CAACC,QAAQ,CAACC,mBAAmB,CAAC,CAAC;IACjF,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,CAACC,MAAM,GAAG,CACZ,WAAW,EACX,UAAU,EACV,OAAO,EACP,YAAY,EACZ,WAAW,CACZ;IACD,IAAI,CAACC,IAAI,GAAG;MACVC,MAAM,EAAE,oDAAoD;MAC5Dpe,IAAI,EAAE,EAAE;MACRqe,KAAK,EAAE,QAAQ;MACfC,UAAU,EAAE,GAAG;MACftC,MAAM,EAAE;IACV,CAAC;IACD,IAAI,CAACuC,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAACC,oBAAoB,GAAG,CAACC,GAAG,EAAEjhB,OAAO,KAAKwf,aAAa,CAACxf,OAAO,CAACigB,eAAe,CAAC;IACpF,IAAI,CAACiB,gBAAgB,GAAG,CAACD,GAAG,EAAEjhB,OAAO,KAAKwf,aAAa,CAACxf,OAAO,CAACkgB,WAAW,CAAC;IAC5E,IAAI,CAACiB,UAAU,GAAG,CAACF,GAAG,EAAEjhB,OAAO,KAAKwf,aAAa,CAACxf,OAAO,CAACue,KAAK,CAAC;IAChE,IAAI,CAAC6C,SAAS,GAAG,GAAG;IACpB,IAAI,CAACC,WAAW,GAAG;MACjBC,IAAI,EAAE,SAAS;MACfC,SAAS,EAAE,IAAI;MACfC,gBAAgB,EAAE;IACpB,CAAC;IACD,IAAI,CAACC,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,KAAK,GAAGlhB,SAAS;IACtB,IAAI,CAACmhB,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,uBAAuB,GAAG,IAAI;IACnC,IAAI,CAACC,QAAQ,CAACpC,YAAY,CAAC;EAC7B;EACA9W,GAAGA,CAACvI,KAAK,EAAEoH,MAAM,EAAE;IACjB,OAAOmB,GAAG,CAAC,IAAI,EAAEvI,KAAK,EAAEoH,MAAM,CAAC;EACjC;EACAsa,GAAGA,CAAC1hB,KAAK,EAAE;IACT,OAAOif,UAAU,CAAC,IAAI,EAAEjf,KAAK,CAAC;EAChC;EACAyhB,QAAQA,CAACzhB,KAAK,EAAEoH,MAAM,EAAE;IACtB,OAAOmB,GAAG,CAACyW,WAAW,EAAEhf,KAAK,EAAEoH,MAAM,CAAC;EACxC;EACAua,QAAQA,CAAC3hB,KAAK,EAAEoH,MAAM,EAAE;IACtB,OAAOmB,GAAG,CAACwW,SAAS,EAAE/e,KAAK,EAAEoH,MAAM,CAAC;EACtC;EACAwa,KAAKA,CAAC5hB,KAAK,EAAE6hB,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAE;IAC1C,MAAMC,WAAW,GAAG/C,UAAU,CAAC,IAAI,EAAEjf,KAAK,CAAC;IAC3C,MAAMiiB,iBAAiB,GAAGhD,UAAU,CAAC,IAAI,EAAE6C,WAAW,CAAC;IACvD,MAAMI,WAAW,GAAG,GAAG,GAAGL,IAAI;IAC9BvlB,MAAM,CAAC6lB,gBAAgB,CAACH,WAAW,EAAE;MACnC,CAACE,WAAW,GAAG;QACbhmB,KAAK,EAAE8lB,WAAW,CAACH,IAAI,CAAC;QACxBO,QAAQ,EAAE;MACZ,CAAC;MACD,CAACP,IAAI,GAAG;QACNja,UAAU,EAAE,IAAI;QAChB8Z,GAAGA,CAAA,EAAG;UACJ,MAAMW,KAAK,GAAG,IAAI,CAACH,WAAW,CAAC;UAC/B,MAAMpjB,MAAM,GAAGmjB,iBAAiB,CAACF,UAAU,CAAC;UAC5C,IAAIplB,QAAQ,CAAC0lB,KAAK,CAAC,EAAE;YACnB,OAAO/lB,MAAM,CAACsP,MAAM,CAAC,CAAC,CAAC,EAAE9M,MAAM,EAAEujB,KAAK,CAAC;UACzC;UACA,OAAOplB,cAAc,CAAColB,KAAK,EAAEvjB,MAAM,CAAC;QACtC,CAAC;QACDyJ,GAAGA,CAACrM,KAAK,EAAE;UACT,IAAI,CAACgmB,WAAW,CAAC,GAAGhmB,KAAK;QAC3B;MACF;IACF,CAAC,CAAC;EACJ;AACF;AACA,IAAIomB,QAAQ,GAAG,IAAIlD,QAAQ,CAAC;EAC1BmD,WAAW,EAAGV,IAAI,IAAK,CAACA,IAAI,CAACW,UAAU,CAAC,IAAI,CAAC;EAC7CC,UAAU,EAAGZ,IAAI,IAAKA,IAAI,KAAK,QAAQ;EACvCxB,KAAK,EAAE;IACLqC,SAAS,EAAE;EACb,CAAC;EACD/B,WAAW,EAAE;IACX4B,WAAW,EAAE,KAAK;IAClBE,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAEF,SAASE,YAAYA,CAAC1C,IAAI,EAAE;EAC1B,IAAI,CAACA,IAAI,IAAIhkB,aAAa,CAACgkB,IAAI,CAACne,IAAI,CAAC,IAAI7F,aAAa,CAACgkB,IAAI,CAACC,MAAM,CAAC,EAAE;IACnE,OAAO,IAAI;EACb;EACA,OAAO,CAACD,IAAI,CAACE,KAAK,GAAGF,IAAI,CAACE,KAAK,GAAG,GAAG,GAAG,EAAE,KACvCF,IAAI,CAACnC,MAAM,GAAGmC,IAAI,CAACnC,MAAM,GAAG,GAAG,GAAG,EAAE,CAAC,GACtCmC,IAAI,CAACne,IAAI,GAAG,KAAK,GACjBme,IAAI,CAACC,MAAM;AACf;AACA,SAAS0C,YAAYA,CAACrC,GAAG,EAAEsC,IAAI,EAAEC,EAAE,EAAEC,OAAO,EAAEC,MAAM,EAAE;EACpD,IAAIC,SAAS,GAAGJ,IAAI,CAACG,MAAM,CAAC;EAC5B,IAAI,CAACC,SAAS,EAAE;IACdA,SAAS,GAAGJ,IAAI,CAACG,MAAM,CAAC,GAAGzC,GAAG,CAAC2C,WAAW,CAACF,MAAM,CAAC,CAACG,KAAK;IACxDL,EAAE,CAAC1hB,IAAI,CAAC4hB,MAAM,CAAC;EACjB;EACA,IAAIC,SAAS,GAAGF,OAAO,EAAE;IACvBA,OAAO,GAAGE,SAAS;EACrB;EACA,OAAOF,OAAO;AAChB;AACA,SAASK,YAAYA,CAAC7C,GAAG,EAAEN,IAAI,EAAEoD,aAAa,EAAEC,KAAK,EAAE;EACrDA,KAAK,GAAGA,KAAK,IAAI,CAAC,CAAC;EACnB,IAAIT,IAAI,GAAGS,KAAK,CAACT,IAAI,GAAGS,KAAK,CAACT,IAAI,IAAI,CAAC,CAAC;EACxC,IAAIC,EAAE,GAAGQ,KAAK,CAACC,cAAc,GAAGD,KAAK,CAACC,cAAc,IAAI,EAAE;EAC1D,IAAID,KAAK,CAACrD,IAAI,KAAKA,IAAI,EAAE;IACvB4C,IAAI,GAAGS,KAAK,CAACT,IAAI,GAAG,CAAC,CAAC;IACtBC,EAAE,GAAGQ,KAAK,CAACC,cAAc,GAAG,EAAE;IAC9BD,KAAK,CAACrD,IAAI,GAAGA,IAAI;EACnB;EACAM,GAAG,CAACiD,IAAI,CAAC,CAAC;EACVjD,GAAG,CAACN,IAAI,GAAGA,IAAI;EACf,IAAI8C,OAAO,GAAG,CAAC;EACf,MAAMzkB,IAAI,GAAG+kB,aAAa,CAACnlB,MAAM;EACjC,IAAIH,CAAC,EAAEie,CAAC,EAAEyH,IAAI,EAAEC,KAAK,EAAEC,WAAW;EAClC,KAAK5lB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGO,IAAI,EAAEP,CAAC,EAAE,EAAE;IACzB2lB,KAAK,GAAGL,aAAa,CAACtlB,CAAC,CAAC;IACxB,IAAI2lB,KAAK,KAAKvjB,SAAS,IAAIujB,KAAK,KAAK,IAAI,IAAIvnB,OAAO,CAACunB,KAAK,CAAC,KAAK,IAAI,EAAE;MACpEX,OAAO,GAAGH,YAAY,CAACrC,GAAG,EAAEsC,IAAI,EAAEC,EAAE,EAAEC,OAAO,EAAEW,KAAK,CAAC;IACvD,CAAC,MAAM,IAAIvnB,OAAO,CAACunB,KAAK,CAAC,EAAE;MACzB,KAAK1H,CAAC,GAAG,CAAC,EAAEyH,IAAI,GAAGC,KAAK,CAACxlB,MAAM,EAAE8d,CAAC,GAAGyH,IAAI,EAAEzH,CAAC,EAAE,EAAE;QAC9C2H,WAAW,GAAGD,KAAK,CAAC1H,CAAC,CAAC;QACtB,IAAI2H,WAAW,KAAKxjB,SAAS,IAAIwjB,WAAW,KAAK,IAAI,IAAI,CAACxnB,OAAO,CAACwnB,WAAW,CAAC,EAAE;UAC9EZ,OAAO,GAAGH,YAAY,CAACrC,GAAG,EAAEsC,IAAI,EAAEC,EAAE,EAAEC,OAAO,EAAEY,WAAW,CAAC;QAC7D;MACF;IACF;EACF;EACApD,GAAG,CAACqD,OAAO,CAAC,CAAC;EACb,MAAMC,KAAK,GAAGf,EAAE,CAAC5kB,MAAM,GAAG,CAAC;EAC3B,IAAI2lB,KAAK,GAAGR,aAAa,CAACnlB,MAAM,EAAE;IAChC,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8lB,KAAK,EAAE9lB,CAAC,EAAE,EAAE;MAC1B,OAAO8kB,IAAI,CAACC,EAAE,CAAC/kB,CAAC,CAAC,CAAC;IACpB;IACA+kB,EAAE,CAAC1a,MAAM,CAAC,CAAC,EAAEyb,KAAK,CAAC;EACrB;EACA,OAAOd,OAAO;AAChB;AACA,SAASe,WAAWA,CAAClE,KAAK,EAAEmE,KAAK,EAAEZ,KAAK,EAAE;EACxC,MAAMzD,gBAAgB,GAAGE,KAAK,CAACoE,uBAAuB;EACtD,MAAMC,SAAS,GAAGd,KAAK,KAAK,CAAC,GAAG/gB,IAAI,CAACqC,GAAG,CAAC0e,KAAK,GAAG,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC;EAC5D,OAAO/gB,IAAI,CAACc,KAAK,CAAC,CAAC6gB,KAAK,GAAGE,SAAS,IAAIvE,gBAAgB,CAAC,GAAGA,gBAAgB,GAAGuE,SAAS;AAC1F;AACA,SAASC,WAAWA,CAACC,MAAM,EAAE5D,GAAG,EAAE;EAChCA,GAAG,GAAGA,GAAG,IAAI4D,MAAM,CAACC,UAAU,CAAC,IAAI,CAAC;EACpC7D,GAAG,CAACiD,IAAI,CAAC,CAAC;EACVjD,GAAG,CAAC8D,cAAc,CAAC,CAAC;EACpB9D,GAAG,CAAC+D,SAAS,CAAC,CAAC,EAAE,CAAC,EAAEH,MAAM,CAAChB,KAAK,EAAEgB,MAAM,CAACI,MAAM,CAAC;EAChDhE,GAAG,CAACqD,OAAO,CAAC,CAAC;AACf;AACA,SAASY,SAASA,CAACjE,GAAG,EAAEjhB,OAAO,EAAEkB,CAAC,EAAEE,CAAC,EAAE;EACrC+jB,eAAe,CAAClE,GAAG,EAAEjhB,OAAO,EAAEkB,CAAC,EAAEE,CAAC,EAAE,IAAI,CAAC;AAC3C;AACA,SAAS+jB,eAAeA,CAAClE,GAAG,EAAEjhB,OAAO,EAAEkB,CAAC,EAAEE,CAAC,EAAE0P,CAAC,EAAE;EAC9C,IAAI/T,IAAI,EAAEqoB,OAAO,EAAEC,OAAO,EAAE7iB,IAAI,EAAE8iB,YAAY,EAAEzB,KAAK;EACrD,MAAMhD,KAAK,GAAG7gB,OAAO,CAACulB,UAAU;EAChC,MAAMC,QAAQ,GAAGxlB,OAAO,CAACwlB,QAAQ;EACjC,MAAMC,MAAM,GAAGzlB,OAAO,CAACylB,MAAM;EAC7B,IAAIC,GAAG,GAAG,CAACF,QAAQ,IAAI,CAAC,IAAIriB,WAAW;EACvC,IAAI0d,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IACtC9jB,IAAI,GAAG8jB,KAAK,CAAC3jB,QAAQ,CAAC,CAAC;IACvB,IAAIH,IAAI,KAAK,2BAA2B,IAAIA,IAAI,KAAK,4BAA4B,EAAE;MACjFkkB,GAAG,CAACiD,IAAI,CAAC,CAAC;MACVjD,GAAG,CAAC0E,SAAS,CAACzkB,CAAC,EAAEE,CAAC,CAAC;MACnB6f,GAAG,CAACtP,MAAM,CAAC+T,GAAG,CAAC;MACfzE,GAAG,CAAC2E,SAAS,CAAC/E,KAAK,EAAE,CAACA,KAAK,CAACgD,KAAK,GAAG,CAAC,EAAE,CAAChD,KAAK,CAACoE,MAAM,GAAG,CAAC,EAAEpE,KAAK,CAACgD,KAAK,EAAEhD,KAAK,CAACoE,MAAM,CAAC;MACpFhE,GAAG,CAACqD,OAAO,CAAC,CAAC;MACb;IACF;EACF;EACA,IAAI5f,KAAK,CAAC+gB,MAAM,CAAC,IAAIA,MAAM,IAAI,CAAC,EAAE;IAChC;EACF;EACAxE,GAAG,CAAC4E,SAAS,CAAC,CAAC;EACf,QAAQhF,KAAK;IACb;MACE,IAAI/P,CAAC,EAAE;QACLmQ,GAAG,CAAC6E,OAAO,CAAC5kB,CAAC,EAAEE,CAAC,EAAE0P,CAAC,GAAG,CAAC,EAAE2U,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE1iB,GAAG,CAAC;MAC7C,CAAC,MAAM;QACLke,GAAG,CAAC8E,GAAG,CAAC7kB,CAAC,EAAEE,CAAC,EAAEqkB,MAAM,EAAE,CAAC,EAAE1iB,GAAG,CAAC;MAC/B;MACAke,GAAG,CAAC+E,SAAS,CAAC,CAAC;MACf;IACF,KAAK,UAAU;MACb/E,GAAG,CAACgF,MAAM,CAAC/kB,CAAC,GAAG4B,IAAI,CAAC4J,GAAG,CAACgZ,GAAG,CAAC,GAAGD,MAAM,EAAErkB,CAAC,GAAG0B,IAAI,CAAC6K,GAAG,CAAC+X,GAAG,CAAC,GAAGD,MAAM,CAAC;MAClEC,GAAG,IAAIpiB,aAAa;MACpB2d,GAAG,CAACiF,MAAM,CAAChlB,CAAC,GAAG4B,IAAI,CAAC4J,GAAG,CAACgZ,GAAG,CAAC,GAAGD,MAAM,EAAErkB,CAAC,GAAG0B,IAAI,CAAC6K,GAAG,CAAC+X,GAAG,CAAC,GAAGD,MAAM,CAAC;MAClEC,GAAG,IAAIpiB,aAAa;MACpB2d,GAAG,CAACiF,MAAM,CAAChlB,CAAC,GAAG4B,IAAI,CAAC4J,GAAG,CAACgZ,GAAG,CAAC,GAAGD,MAAM,EAAErkB,CAAC,GAAG0B,IAAI,CAAC6K,GAAG,CAAC+X,GAAG,CAAC,GAAGD,MAAM,CAAC;MAClExE,GAAG,CAAC+E,SAAS,CAAC,CAAC;MACf;IACF,KAAK,aAAa;MAChBV,YAAY,GAAGG,MAAM,GAAG,KAAK;MAC7BjjB,IAAI,GAAGijB,MAAM,GAAGH,YAAY;MAC5BF,OAAO,GAAGtiB,IAAI,CAAC6K,GAAG,CAAC+X,GAAG,GAAGriB,UAAU,CAAC,GAAGb,IAAI;MAC3C6iB,OAAO,GAAGviB,IAAI,CAAC4J,GAAG,CAACgZ,GAAG,GAAGriB,UAAU,CAAC,GAAGb,IAAI;MAC3Cye,GAAG,CAAC8E,GAAG,CAAC7kB,CAAC,GAAGkkB,OAAO,EAAEhkB,CAAC,GAAGikB,OAAO,EAAEC,YAAY,EAAEI,GAAG,GAAG7iB,EAAE,EAAE6iB,GAAG,GAAGtiB,OAAO,CAAC;MACxE6d,GAAG,CAAC8E,GAAG,CAAC7kB,CAAC,GAAGmkB,OAAO,EAAEjkB,CAAC,GAAGgkB,OAAO,EAAEE,YAAY,EAAEI,GAAG,GAAGtiB,OAAO,EAAEsiB,GAAG,CAAC;MACnEzE,GAAG,CAAC8E,GAAG,CAAC7kB,CAAC,GAAGkkB,OAAO,EAAEhkB,CAAC,GAAGikB,OAAO,EAAEC,YAAY,EAAEI,GAAG,EAAEA,GAAG,GAAGtiB,OAAO,CAAC;MACnE6d,GAAG,CAAC8E,GAAG,CAAC7kB,CAAC,GAAGmkB,OAAO,EAAEjkB,CAAC,GAAGgkB,OAAO,EAAEE,YAAY,EAAEI,GAAG,GAAGtiB,OAAO,EAAEsiB,GAAG,GAAG7iB,EAAE,CAAC;MACxEoe,GAAG,CAAC+E,SAAS,CAAC,CAAC;MACf;IACF,KAAK,MAAM;MACT,IAAI,CAACR,QAAQ,EAAE;QACbhjB,IAAI,GAAGM,IAAI,CAACqjB,OAAO,GAAGV,MAAM;QAC5B5B,KAAK,GAAG/S,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGtO,IAAI;QACxBye,GAAG,CAACmF,IAAI,CAACllB,CAAC,GAAG2iB,KAAK,EAAEziB,CAAC,GAAGoB,IAAI,EAAE,CAAC,GAAGqhB,KAAK,EAAE,CAAC,GAAGrhB,IAAI,CAAC;QAClD;MACF;MACAkjB,GAAG,IAAIriB,UAAU;IACnB,KAAK,SAAS;MACZ+hB,OAAO,GAAGtiB,IAAI,CAAC6K,GAAG,CAAC+X,GAAG,CAAC,GAAGD,MAAM;MAChCJ,OAAO,GAAGviB,IAAI,CAAC4J,GAAG,CAACgZ,GAAG,CAAC,GAAGD,MAAM;MAChCxE,GAAG,CAACgF,MAAM,CAAC/kB,CAAC,GAAGkkB,OAAO,EAAEhkB,CAAC,GAAGikB,OAAO,CAAC;MACpCpE,GAAG,CAACiF,MAAM,CAAChlB,CAAC,GAAGmkB,OAAO,EAAEjkB,CAAC,GAAGgkB,OAAO,CAAC;MACpCnE,GAAG,CAACiF,MAAM,CAAChlB,CAAC,GAAGkkB,OAAO,EAAEhkB,CAAC,GAAGikB,OAAO,CAAC;MACpCpE,GAAG,CAACiF,MAAM,CAAChlB,CAAC,GAAGmkB,OAAO,EAAEjkB,CAAC,GAAGgkB,OAAO,CAAC;MACpCnE,GAAG,CAAC+E,SAAS,CAAC,CAAC;MACf;IACF,KAAK,UAAU;MACbN,GAAG,IAAIriB,UAAU;IACnB,KAAK,OAAO;MACV+hB,OAAO,GAAGtiB,IAAI,CAAC6K,GAAG,CAAC+X,GAAG,CAAC,GAAGD,MAAM;MAChCJ,OAAO,GAAGviB,IAAI,CAAC4J,GAAG,CAACgZ,GAAG,CAAC,GAAGD,MAAM;MAChCxE,GAAG,CAACgF,MAAM,CAAC/kB,CAAC,GAAGkkB,OAAO,EAAEhkB,CAAC,GAAGikB,OAAO,CAAC;MACpCpE,GAAG,CAACiF,MAAM,CAAChlB,CAAC,GAAGkkB,OAAO,EAAEhkB,CAAC,GAAGikB,OAAO,CAAC;MACpCpE,GAAG,CAACgF,MAAM,CAAC/kB,CAAC,GAAGmkB,OAAO,EAAEjkB,CAAC,GAAGgkB,OAAO,CAAC;MACpCnE,GAAG,CAACiF,MAAM,CAAChlB,CAAC,GAAGmkB,OAAO,EAAEjkB,CAAC,GAAGgkB,OAAO,CAAC;MACpC;IACF,KAAK,MAAM;MACTA,OAAO,GAAGtiB,IAAI,CAAC6K,GAAG,CAAC+X,GAAG,CAAC,GAAGD,MAAM;MAChCJ,OAAO,GAAGviB,IAAI,CAAC4J,GAAG,CAACgZ,GAAG,CAAC,GAAGD,MAAM;MAChCxE,GAAG,CAACgF,MAAM,CAAC/kB,CAAC,GAAGkkB,OAAO,EAAEhkB,CAAC,GAAGikB,OAAO,CAAC;MACpCpE,GAAG,CAACiF,MAAM,CAAChlB,CAAC,GAAGkkB,OAAO,EAAEhkB,CAAC,GAAGikB,OAAO,CAAC;MACpCpE,GAAG,CAACgF,MAAM,CAAC/kB,CAAC,GAAGmkB,OAAO,EAAEjkB,CAAC,GAAGgkB,OAAO,CAAC;MACpCnE,GAAG,CAACiF,MAAM,CAAChlB,CAAC,GAAGmkB,OAAO,EAAEjkB,CAAC,GAAGgkB,OAAO,CAAC;MACpCM,GAAG,IAAIriB,UAAU;MACjB+hB,OAAO,GAAGtiB,IAAI,CAAC6K,GAAG,CAAC+X,GAAG,CAAC,GAAGD,MAAM;MAChCJ,OAAO,GAAGviB,IAAI,CAAC4J,GAAG,CAACgZ,GAAG,CAAC,GAAGD,MAAM;MAChCxE,GAAG,CAACgF,MAAM,CAAC/kB,CAAC,GAAGkkB,OAAO,EAAEhkB,CAAC,GAAGikB,OAAO,CAAC;MACpCpE,GAAG,CAACiF,MAAM,CAAChlB,CAAC,GAAGkkB,OAAO,EAAEhkB,CAAC,GAAGikB,OAAO,CAAC;MACpCpE,GAAG,CAACgF,MAAM,CAAC/kB,CAAC,GAAGmkB,OAAO,EAAEjkB,CAAC,GAAGgkB,OAAO,CAAC;MACpCnE,GAAG,CAACiF,MAAM,CAAChlB,CAAC,GAAGmkB,OAAO,EAAEjkB,CAAC,GAAGgkB,OAAO,CAAC;MACpC;IACF,KAAK,MAAM;MACTA,OAAO,GAAGtU,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGhO,IAAI,CAAC6K,GAAG,CAAC+X,GAAG,CAAC,GAAGD,MAAM;MAC5CJ,OAAO,GAAGviB,IAAI,CAAC4J,GAAG,CAACgZ,GAAG,CAAC,GAAGD,MAAM;MAChCxE,GAAG,CAACgF,MAAM,CAAC/kB,CAAC,GAAGkkB,OAAO,EAAEhkB,CAAC,GAAGikB,OAAO,CAAC;MACpCpE,GAAG,CAACiF,MAAM,CAAChlB,CAAC,GAAGkkB,OAAO,EAAEhkB,CAAC,GAAGikB,OAAO,CAAC;MACpC;IACF,KAAK,MAAM;MACTpE,GAAG,CAACgF,MAAM,CAAC/kB,CAAC,EAAEE,CAAC,CAAC;MAChB6f,GAAG,CAACiF,MAAM,CAAChlB,CAAC,GAAG4B,IAAI,CAAC6K,GAAG,CAAC+X,GAAG,CAAC,GAAGD,MAAM,EAAErkB,CAAC,GAAG0B,IAAI,CAAC4J,GAAG,CAACgZ,GAAG,CAAC,GAAGD,MAAM,CAAC;MAClE;EACF;EACAxE,GAAG,CAACoF,IAAI,CAAC,CAAC;EACV,IAAIrmB,OAAO,CAACsmB,WAAW,GAAG,CAAC,EAAE;IAC3BrF,GAAG,CAACsF,MAAM,CAAC,CAAC;EACd;AACF;AACA,SAASC,cAAcA,CAACC,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAE;EAC3CA,MAAM,GAAGA,MAAM,IAAI,GAAG;EACtB,OAAO,CAACD,IAAI,IAAKD,KAAK,IAAIA,KAAK,CAACvlB,CAAC,GAAGwlB,IAAI,CAAChc,IAAI,GAAGic,MAAM,IAAIF,KAAK,CAACvlB,CAAC,GAAGwlB,IAAI,CAAC/b,KAAK,GAAGgc,MAAM,IACvFF,KAAK,CAACrlB,CAAC,GAAGslB,IAAI,CAACE,GAAG,GAAGD,MAAM,IAAIF,KAAK,CAACrlB,CAAC,GAAGslB,IAAI,CAACG,MAAM,GAAGF,MAAO;AAChE;AACA,SAASG,QAAQA,CAAC7F,GAAG,EAAEyF,IAAI,EAAE;EAC3BzF,GAAG,CAACiD,IAAI,CAAC,CAAC;EACVjD,GAAG,CAAC4E,SAAS,CAAC,CAAC;EACf5E,GAAG,CAACmF,IAAI,CAACM,IAAI,CAAChc,IAAI,EAAEgc,IAAI,CAACE,GAAG,EAAEF,IAAI,CAAC/b,KAAK,GAAG+b,IAAI,CAAChc,IAAI,EAAEgc,IAAI,CAACG,MAAM,GAAGH,IAAI,CAACE,GAAG,CAAC;EAC7E3F,GAAG,CAAC8F,IAAI,CAAC,CAAC;AACZ;AACA,SAASC,UAAUA,CAAC/F,GAAG,EAAE;EACvBA,GAAG,CAACqD,OAAO,CAAC,CAAC;AACf;AACA,SAAS2C,cAAcA,CAAChG,GAAG,EAAEtgB,QAAQ,EAAEnB,MAAM,EAAE0nB,IAAI,EAAE5F,IAAI,EAAE;EACzD,IAAI,CAAC3gB,QAAQ,EAAE;IACb,OAAOsgB,GAAG,CAACiF,MAAM,CAAC1mB,MAAM,CAAC0B,CAAC,EAAE1B,MAAM,CAAC4B,CAAC,CAAC;EACvC;EACA,IAAIkgB,IAAI,KAAK,QAAQ,EAAE;IACrB,MAAM6F,QAAQ,GAAG,CAACxmB,QAAQ,CAACO,CAAC,GAAG1B,MAAM,CAAC0B,CAAC,IAAI,GAAG;IAC9C+f,GAAG,CAACiF,MAAM,CAACiB,QAAQ,EAAExmB,QAAQ,CAACS,CAAC,CAAC;IAChC6f,GAAG,CAACiF,MAAM,CAACiB,QAAQ,EAAE3nB,MAAM,CAAC4B,CAAC,CAAC;EAChC,CAAC,MAAM,IAAIkgB,IAAI,KAAK,OAAO,KAAK,CAAC,CAAC4F,IAAI,EAAE;IACtCjG,GAAG,CAACiF,MAAM,CAACvlB,QAAQ,CAACO,CAAC,EAAE1B,MAAM,CAAC4B,CAAC,CAAC;EAClC,CAAC,MAAM;IACL6f,GAAG,CAACiF,MAAM,CAAC1mB,MAAM,CAAC0B,CAAC,EAAEP,QAAQ,CAACS,CAAC,CAAC;EAClC;EACA6f,GAAG,CAACiF,MAAM,CAAC1mB,MAAM,CAAC0B,CAAC,EAAE1B,MAAM,CAAC4B,CAAC,CAAC;AAChC;AACA,SAASgmB,cAAcA,CAACnG,GAAG,EAAEtgB,QAAQ,EAAEnB,MAAM,EAAE0nB,IAAI,EAAE;EACnD,IAAI,CAACvmB,QAAQ,EAAE;IACb,OAAOsgB,GAAG,CAACiF,MAAM,CAAC1mB,MAAM,CAAC0B,CAAC,EAAE1B,MAAM,CAAC4B,CAAC,CAAC;EACvC;EACA6f,GAAG,CAACoG,aAAa,CACfH,IAAI,GAAGvmB,QAAQ,CAAC2mB,IAAI,GAAG3mB,QAAQ,CAAC4mB,IAAI,EACpCL,IAAI,GAAGvmB,QAAQ,CAAC6mB,IAAI,GAAG7mB,QAAQ,CAAC8mB,IAAI,EACpCP,IAAI,GAAG1nB,MAAM,CAAC+nB,IAAI,GAAG/nB,MAAM,CAAC8nB,IAAI,EAChCJ,IAAI,GAAG1nB,MAAM,CAACioB,IAAI,GAAGjoB,MAAM,CAACgoB,IAAI,EAChChoB,MAAM,CAAC0B,CAAC,EACR1B,MAAM,CAAC4B,CAAC,CAAC;AACb;AACA,SAASsmB,UAAUA,CAACzG,GAAG,EAAE0G,IAAI,EAAEzmB,CAAC,EAAEE,CAAC,EAAEuf,IAAI,EAAEiH,IAAI,GAAG,CAAC,CAAC,EAAE;EACpD,MAAMC,KAAK,GAAGhrB,OAAO,CAAC8qB,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC;EAC3C,MAAMpB,MAAM,GAAGqB,IAAI,CAACE,WAAW,GAAG,CAAC,IAAIF,IAAI,CAACG,WAAW,KAAK,EAAE;EAC9D,IAAItpB,CAAC,EAAEupB,IAAI;EACX/G,GAAG,CAACiD,IAAI,CAAC,CAAC;EACVjD,GAAG,CAACN,IAAI,GAAGA,IAAI,CAAC+C,MAAM;EACtBuE,aAAa,CAAChH,GAAG,EAAE2G,IAAI,CAAC;EACxB,KAAKnpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGopB,KAAK,CAACjpB,MAAM,EAAE,EAAEH,CAAC,EAAE;IACjCupB,IAAI,GAAGH,KAAK,CAACppB,CAAC,CAAC;IACf,IAAI8nB,MAAM,EAAE;MACV,IAAIqB,IAAI,CAACG,WAAW,EAAE;QACpB9G,GAAG,CAACiH,WAAW,GAAGN,IAAI,CAACG,WAAW;MACpC;MACA,IAAI,CAACprB,aAAa,CAACirB,IAAI,CAACE,WAAW,CAAC,EAAE;QACpC7G,GAAG,CAACkH,SAAS,GAAGP,IAAI,CAACE,WAAW;MAClC;MACA7G,GAAG,CAACmH,UAAU,CAACJ,IAAI,EAAE9mB,CAAC,EAAEE,CAAC,EAAEwmB,IAAI,CAACS,QAAQ,CAAC;IAC3C;IACApH,GAAG,CAACqH,QAAQ,CAACN,IAAI,EAAE9mB,CAAC,EAAEE,CAAC,EAAEwmB,IAAI,CAACS,QAAQ,CAAC;IACvCE,YAAY,CAACtH,GAAG,EAAE/f,CAAC,EAAEE,CAAC,EAAE4mB,IAAI,EAAEJ,IAAI,CAAC;IACnCxmB,CAAC,IAAIuf,IAAI,CAACG,UAAU;EACtB;EACAG,GAAG,CAACqD,OAAO,CAAC,CAAC;AACf;AACA,SAAS2D,aAAaA,CAAChH,GAAG,EAAE2G,IAAI,EAAE;EAChC,IAAIA,IAAI,CAACY,WAAW,EAAE;IACpBvH,GAAG,CAAC0E,SAAS,CAACiC,IAAI,CAACY,WAAW,CAAC,CAAC,CAAC,EAAEZ,IAAI,CAACY,WAAW,CAAC,CAAC,CAAC,CAAC;EACzD;EACA,IAAI,CAAC7rB,aAAa,CAACirB,IAAI,CAACpC,QAAQ,CAAC,EAAE;IACjCvE,GAAG,CAACtP,MAAM,CAACiW,IAAI,CAACpC,QAAQ,CAAC;EAC3B;EACA,IAAIoC,IAAI,CAACrJ,KAAK,EAAE;IACd0C,GAAG,CAACwH,SAAS,GAAGb,IAAI,CAACrJ,KAAK;EAC5B;EACA,IAAIqJ,IAAI,CAACc,SAAS,EAAE;IAClBzH,GAAG,CAACyH,SAAS,GAAGd,IAAI,CAACc,SAAS;EAChC;EACA,IAAId,IAAI,CAACe,YAAY,EAAE;IACrB1H,GAAG,CAAC0H,YAAY,GAAGf,IAAI,CAACe,YAAY;EACtC;AACF;AACA,SAASJ,YAAYA,CAACtH,GAAG,EAAE/f,CAAC,EAAEE,CAAC,EAAE4mB,IAAI,EAAEJ,IAAI,EAAE;EAC3C,IAAIA,IAAI,CAACgB,aAAa,IAAIhB,IAAI,CAACiB,SAAS,EAAE;IACxC,MAAMC,OAAO,GAAG7H,GAAG,CAAC2C,WAAW,CAACoE,IAAI,CAAC;IACrC,MAAMtd,IAAI,GAAGxJ,CAAC,GAAG4nB,OAAO,CAACC,qBAAqB;IAC9C,MAAMpe,KAAK,GAAGzJ,CAAC,GAAG4nB,OAAO,CAACE,sBAAsB;IAChD,MAAMpC,GAAG,GAAGxlB,CAAC,GAAG0nB,OAAO,CAACG,uBAAuB;IAC/C,MAAMpC,MAAM,GAAGzlB,CAAC,GAAG0nB,OAAO,CAACI,wBAAwB;IACnD,MAAMC,WAAW,GAAGvB,IAAI,CAACgB,aAAa,GAAG,CAAChC,GAAG,GAAGC,MAAM,IAAI,CAAC,GAAGA,MAAM;IACpE5F,GAAG,CAACiH,WAAW,GAAGjH,GAAG,CAACwH,SAAS;IAC/BxH,GAAG,CAAC4E,SAAS,CAAC,CAAC;IACf5E,GAAG,CAACkH,SAAS,GAAGP,IAAI,CAACwB,eAAe,IAAI,CAAC;IACzCnI,GAAG,CAACgF,MAAM,CAACvb,IAAI,EAAEye,WAAW,CAAC;IAC7BlI,GAAG,CAACiF,MAAM,CAACvb,KAAK,EAAEwe,WAAW,CAAC;IAC9BlI,GAAG,CAACsF,MAAM,CAAC,CAAC;EACd;AACF;AACA,SAAS8C,kBAAkBA,CAACpI,GAAG,EAAEmF,IAAI,EAAE;EACrC,MAAM;IAACllB,CAAC;IAAEE,CAAC;IAAE0P,CAAC;IAAE7B,CAAC;IAAEwW;EAAM,CAAC,GAAGW,IAAI;EACjCnF,GAAG,CAAC8E,GAAG,CAAC7kB,CAAC,GAAGukB,MAAM,CAAC6D,OAAO,EAAEloB,CAAC,GAAGqkB,MAAM,CAAC6D,OAAO,EAAE7D,MAAM,CAAC6D,OAAO,EAAE,CAAClmB,OAAO,EAAEP,EAAE,EAAE,IAAI,CAAC;EACnFoe,GAAG,CAACiF,MAAM,CAAChlB,CAAC,EAAEE,CAAC,GAAG6N,CAAC,GAAGwW,MAAM,CAAC8D,UAAU,CAAC;EACxCtI,GAAG,CAAC8E,GAAG,CAAC7kB,CAAC,GAAGukB,MAAM,CAAC8D,UAAU,EAAEnoB,CAAC,GAAG6N,CAAC,GAAGwW,MAAM,CAAC8D,UAAU,EAAE9D,MAAM,CAAC8D,UAAU,EAAE1mB,EAAE,EAAEO,OAAO,EAAE,IAAI,CAAC;EAC/F6d,GAAG,CAACiF,MAAM,CAAChlB,CAAC,GAAG4P,CAAC,GAAG2U,MAAM,CAAC+D,WAAW,EAAEpoB,CAAC,GAAG6N,CAAC,CAAC;EAC7CgS,GAAG,CAAC8E,GAAG,CAAC7kB,CAAC,GAAG4P,CAAC,GAAG2U,MAAM,CAAC+D,WAAW,EAAEpoB,CAAC,GAAG6N,CAAC,GAAGwW,MAAM,CAAC+D,WAAW,EAAE/D,MAAM,CAAC+D,WAAW,EAAEpmB,OAAO,EAAE,CAAC,EAAE,IAAI,CAAC;EACrG6d,GAAG,CAACiF,MAAM,CAAChlB,CAAC,GAAG4P,CAAC,EAAE1P,CAAC,GAAGqkB,MAAM,CAACgE,QAAQ,CAAC;EACtCxI,GAAG,CAAC8E,GAAG,CAAC7kB,CAAC,GAAG4P,CAAC,GAAG2U,MAAM,CAACgE,QAAQ,EAAEroB,CAAC,GAAGqkB,MAAM,CAACgE,QAAQ,EAAEhE,MAAM,CAACgE,QAAQ,EAAE,CAAC,EAAE,CAACrmB,OAAO,EAAE,IAAI,CAAC;EACzF6d,GAAG,CAACiF,MAAM,CAAChlB,CAAC,GAAGukB,MAAM,CAAC6D,OAAO,EAAEloB,CAAC,CAAC;AACnC;AAEA,MAAMsoB,WAAW,GAAG,IAAIC,MAAM,CAAC,sCAAsC,CAAC;AACtE,MAAMC,UAAU,GAAG,IAAID,MAAM,CAAC,uEAAuE,CAAC;AACtG,SAASE,YAAYA,CAACjtB,KAAK,EAAE4F,IAAI,EAAE;EACjC,MAAMsnB,OAAO,GAAG,CAAC,EAAE,GAAGltB,KAAK,EAAEmtB,KAAK,CAACL,WAAW,CAAC;EAC/C,IAAI,CAACI,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;IACvC,OAAOtnB,IAAI,GAAG,GAAG;EACnB;EACA5F,KAAK,GAAG,CAACktB,OAAO,CAAC,CAAC,CAAC;EACnB,QAAQA,OAAO,CAAC,CAAC,CAAC;IAClB,KAAK,IAAI;MACP,OAAOltB,KAAK;IACd,KAAK,GAAG;MACNA,KAAK,IAAI,GAAG;MACZ;EACF;EACA,OAAO4F,IAAI,GAAG5F,KAAK;AACrB;AACA,MAAMotB,YAAY,GAAG/oB,CAAC,IAAI,CAACA,CAAC,IAAI,CAAC;AACjC,SAASgpB,iBAAiBA,CAACrtB,KAAK,EAAEstB,KAAK,EAAE;EACvC,MAAM3Z,GAAG,GAAG,CAAC,CAAC;EACd,MAAM4Z,QAAQ,GAAG9sB,QAAQ,CAAC6sB,KAAK,CAAC;EAChC,MAAMvrB,IAAI,GAAGwrB,QAAQ,GAAGntB,MAAM,CAAC2B,IAAI,CAACurB,KAAK,CAAC,GAAGA,KAAK;EAClD,MAAME,IAAI,GAAG/sB,QAAQ,CAACT,KAAK,CAAC,GACxButB,QAAQ,GACNE,IAAI,IAAI1sB,cAAc,CAACf,KAAK,CAACytB,IAAI,CAAC,EAAEztB,KAAK,CAACstB,KAAK,CAACG,IAAI,CAAC,CAAC,CAAC,GACvDA,IAAI,IAAIztB,KAAK,CAACytB,IAAI,CAAC,GACrB,MAAMztB,KAAK;EACf,KAAK,MAAMytB,IAAI,IAAI1rB,IAAI,EAAE;IACvB4R,GAAG,CAAC8Z,IAAI,CAAC,GAAGL,YAAY,CAACI,IAAI,CAACC,IAAI,CAAC,CAAC;EACtC;EACA,OAAO9Z,GAAG;AACZ;AACA,SAAS+Z,MAAMA,CAAC1tB,KAAK,EAAE;EACrB,OAAOqtB,iBAAiB,CAACrtB,KAAK,EAAE;IAACgqB,GAAG,EAAE,GAAG;IAAEjc,KAAK,EAAE,GAAG;IAAEkc,MAAM,EAAE,GAAG;IAAEnc,IAAI,EAAE;EAAG,CAAC,CAAC;AACjF;AACA,SAAS6f,aAAaA,CAAC3tB,KAAK,EAAE;EAC5B,OAAOqtB,iBAAiB,CAACrtB,KAAK,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;AACvF;AACA,SAAS4tB,SAASA,CAAC5tB,KAAK,EAAE;EACxB,MAAM0E,GAAG,GAAGgpB,MAAM,CAAC1tB,KAAK,CAAC;EACzB0E,GAAG,CAACuiB,KAAK,GAAGviB,GAAG,CAACoJ,IAAI,GAAGpJ,GAAG,CAACqJ,KAAK;EAChCrJ,GAAG,CAAC2jB,MAAM,GAAG3jB,GAAG,CAACslB,GAAG,GAAGtlB,GAAG,CAACulB,MAAM;EACjC,OAAOvlB,GAAG;AACZ;AACA,SAASmpB,MAAMA,CAACzqB,OAAO,EAAE0qB,QAAQ,EAAE;EACjC1qB,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvB0qB,QAAQ,GAAGA,QAAQ,IAAI1H,QAAQ,CAACrC,IAAI;EACpC,IAAIne,IAAI,GAAG7E,cAAc,CAACqC,OAAO,CAACwC,IAAI,EAAEkoB,QAAQ,CAACloB,IAAI,CAAC;EACtD,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC5BA,IAAI,GAAGsa,QAAQ,CAACta,IAAI,EAAE,EAAE,CAAC;EAC3B;EACA,IAAIqe,KAAK,GAAGljB,cAAc,CAACqC,OAAO,CAAC6gB,KAAK,EAAE6J,QAAQ,CAAC7J,KAAK,CAAC;EACzD,IAAIA,KAAK,IAAI,CAAC,CAAC,EAAE,GAAGA,KAAK,EAAEkJ,KAAK,CAACH,UAAU,CAAC,EAAE;IAC5C9oB,OAAO,CAACC,IAAI,CAAC,iCAAiC,GAAG8f,KAAK,GAAG,GAAG,CAAC;IAC7DA,KAAK,GAAG,EAAE;EACZ;EACA,MAAMF,IAAI,GAAG;IACXC,MAAM,EAAEjjB,cAAc,CAACqC,OAAO,CAAC4gB,MAAM,EAAE8J,QAAQ,CAAC9J,MAAM,CAAC;IACvDE,UAAU,EAAE+I,YAAY,CAAClsB,cAAc,CAACqC,OAAO,CAAC8gB,UAAU,EAAE4J,QAAQ,CAAC5J,UAAU,CAAC,EAAEte,IAAI,CAAC;IACvFA,IAAI;IACJqe,KAAK;IACLrC,MAAM,EAAE7gB,cAAc,CAACqC,OAAO,CAACwe,MAAM,EAAEkM,QAAQ,CAAClM,MAAM,CAAC;IACvDkF,MAAM,EAAE;EACV,CAAC;EACD/C,IAAI,CAAC+C,MAAM,GAAGL,YAAY,CAAC1C,IAAI,CAAC;EAChC,OAAOA,IAAI;AACb;AACA,SAASgK,OAAOA,CAACC,MAAM,EAAEvK,OAAO,EAAEjhB,KAAK,EAAEyrB,IAAI,EAAE;EAC7C,IAAIC,SAAS,GAAG,IAAI;EACpB,IAAIrsB,CAAC,EAAEO,IAAI,EAAEpC,KAAK;EAClB,KAAK6B,CAAC,GAAG,CAAC,EAAEO,IAAI,GAAG4rB,MAAM,CAAChsB,MAAM,EAAEH,CAAC,GAAGO,IAAI,EAAE,EAAEP,CAAC,EAAE;IAC/C7B,KAAK,GAAGguB,MAAM,CAACnsB,CAAC,CAAC;IACjB,IAAI7B,KAAK,KAAKiE,SAAS,EAAE;MACvB;IACF;IACA,IAAIwf,OAAO,KAAKxf,SAAS,IAAI,OAAOjE,KAAK,KAAK,UAAU,EAAE;MACxDA,KAAK,GAAGA,KAAK,CAACyjB,OAAO,CAAC;MACtByK,SAAS,GAAG,KAAK;IACnB;IACA,IAAI1rB,KAAK,KAAKyB,SAAS,IAAIhE,OAAO,CAACD,KAAK,CAAC,EAAE;MACzCA,KAAK,GAAGA,KAAK,CAACwC,KAAK,GAAGxC,KAAK,CAACgC,MAAM,CAAC;MACnCksB,SAAS,GAAG,KAAK;IACnB;IACA,IAAIluB,KAAK,KAAKiE,SAAS,EAAE;MACvB,IAAIgqB,IAAI,IAAI,CAACC,SAAS,EAAE;QACtBD,IAAI,CAACC,SAAS,GAAG,KAAK;MACxB;MACA,OAAOluB,KAAK;IACd;EACF;AACF;AACA,SAASmuB,SAASA,CAACC,MAAM,EAAEC,KAAK,EAAEC,WAAW,EAAE;EAC7C,MAAM;IAAChmB,GAAG;IAAEC;EAAG,CAAC,GAAG6lB,MAAM;EACzB,MAAMG,MAAM,GAAGntB,WAAW,CAACitB,KAAK,EAAE,CAAC9lB,GAAG,GAAGD,GAAG,IAAI,CAAC,CAAC;EAClD,MAAMkmB,QAAQ,GAAGA,CAACxuB,KAAK,EAAEuM,GAAG,KAAK+hB,WAAW,IAAItuB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK,GAAGuM,GAAG;EAC7E,OAAO;IACLjE,GAAG,EAAEkmB,QAAQ,CAAClmB,GAAG,EAAE,CAACpC,IAAI,CAAC8B,GAAG,CAACumB,MAAM,CAAC,CAAC;IACrChmB,GAAG,EAAEimB,QAAQ,CAACjmB,GAAG,EAAEgmB,MAAM;EAC3B,CAAC;AACH;AACA,SAASE,aAAaA,CAACC,aAAa,EAAEjL,OAAO,EAAE;EAC7C,OAAOrjB,MAAM,CAACsP,MAAM,CAACtP,MAAM,CAACyC,MAAM,CAAC6rB,aAAa,CAAC,EAAEjL,OAAO,CAAC;AAC7D;AAEA,SAASkL,eAAeA,CAACC,MAAM,EAAEC,QAAQ,GAAG,CAAC,EAAE,CAAC,EAAEC,UAAU,GAAGF,MAAM,EAAEd,QAAQ,EAAEiB,SAAS,GAAGA,CAAA,KAAMH,MAAM,CAAC,CAAC,CAAC,EAAE;EAC5G,IAAI,CAACrpB,OAAO,CAACuoB,QAAQ,CAAC,EAAE;IACtBA,QAAQ,GAAGkB,QAAQ,CAAC,WAAW,EAAEJ,MAAM,CAAC;EAC1C;EACA,MAAMxH,KAAK,GAAG;IACZ,CAAC6H,MAAM,CAACC,WAAW,GAAG,QAAQ;IAC9BC,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAER,MAAM;IACfS,WAAW,EAAEP,UAAU;IACvBtI,SAAS,EAAEsH,QAAQ;IACnBwB,UAAU,EAAEP,SAAS;IACrBtJ,QAAQ,EAAG3hB,KAAK,IAAK6qB,eAAe,CAAC,CAAC7qB,KAAK,EAAE,GAAG8qB,MAAM,CAAC,EAAEC,QAAQ,EAAEC,UAAU,EAAEhB,QAAQ;EACzF,CAAC;EACD,OAAO,IAAIyB,KAAK,CAACnI,KAAK,EAAE;IACtBoI,cAAcA,CAAC5sB,MAAM,EAAE6qB,IAAI,EAAE;MAC3B,OAAO7qB,MAAM,CAAC6qB,IAAI,CAAC;MACnB,OAAO7qB,MAAM,CAAC6sB,KAAK;MACnB,OAAOb,MAAM,CAAC,CAAC,CAAC,CAACnB,IAAI,CAAC;MACtB,OAAO,IAAI;IACb,CAAC;IACDjI,GAAGA,CAAC5iB,MAAM,EAAE6qB,IAAI,EAAE;MAChB,OAAOiC,OAAO,CAAC9sB,MAAM,EAAE6qB,IAAI,EACzB,MAAMkC,oBAAoB,CAAClC,IAAI,EAAEoB,QAAQ,EAAED,MAAM,EAAEhsB,MAAM,CAAC,CAAC;IAC/D,CAAC;IACDgtB,wBAAwBA,CAAChtB,MAAM,EAAE6qB,IAAI,EAAE;MACrC,OAAOoC,OAAO,CAACD,wBAAwB,CAAChtB,MAAM,CAACwsB,OAAO,CAAC,CAAC,CAAC,EAAE3B,IAAI,CAAC;IAClE,CAAC;IACDqC,cAAcA,CAAA,EAAG;MACf,OAAOD,OAAO,CAACC,cAAc,CAAClB,MAAM,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC;IACD9oB,GAAGA,CAAClD,MAAM,EAAE6qB,IAAI,EAAE;MAChB,OAAOsC,oBAAoB,CAACntB,MAAM,CAAC,CAACotB,QAAQ,CAACvC,IAAI,CAAC;IACpD,CAAC;IACDwC,OAAOA,CAACrtB,MAAM,EAAE;MACd,OAAOmtB,oBAAoB,CAACntB,MAAM,CAAC;IACrC,CAAC;IACDyJ,GAAGA,CAACzJ,MAAM,EAAE6qB,IAAI,EAAEztB,KAAK,EAAE;MACvB,MAAMkwB,OAAO,GAAGttB,MAAM,CAACutB,QAAQ,KAAKvtB,MAAM,CAACutB,QAAQ,GAAGpB,SAAS,CAAC,CAAC,CAAC;MAClEnsB,MAAM,CAAC6qB,IAAI,CAAC,GAAGyC,OAAO,CAACzC,IAAI,CAAC,GAAGztB,KAAK;MACpC,OAAO4C,MAAM,CAAC6sB,KAAK;MACnB,OAAO,IAAI;IACb;EACF,CAAC,CAAC;AACJ;AACA,SAASW,cAAcA,CAACC,KAAK,EAAE5M,OAAO,EAAE6M,QAAQ,EAAEC,kBAAkB,EAAE;EACpE,MAAMnJ,KAAK,GAAG;IACZ+H,UAAU,EAAE,KAAK;IACjBqB,MAAM,EAAEH,KAAK;IACbI,QAAQ,EAAEhN,OAAO;IACjBiN,SAAS,EAAEJ,QAAQ;IACnBK,MAAM,EAAE,IAAIrkB,GAAG,CAAC,CAAC;IACjB6W,YAAY,EAAEA,YAAY,CAACkN,KAAK,EAAEE,kBAAkB,CAAC;IACrDK,UAAU,EAAGvM,GAAG,IAAK+L,cAAc,CAACC,KAAK,EAAEhM,GAAG,EAAEiM,QAAQ,EAAEC,kBAAkB,CAAC;IAC7E9K,QAAQ,EAAG3hB,KAAK,IAAKssB,cAAc,CAACC,KAAK,CAAC5K,QAAQ,CAAC3hB,KAAK,CAAC,EAAE2f,OAAO,EAAE6M,QAAQ,EAAEC,kBAAkB;EAClG,CAAC;EACD,OAAO,IAAIhB,KAAK,CAACnI,KAAK,EAAE;IACtBoI,cAAcA,CAAC5sB,MAAM,EAAE6qB,IAAI,EAAE;MAC3B,OAAO7qB,MAAM,CAAC6qB,IAAI,CAAC;MACnB,OAAO4C,KAAK,CAAC5C,IAAI,CAAC;MAClB,OAAO,IAAI;IACb,CAAC;IACDjI,GAAGA,CAAC5iB,MAAM,EAAE6qB,IAAI,EAAEoD,QAAQ,EAAE;MAC1B,OAAOnB,OAAO,CAAC9sB,MAAM,EAAE6qB,IAAI,EACzB,MAAMqD,mBAAmB,CAACluB,MAAM,EAAE6qB,IAAI,EAAEoD,QAAQ,CAAC,CAAC;IACtD,CAAC;IACDjB,wBAAwBA,CAAChtB,MAAM,EAAE6qB,IAAI,EAAE;MACrC,OAAO7qB,MAAM,CAACugB,YAAY,CAAC4N,OAAO,GAC9BlB,OAAO,CAAC/pB,GAAG,CAACuqB,KAAK,EAAE5C,IAAI,CAAC,GAAG;QAAC/hB,UAAU,EAAE,IAAI;QAAED,YAAY,EAAE;MAAI,CAAC,GAAGxH,SAAS,GAC7E4rB,OAAO,CAACD,wBAAwB,CAACS,KAAK,EAAE5C,IAAI,CAAC;IACnD,CAAC;IACDqC,cAAcA,CAAA,EAAG;MACf,OAAOD,OAAO,CAACC,cAAc,CAACO,KAAK,CAAC;IACtC,CAAC;IACDvqB,GAAGA,CAAClD,MAAM,EAAE6qB,IAAI,EAAE;MAChB,OAAOoC,OAAO,CAAC/pB,GAAG,CAACuqB,KAAK,EAAE5C,IAAI,CAAC;IACjC,CAAC;IACDwC,OAAOA,CAAA,EAAG;MACR,OAAOJ,OAAO,CAACI,OAAO,CAACI,KAAK,CAAC;IAC/B,CAAC;IACDhkB,GAAGA,CAACzJ,MAAM,EAAE6qB,IAAI,EAAEztB,KAAK,EAAE;MACvBqwB,KAAK,CAAC5C,IAAI,CAAC,GAAGztB,KAAK;MACnB,OAAO4C,MAAM,CAAC6qB,IAAI,CAAC;MACnB,OAAO,IAAI;IACb;EACF,CAAC,CAAC;AACJ;AACA,SAAStK,YAAYA,CAACkN,KAAK,EAAEjK,QAAQ,GAAG;EAAC4K,UAAU,EAAE,IAAI;EAAEC,SAAS,EAAE;AAAI,CAAC,EAAE;EAC3E,MAAM;IAAC5K,WAAW,GAAGD,QAAQ,CAAC4K,UAAU;IAAEzK,UAAU,GAAGH,QAAQ,CAAC6K,SAAS;IAAEC,QAAQ,GAAG9K,QAAQ,CAAC2K;EAAO,CAAC,GAAGV,KAAK;EAC/G,OAAO;IACLU,OAAO,EAAEG,QAAQ;IACjBF,UAAU,EAAE3K,WAAW;IACvB4K,SAAS,EAAE1K,UAAU;IACrB4K,YAAY,EAAE3rB,UAAU,CAAC6gB,WAAW,CAAC,GAAGA,WAAW,GAAG,MAAMA,WAAW;IACvE+K,WAAW,EAAE5rB,UAAU,CAAC+gB,UAAU,CAAC,GAAGA,UAAU,GAAG,MAAMA;EAC3D,CAAC;AACH;AACA,MAAM8K,OAAO,GAAGA,CAACC,MAAM,EAAE3L,IAAI,KAAK2L,MAAM,GAAGA,MAAM,GAAGnsB,WAAW,CAACwgB,IAAI,CAAC,GAAGA,IAAI;AAC5E,MAAM4L,gBAAgB,GAAGA,CAAC9D,IAAI,EAAEztB,KAAK,KAAKS,QAAQ,CAACT,KAAK,CAAC,IAAIytB,IAAI,KAAK,UAAU,KAC7ErtB,MAAM,CAAC0vB,cAAc,CAAC9vB,KAAK,CAAC,KAAK,IAAI,IAAIA,KAAK,CAACshB,WAAW,KAAKlhB,MAAM,CAAC;AACzE,SAASsvB,OAAOA,CAAC9sB,MAAM,EAAE6qB,IAAI,EAAEM,OAAO,EAAE;EACtC,IAAI3tB,MAAM,CAACC,SAAS,CAACuD,cAAc,CAACrD,IAAI,CAACqC,MAAM,EAAE6qB,IAAI,CAAC,EAAE;IACtD,OAAO7qB,MAAM,CAAC6qB,IAAI,CAAC;EACrB;EACA,MAAMztB,KAAK,GAAG+tB,OAAO,CAAC,CAAC;EACvBnrB,MAAM,CAAC6qB,IAAI,CAAC,GAAGztB,KAAK;EACpB,OAAOA,KAAK;AACd;AACA,SAAS8wB,mBAAmBA,CAACluB,MAAM,EAAE6qB,IAAI,EAAEoD,QAAQ,EAAE;EACnD,MAAM;IAACL,MAAM;IAAEC,QAAQ;IAAEC,SAAS;IAAEvN,YAAY,EAAEL;EAAW,CAAC,GAAGlgB,MAAM;EACvE,IAAI5C,KAAK,GAAGwwB,MAAM,CAAC/C,IAAI,CAAC;EACxB,IAAIjoB,UAAU,CAACxF,KAAK,CAAC,IAAI8iB,WAAW,CAACqO,YAAY,CAAC1D,IAAI,CAAC,EAAE;IACvDztB,KAAK,GAAGwxB,kBAAkB,CAAC/D,IAAI,EAAEztB,KAAK,EAAE4C,MAAM,EAAEiuB,QAAQ,CAAC;EAC3D;EACA,IAAI5wB,OAAO,CAACD,KAAK,CAAC,IAAIA,KAAK,CAACgC,MAAM,EAAE;IAClChC,KAAK,GAAGyxB,aAAa,CAAChE,IAAI,EAAEztB,KAAK,EAAE4C,MAAM,EAAEkgB,WAAW,CAACsO,WAAW,CAAC;EACrE;EACA,IAAIG,gBAAgB,CAAC9D,IAAI,EAAEztB,KAAK,CAAC,EAAE;IACjCA,KAAK,GAAGowB,cAAc,CAACpwB,KAAK,EAAEywB,QAAQ,EAAEC,SAAS,IAAIA,SAAS,CAACjD,IAAI,CAAC,EAAE3K,WAAW,CAAC;EACpF;EACA,OAAO9iB,KAAK;AACd;AACA,SAASwxB,kBAAkBA,CAAC/D,IAAI,EAAEztB,KAAK,EAAE4C,MAAM,EAAEiuB,QAAQ,EAAE;EACzD,MAAM;IAACL,MAAM;IAAEC,QAAQ;IAAEC,SAAS;IAAEC;EAAM,CAAC,GAAG/tB,MAAM;EACpD,IAAI+tB,MAAM,CAAC7qB,GAAG,CAAC2nB,IAAI,CAAC,EAAE;IACpB,MAAM,IAAIiE,KAAK,CAAC,sBAAsB,GAAGxxB,KAAK,CAACsM,IAAI,CAACmkB,MAAM,CAAC,CAACgB,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,GAAGlE,IAAI,CAAC;EACvF;EACAkD,MAAM,CAACpkB,GAAG,CAACkhB,IAAI,CAAC;EAChBztB,KAAK,GAAGA,KAAK,CAACywB,QAAQ,EAAEC,SAAS,IAAIG,QAAQ,CAAC;EAC9CF,MAAM,CAACiB,MAAM,CAACnE,IAAI,CAAC;EACnB,IAAI8D,gBAAgB,CAAC9D,IAAI,EAAEztB,KAAK,CAAC,EAAE;IACjCA,KAAK,GAAG6xB,iBAAiB,CAACrB,MAAM,CAACpB,OAAO,EAAEoB,MAAM,EAAE/C,IAAI,EAAEztB,KAAK,CAAC;EAChE;EACA,OAAOA,KAAK;AACd;AACA,SAASyxB,aAAaA,CAAChE,IAAI,EAAEztB,KAAK,EAAE4C,MAAM,EAAEwuB,WAAW,EAAE;EACvD,MAAM;IAACZ,MAAM;IAAEC,QAAQ;IAAEC,SAAS;IAAEvN,YAAY,EAAEL;EAAW,CAAC,GAAGlgB,MAAM;EACvE,IAAI2C,OAAO,CAACkrB,QAAQ,CAACjuB,KAAK,CAAC,IAAI4uB,WAAW,CAAC3D,IAAI,CAAC,EAAE;IAChDztB,KAAK,GAAGA,KAAK,CAACywB,QAAQ,CAACjuB,KAAK,GAAGxC,KAAK,CAACgC,MAAM,CAAC;EAC9C,CAAC,MAAM,IAAIvB,QAAQ,CAACT,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;IAC7B,MAAM8xB,GAAG,GAAG9xB,KAAK;IACjB,MAAM4uB,MAAM,GAAG4B,MAAM,CAACpB,OAAO,CAAC2C,MAAM,CAAC/nB,CAAC,IAAIA,CAAC,KAAK8nB,GAAG,CAAC;IACpD9xB,KAAK,GAAG,EAAE;IACV,KAAK,MAAM6F,IAAI,IAAIisB,GAAG,EAAE;MACtB,MAAMntB,QAAQ,GAAGktB,iBAAiB,CAACjD,MAAM,EAAE4B,MAAM,EAAE/C,IAAI,EAAE5nB,IAAI,CAAC;MAC9D7F,KAAK,CAACkF,IAAI,CAACkrB,cAAc,CAACzrB,QAAQ,EAAE8rB,QAAQ,EAAEC,SAAS,IAAIA,SAAS,CAACjD,IAAI,CAAC,EAAE3K,WAAW,CAAC,CAAC;IAC3F;EACF;EACA,OAAO9iB,KAAK;AACd;AACA,SAASgyB,eAAeA,CAAClE,QAAQ,EAAEL,IAAI,EAAEztB,KAAK,EAAE;EAC9C,OAAOwF,UAAU,CAACsoB,QAAQ,CAAC,GAAGA,QAAQ,CAACL,IAAI,EAAEztB,KAAK,CAAC,GAAG8tB,QAAQ;AAChE;AACA,MAAMmE,QAAQ,GAAGA,CAAChvB,GAAG,EAAEivB,MAAM,KAAKjvB,GAAG,KAAK,IAAI,GAAGivB,MAAM,GACnD,OAAOjvB,GAAG,KAAK,QAAQ,GAAGwB,gBAAgB,CAACytB,MAAM,EAAEjvB,GAAG,CAAC,GAAGgB,SAAS;AACvE,SAASkuB,SAASA,CAAC9lB,GAAG,EAAE+lB,YAAY,EAAEnvB,GAAG,EAAEovB,cAAc,EAAEryB,KAAK,EAAE;EAChE,KAAK,MAAMkyB,MAAM,IAAIE,YAAY,EAAE;IACjC,MAAMtuB,KAAK,GAAGmuB,QAAQ,CAAChvB,GAAG,EAAEivB,MAAM,CAAC;IACnC,IAAIpuB,KAAK,EAAE;MACTuI,GAAG,CAACE,GAAG,CAACzI,KAAK,CAAC;MACd,MAAMgqB,QAAQ,GAAGkE,eAAe,CAACluB,KAAK,CAAC0iB,SAAS,EAAEvjB,GAAG,EAAEjD,KAAK,CAAC;MAC7D,IAAIuF,OAAO,CAACuoB,QAAQ,CAAC,IAAIA,QAAQ,KAAK7qB,GAAG,IAAI6qB,QAAQ,KAAKuE,cAAc,EAAE;QACxE,OAAOvE,QAAQ;MACjB;IACF,CAAC,MAAM,IAAIhqB,KAAK,KAAK,KAAK,IAAIyB,OAAO,CAAC8sB,cAAc,CAAC,IAAIpvB,GAAG,KAAKovB,cAAc,EAAE;MAC/E,OAAO,IAAI;IACb;EACF;EACA,OAAO,KAAK;AACd;AACA,SAASR,iBAAiBA,CAACO,YAAY,EAAEztB,QAAQ,EAAE8oB,IAAI,EAAEztB,KAAK,EAAE;EAC9D,MAAM8uB,UAAU,GAAGnqB,QAAQ,CAAC0qB,WAAW;EACvC,MAAMvB,QAAQ,GAAGkE,eAAe,CAACrtB,QAAQ,CAAC6hB,SAAS,EAAEiH,IAAI,EAAEztB,KAAK,CAAC;EACjE,MAAMsyB,SAAS,GAAG,CAAC,GAAGF,YAAY,EAAE,GAAGtD,UAAU,CAAC;EAClD,MAAMziB,GAAG,GAAG,IAAIC,GAAG,CAAC,CAAC;EACrBD,GAAG,CAACE,GAAG,CAACvM,KAAK,CAAC;EACd,IAAIiD,GAAG,GAAGsvB,gBAAgB,CAAClmB,GAAG,EAAEimB,SAAS,EAAE7E,IAAI,EAAEK,QAAQ,IAAIL,IAAI,EAAEztB,KAAK,CAAC;EACzE,IAAIiD,GAAG,KAAK,IAAI,EAAE;IAChB,OAAO,KAAK;EACd;EACA,IAAIsC,OAAO,CAACuoB,QAAQ,CAAC,IAAIA,QAAQ,KAAKL,IAAI,EAAE;IAC1CxqB,GAAG,GAAGsvB,gBAAgB,CAAClmB,GAAG,EAAEimB,SAAS,EAAExE,QAAQ,EAAE7qB,GAAG,EAAEjD,KAAK,CAAC;IAC5D,IAAIiD,GAAG,KAAK,IAAI,EAAE;MAChB,OAAO,KAAK;IACd;EACF;EACA,OAAO0rB,eAAe,CAACzuB,KAAK,CAACsM,IAAI,CAACH,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEyiB,UAAU,EAAEhB,QAAQ,EAChE,MAAM0E,YAAY,CAAC7tB,QAAQ,EAAE8oB,IAAI,EAAEztB,KAAK,CAAC,CAAC;AAC9C;AACA,SAASuyB,gBAAgBA,CAAClmB,GAAG,EAAEimB,SAAS,EAAErvB,GAAG,EAAE6qB,QAAQ,EAAEjoB,IAAI,EAAE;EAC7D,OAAO5C,GAAG,EAAE;IACVA,GAAG,GAAGkvB,SAAS,CAAC9lB,GAAG,EAAEimB,SAAS,EAAErvB,GAAG,EAAE6qB,QAAQ,EAAEjoB,IAAI,CAAC;EACtD;EACA,OAAO5C,GAAG;AACZ;AACA,SAASuvB,YAAYA,CAAC7tB,QAAQ,EAAE8oB,IAAI,EAAEztB,KAAK,EAAE;EAC3C,MAAMkyB,MAAM,GAAGvtB,QAAQ,CAAC2qB,UAAU,CAAC,CAAC;EACpC,IAAI,EAAE7B,IAAI,IAAIyE,MAAM,CAAC,EAAE;IACrBA,MAAM,CAACzE,IAAI,CAAC,GAAG,CAAC,CAAC;EACnB;EACA,MAAM7qB,MAAM,GAAGsvB,MAAM,CAACzE,IAAI,CAAC;EAC3B,IAAIxtB,OAAO,CAAC2C,MAAM,CAAC,IAAInC,QAAQ,CAACT,KAAK,CAAC,EAAE;IACtC,OAAOA,KAAK;EACd;EACA,OAAO4C,MAAM;AACf;AACA,SAAS+sB,oBAAoBA,CAAClC,IAAI,EAAEoB,QAAQ,EAAED,MAAM,EAAEyB,KAAK,EAAE;EAC3D,IAAIrwB,KAAK;EACT,KAAK,MAAMsxB,MAAM,IAAIzC,QAAQ,EAAE;IAC7B7uB,KAAK,GAAGgvB,QAAQ,CAACqC,OAAO,CAACC,MAAM,EAAE7D,IAAI,CAAC,EAAEmB,MAAM,CAAC;IAC/C,IAAIrpB,OAAO,CAACvF,KAAK,CAAC,EAAE;MAClB,OAAOuxB,gBAAgB,CAAC9D,IAAI,EAAEztB,KAAK,CAAC,GAChC6xB,iBAAiB,CAACjD,MAAM,EAAEyB,KAAK,EAAE5C,IAAI,EAAEztB,KAAK,CAAC,GAC7CA,KAAK;IACX;EACF;AACF;AACA,SAASgvB,QAAQA,CAAC/rB,GAAG,EAAE2rB,MAAM,EAAE;EAC7B,KAAK,MAAM9qB,KAAK,IAAI8qB,MAAM,EAAE;IAC1B,IAAI,CAAC9qB,KAAK,EAAE;MACV;IACF;IACA,MAAM9D,KAAK,GAAG8D,KAAK,CAACb,GAAG,CAAC;IACxB,IAAIsC,OAAO,CAACvF,KAAK,CAAC,EAAE;MAClB,OAAOA,KAAK;IACd;EACF;AACF;AACA,SAAS+vB,oBAAoBA,CAACntB,MAAM,EAAE;EACpC,IAAIb,IAAI,GAAGa,MAAM,CAAC6sB,KAAK;EACvB,IAAI,CAAC1tB,IAAI,EAAE;IACTA,IAAI,GAAGa,MAAM,CAAC6sB,KAAK,GAAGgD,wBAAwB,CAAC7vB,MAAM,CAACwsB,OAAO,CAAC;EAChE;EACA,OAAOrtB,IAAI;AACb;AACA,SAAS0wB,wBAAwBA,CAAC7D,MAAM,EAAE;EACxC,MAAMviB,GAAG,GAAG,IAAIC,GAAG,CAAC,CAAC;EACrB,KAAK,MAAMxI,KAAK,IAAI8qB,MAAM,EAAE;IAC1B,KAAK,MAAM3rB,GAAG,IAAI7C,MAAM,CAAC2B,IAAI,CAAC+B,KAAK,CAAC,CAACiuB,MAAM,CAAChvB,CAAC,IAAI,CAACA,CAAC,CAACujB,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE;MACpEja,GAAG,CAACE,GAAG,CAACtJ,GAAG,CAAC;IACd;EACF;EACA,OAAO/C,KAAK,CAACsM,IAAI,CAACH,GAAG,CAAC;AACxB;AACA,SAASqmB,2BAA2BA,CAACvkB,IAAI,EAAEwY,IAAI,EAAE9c,KAAK,EAAE0E,KAAK,EAAE;EAC7D,MAAM;IAACE;EAAM,CAAC,GAAGN,IAAI;EACrB,MAAM;IAAClL,GAAG,GAAG;EAAG,CAAC,GAAG,IAAI,CAAC0vB,QAAQ;EACjC,MAAMC,MAAM,GAAG,IAAI1yB,KAAK,CAACqO,KAAK,CAAC;EAC/B,IAAI1M,CAAC,EAAEO,IAAI,EAAEI,KAAK,EAAEqD,IAAI;EACxB,KAAKhE,CAAC,GAAG,CAAC,EAAEO,IAAI,GAAGmM,KAAK,EAAE1M,CAAC,GAAGO,IAAI,EAAE,EAAEP,CAAC,EAAE;IACvCW,KAAK,GAAGX,CAAC,GAAGgI,KAAK;IACjBhE,IAAI,GAAG8gB,IAAI,CAACnkB,KAAK,CAAC;IAClBowB,MAAM,CAAC/wB,CAAC,CAAC,GAAG;MACV2R,CAAC,EAAE/E,MAAM,CAACokB,KAAK,CAACpuB,gBAAgB,CAACoB,IAAI,EAAE5C,GAAG,CAAC,EAAET,KAAK;IACpD,CAAC;EACH;EACA,OAAOowB,MAAM;AACf;AAEA,MAAME,OAAO,GAAGnyB,MAAM,CAACmyB,OAAO,IAAI,KAAK;AACvC,MAAMC,QAAQ,GAAGA,CAAC3kB,MAAM,EAAEvM,CAAC,KAAKA,CAAC,GAAGuM,MAAM,CAACpM,MAAM,IAAI,CAACoM,MAAM,CAACvM,CAAC,CAAC,CAACmxB,IAAI,IAAI5kB,MAAM,CAACvM,CAAC,CAAC;AACjF,MAAMoxB,YAAY,GAAIzO,SAAS,IAAKA,SAAS,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG;AACjE,SAAS0O,WAAWA,CAACC,UAAU,EAAEC,WAAW,EAAEC,UAAU,EAAEzjB,CAAC,EAAE;EAC3D,MAAM7L,QAAQ,GAAGovB,UAAU,CAACH,IAAI,GAAGI,WAAW,GAAGD,UAAU;EAC3D,MAAMnvB,OAAO,GAAGovB,WAAW;EAC3B,MAAME,IAAI,GAAGD,UAAU,CAACL,IAAI,GAAGI,WAAW,GAAGC,UAAU;EACvD,MAAME,GAAG,GAAGhqB,qBAAqB,CAACvF,OAAO,EAAED,QAAQ,CAAC;EACpD,MAAMyvB,GAAG,GAAGjqB,qBAAqB,CAAC+pB,IAAI,EAAEtvB,OAAO,CAAC;EAChD,IAAIyvB,GAAG,GAAGF,GAAG,IAAIA,GAAG,GAAGC,GAAG,CAAC;EAC3B,IAAIE,GAAG,GAAGF,GAAG,IAAID,GAAG,GAAGC,GAAG,CAAC;EAC3BC,GAAG,GAAG3rB,KAAK,CAAC2rB,GAAG,CAAC,GAAG,CAAC,GAAGA,GAAG;EAC1BC,GAAG,GAAG5rB,KAAK,CAAC4rB,GAAG,CAAC,GAAG,CAAC,GAAGA,GAAG;EAC1B,MAAMC,EAAE,GAAG/jB,CAAC,GAAG6jB,GAAG;EAClB,MAAMG,EAAE,GAAGhkB,CAAC,GAAG8jB,GAAG;EAClB,OAAO;IACL3vB,QAAQ,EAAE;MACRO,CAAC,EAAEN,OAAO,CAACM,CAAC,GAAGqvB,EAAE,IAAIL,IAAI,CAAChvB,CAAC,GAAGP,QAAQ,CAACO,CAAC,CAAC;MACzCE,CAAC,EAAER,OAAO,CAACQ,CAAC,GAAGmvB,EAAE,IAAIL,IAAI,CAAC9uB,CAAC,GAAGT,QAAQ,CAACS,CAAC;IAC1C,CAAC;IACD8uB,IAAI,EAAE;MACJhvB,CAAC,EAAEN,OAAO,CAACM,CAAC,GAAGsvB,EAAE,IAAIN,IAAI,CAAChvB,CAAC,GAAGP,QAAQ,CAACO,CAAC,CAAC;MACzCE,CAAC,EAAER,OAAO,CAACQ,CAAC,GAAGovB,EAAE,IAAIN,IAAI,CAAC9uB,CAAC,GAAGT,QAAQ,CAACS,CAAC;IAC1C;EACF,CAAC;AACH;AACA,SAASqvB,cAAcA,CAACzlB,MAAM,EAAE0lB,MAAM,EAAEC,EAAE,EAAE;EAC1C,MAAMC,SAAS,GAAG5lB,MAAM,CAACpM,MAAM;EAC/B,IAAIiyB,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,gBAAgB,EAAEC,YAAY;EACvD,IAAIC,UAAU,GAAGvB,QAAQ,CAAC3kB,MAAM,EAAE,CAAC,CAAC;EACpC,KAAK,IAAIvM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmyB,SAAS,GAAG,CAAC,EAAE,EAAEnyB,CAAC,EAAE;IACtCwyB,YAAY,GAAGC,UAAU;IACzBA,UAAU,GAAGvB,QAAQ,CAAC3kB,MAAM,EAAEvM,CAAC,GAAG,CAAC,CAAC;IACpC,IAAI,CAACwyB,YAAY,IAAI,CAACC,UAAU,EAAE;MAChC;IACF;IACA,IAAIrtB,YAAY,CAAC6sB,MAAM,CAACjyB,CAAC,CAAC,EAAE,CAAC,EAAEixB,OAAO,CAAC,EAAE;MACvCiB,EAAE,CAAClyB,CAAC,CAAC,GAAGkyB,EAAE,CAAClyB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MACrB;IACF;IACAoyB,MAAM,GAAGF,EAAE,CAAClyB,CAAC,CAAC,GAAGiyB,MAAM,CAACjyB,CAAC,CAAC;IAC1BqyB,KAAK,GAAGH,EAAE,CAAClyB,CAAC,GAAG,CAAC,CAAC,GAAGiyB,MAAM,CAACjyB,CAAC,CAAC;IAC7BuyB,gBAAgB,GAAGluB,IAAI,CAACiB,GAAG,CAAC8sB,MAAM,EAAE,CAAC,CAAC,GAAG/tB,IAAI,CAACiB,GAAG,CAAC+sB,KAAK,EAAE,CAAC,CAAC;IAC3D,IAAIE,gBAAgB,IAAI,CAAC,EAAE;MACzB;IACF;IACAD,IAAI,GAAG,CAAC,GAAGjuB,IAAI,CAACuB,IAAI,CAAC2sB,gBAAgB,CAAC;IACtCL,EAAE,CAAClyB,CAAC,CAAC,GAAGoyB,MAAM,GAAGE,IAAI,GAAGL,MAAM,CAACjyB,CAAC,CAAC;IACjCkyB,EAAE,CAAClyB,CAAC,GAAG,CAAC,CAAC,GAAGqyB,KAAK,GAAGC,IAAI,GAAGL,MAAM,CAACjyB,CAAC,CAAC;EACtC;AACF;AACA,SAAS0yB,eAAeA,CAACnmB,MAAM,EAAE2lB,EAAE,EAAEvP,SAAS,GAAG,GAAG,EAAE;EACpD,MAAMgQ,SAAS,GAAGvB,YAAY,CAACzO,SAAS,CAAC;EACzC,MAAMwP,SAAS,GAAG5lB,MAAM,CAACpM,MAAM;EAC/B,IAAIyyB,KAAK,EAAEC,WAAW,EAAEL,YAAY;EACpC,IAAIC,UAAU,GAAGvB,QAAQ,CAAC3kB,MAAM,EAAE,CAAC,CAAC;EACpC,KAAK,IAAIvM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmyB,SAAS,EAAE,EAAEnyB,CAAC,EAAE;IAClC6yB,WAAW,GAAGL,YAAY;IAC1BA,YAAY,GAAGC,UAAU;IACzBA,UAAU,GAAGvB,QAAQ,CAAC3kB,MAAM,EAAEvM,CAAC,GAAG,CAAC,CAAC;IACpC,IAAI,CAACwyB,YAAY,EAAE;MACjB;IACF;IACA,MAAMM,MAAM,GAAGN,YAAY,CAAC7P,SAAS,CAAC;IACtC,MAAMoQ,MAAM,GAAGP,YAAY,CAACG,SAAS,CAAC;IACtC,IAAIE,WAAW,EAAE;MACfD,KAAK,GAAG,CAACE,MAAM,GAAGD,WAAW,CAAClQ,SAAS,CAAC,IAAI,CAAC;MAC7C6P,YAAY,CAAC,MAAM7P,SAAS,EAAE,CAAC,GAAGmQ,MAAM,GAAGF,KAAK;MAChDJ,YAAY,CAAC,MAAMG,SAAS,EAAE,CAAC,GAAGI,MAAM,GAAGH,KAAK,GAAGV,EAAE,CAAClyB,CAAC,CAAC;IAC1D;IACA,IAAIyyB,UAAU,EAAE;MACdG,KAAK,GAAG,CAACH,UAAU,CAAC9P,SAAS,CAAC,GAAGmQ,MAAM,IAAI,CAAC;MAC5CN,YAAY,CAAC,MAAM7P,SAAS,EAAE,CAAC,GAAGmQ,MAAM,GAAGF,KAAK;MAChDJ,YAAY,CAAC,MAAMG,SAAS,EAAE,CAAC,GAAGI,MAAM,GAAGH,KAAK,GAAGV,EAAE,CAAClyB,CAAC,CAAC;IAC1D;EACF;AACF;AACA,SAASgzB,mBAAmBA,CAACzmB,MAAM,EAAEoW,SAAS,GAAG,GAAG,EAAE;EACpD,MAAMgQ,SAAS,GAAGvB,YAAY,CAACzO,SAAS,CAAC;EACzC,MAAMwP,SAAS,GAAG5lB,MAAM,CAACpM,MAAM;EAC/B,MAAM8xB,MAAM,GAAG5zB,KAAK,CAAC8zB,SAAS,CAAC,CAACvK,IAAI,CAAC,CAAC,CAAC;EACvC,MAAMsK,EAAE,GAAG7zB,KAAK,CAAC8zB,SAAS,CAAC;EAC3B,IAAInyB,CAAC,EAAE6yB,WAAW,EAAEL,YAAY;EAChC,IAAIC,UAAU,GAAGvB,QAAQ,CAAC3kB,MAAM,EAAE,CAAC,CAAC;EACpC,KAAKvM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmyB,SAAS,EAAE,EAAEnyB,CAAC,EAAE;IAC9B6yB,WAAW,GAAGL,YAAY;IAC1BA,YAAY,GAAGC,UAAU;IACzBA,UAAU,GAAGvB,QAAQ,CAAC3kB,MAAM,EAAEvM,CAAC,GAAG,CAAC,CAAC;IACpC,IAAI,CAACwyB,YAAY,EAAE;MACjB;IACF;IACA,IAAIC,UAAU,EAAE;MACd,MAAMQ,UAAU,GAAGR,UAAU,CAAC9P,SAAS,CAAC,GAAG6P,YAAY,CAAC7P,SAAS,CAAC;MAClEsP,MAAM,CAACjyB,CAAC,CAAC,GAAGizB,UAAU,KAAK,CAAC,GAAG,CAACR,UAAU,CAACE,SAAS,CAAC,GAAGH,YAAY,CAACG,SAAS,CAAC,IAAIM,UAAU,GAAG,CAAC;IACnG;IACAf,EAAE,CAAClyB,CAAC,CAAC,GAAG,CAAC6yB,WAAW,GAAGZ,MAAM,CAACjyB,CAAC,CAAC,GAC5B,CAACyyB,UAAU,GAAGR,MAAM,CAACjyB,CAAC,GAAG,CAAC,CAAC,GAC1B+E,IAAI,CAACktB,MAAM,CAACjyB,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK+E,IAAI,CAACktB,MAAM,CAACjyB,CAAC,CAAC,CAAC,GAAI,CAAC,GAC7C,CAACiyB,MAAM,CAACjyB,CAAC,GAAG,CAAC,CAAC,GAAGiyB,MAAM,CAACjyB,CAAC,CAAC,IAAI,CAAC;EACrC;EACAgyB,cAAc,CAACzlB,MAAM,EAAE0lB,MAAM,EAAEC,EAAE,CAAC;EAClCQ,eAAe,CAACnmB,MAAM,EAAE2lB,EAAE,EAAEvP,SAAS,CAAC;AACxC;AACA,SAASuQ,eAAeA,CAACC,EAAE,EAAE1sB,GAAG,EAAEC,GAAG,EAAE;EACrC,OAAOrC,IAAI,CAACqC,GAAG,CAACrC,IAAI,CAACoC,GAAG,CAAC0sB,EAAE,EAAEzsB,GAAG,CAAC,EAAED,GAAG,CAAC;AACzC;AACA,SAAS2sB,eAAeA,CAAC7mB,MAAM,EAAE0b,IAAI,EAAE;EACrC,IAAIjoB,CAAC,EAAEO,IAAI,EAAEynB,KAAK,EAAEqL,MAAM,EAAEC,UAAU;EACtC,IAAIC,UAAU,GAAGxL,cAAc,CAACxb,MAAM,CAAC,CAAC,CAAC,EAAE0b,IAAI,CAAC;EAChD,KAAKjoB,CAAC,GAAG,CAAC,EAAEO,IAAI,GAAGgM,MAAM,CAACpM,MAAM,EAAEH,CAAC,GAAGO,IAAI,EAAE,EAAEP,CAAC,EAAE;IAC/CszB,UAAU,GAAGD,MAAM;IACnBA,MAAM,GAAGE,UAAU;IACnBA,UAAU,GAAGvzB,CAAC,GAAGO,IAAI,GAAG,CAAC,IAAIwnB,cAAc,CAACxb,MAAM,CAACvM,CAAC,GAAG,CAAC,CAAC,EAAEioB,IAAI,CAAC;IAChE,IAAI,CAACoL,MAAM,EAAE;MACX;IACF;IACArL,KAAK,GAAGzb,MAAM,CAACvM,CAAC,CAAC;IACjB,IAAIszB,UAAU,EAAE;MACdtL,KAAK,CAACa,IAAI,GAAGqK,eAAe,CAAClL,KAAK,CAACa,IAAI,EAAEZ,IAAI,CAAChc,IAAI,EAAEgc,IAAI,CAAC/b,KAAK,CAAC;MAC/D8b,KAAK,CAACe,IAAI,GAAGmK,eAAe,CAAClL,KAAK,CAACe,IAAI,EAAEd,IAAI,CAACE,GAAG,EAAEF,IAAI,CAACG,MAAM,CAAC;IACjE;IACA,IAAImL,UAAU,EAAE;MACdvL,KAAK,CAACc,IAAI,GAAGoK,eAAe,CAAClL,KAAK,CAACc,IAAI,EAAEb,IAAI,CAAChc,IAAI,EAAEgc,IAAI,CAAC/b,KAAK,CAAC;MAC/D8b,KAAK,CAACgB,IAAI,GAAGkK,eAAe,CAAClL,KAAK,CAACgB,IAAI,EAAEf,IAAI,CAACE,GAAG,EAAEF,IAAI,CAACG,MAAM,CAAC;IACjE;EACF;AACF;AACA,SAASoL,0BAA0BA,CAACjnB,MAAM,EAAEhL,OAAO,EAAE0mB,IAAI,EAAEwL,IAAI,EAAE9Q,SAAS,EAAE;EAC1E,IAAI3iB,CAAC,EAAEO,IAAI,EAAEynB,KAAK,EAAE0L,aAAa;EACjC,IAAInyB,OAAO,CAACoyB,QAAQ,EAAE;IACpBpnB,MAAM,GAAGA,MAAM,CAAC2jB,MAAM,CAAEiD,EAAE,IAAK,CAACA,EAAE,CAAChC,IAAI,CAAC;EAC1C;EACA,IAAI5vB,OAAO,CAACqyB,sBAAsB,KAAK,UAAU,EAAE;IACjDZ,mBAAmB,CAACzmB,MAAM,EAAEoW,SAAS,CAAC;EACxC,CAAC,MAAM;IACL,IAAIkR,IAAI,GAAGJ,IAAI,GAAGlnB,MAAM,CAACA,MAAM,CAACpM,MAAM,GAAG,CAAC,CAAC,GAAGoM,MAAM,CAAC,CAAC,CAAC;IACvD,KAAKvM,CAAC,GAAG,CAAC,EAAEO,IAAI,GAAGgM,MAAM,CAACpM,MAAM,EAAEH,CAAC,GAAGO,IAAI,EAAE,EAAEP,CAAC,EAAE;MAC/CgoB,KAAK,GAAGzb,MAAM,CAACvM,CAAC,CAAC;MACjB0zB,aAAa,GAAGrC,WAAW,CACzBwC,IAAI,EACJ7L,KAAK,EACLzb,MAAM,CAAClI,IAAI,CAACoC,GAAG,CAACzG,CAAC,GAAG,CAAC,EAAEO,IAAI,IAAIkzB,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAGlzB,IAAI,CAAC,EACrDgB,OAAO,CAACuyB,OACV,CAAC;MACD9L,KAAK,CAACa,IAAI,GAAG6K,aAAa,CAACxxB,QAAQ,CAACO,CAAC;MACrCulB,KAAK,CAACe,IAAI,GAAG2K,aAAa,CAACxxB,QAAQ,CAACS,CAAC;MACrCqlB,KAAK,CAACc,IAAI,GAAG4K,aAAa,CAACjC,IAAI,CAAChvB,CAAC;MACjCulB,KAAK,CAACgB,IAAI,GAAG0K,aAAa,CAACjC,IAAI,CAAC9uB,CAAC;MACjCkxB,IAAI,GAAG7L,KAAK;IACd;EACF;EACA,IAAIzmB,OAAO,CAAC6xB,eAAe,EAAE;IAC3BA,eAAe,CAAC7mB,MAAM,EAAE0b,IAAI,CAAC;EAC/B;AACF;AAEA,SAAS8L,eAAeA,CAAA,EAAG;EACzB,OAAO,OAAO9oB,MAAM,KAAK,WAAW,IAAI,OAAO+oB,QAAQ,KAAK,WAAW;AACzE;AACA,SAASC,cAAcA,CAACC,OAAO,EAAE;EAC/B,IAAI7D,MAAM,GAAG6D,OAAO,CAACC,UAAU;EAC/B,IAAI9D,MAAM,IAAIA,MAAM,CAAC5xB,QAAQ,CAAC,CAAC,KAAK,qBAAqB,EAAE;IACzD4xB,MAAM,GAAGA,MAAM,CAAC+D,IAAI;EACtB;EACA,OAAO/D,MAAM;AACf;AACA,SAASgE,aAAaA,CAACC,UAAU,EAAEnT,IAAI,EAAEoT,cAAc,EAAE;EACvD,IAAIC,aAAa;EACjB,IAAI,OAAOF,UAAU,KAAK,QAAQ,EAAE;IAClCE,aAAa,GAAGnW,QAAQ,CAACiW,UAAU,EAAE,EAAE,CAAC;IACxC,IAAIA,UAAU,CAACjzB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAClCmzB,aAAa,GAAGA,aAAa,GAAG,GAAG,GAAGrT,IAAI,CAACgT,UAAU,CAACI,cAAc,CAAC;IACvE;EACF,CAAC,MAAM;IACLC,aAAa,GAAGF,UAAU;EAC5B;EACA,OAAOE,aAAa;AACtB;AACA,MAAMC,gBAAgB,GAAIC,OAAO,IAAKzpB,MAAM,CAACwpB,gBAAgB,CAACC,OAAO,EAAE,IAAI,CAAC;AAC5E,SAASC,QAAQA,CAACC,EAAE,EAAEpuB,QAAQ,EAAE;EAC9B,OAAOiuB,gBAAgB,CAACG,EAAE,CAAC,CAACC,gBAAgB,CAACruB,QAAQ,CAAC;AACxD;AACA,MAAMsuB,SAAS,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC;AACpD,SAASC,kBAAkBA,CAACC,MAAM,EAAE5S,KAAK,EAAE6S,MAAM,EAAE;EACjD,MAAMtvB,MAAM,GAAG,CAAC,CAAC;EACjBsvB,MAAM,GAAGA,MAAM,GAAG,GAAG,GAAGA,MAAM,GAAG,EAAE;EACnC,KAAK,IAAIj1B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC1B,MAAMk1B,GAAG,GAAGJ,SAAS,CAAC90B,CAAC,CAAC;IACxB2F,MAAM,CAACuvB,GAAG,CAAC,GAAG51B,UAAU,CAAC01B,MAAM,CAAC5S,KAAK,GAAG,GAAG,GAAG8S,GAAG,GAAGD,MAAM,CAAC,CAAC,IAAI,CAAC;EACnE;EACAtvB,MAAM,CAACyf,KAAK,GAAGzf,MAAM,CAACsG,IAAI,GAAGtG,MAAM,CAACuG,KAAK;EACzCvG,MAAM,CAAC6gB,MAAM,GAAG7gB,MAAM,CAACwiB,GAAG,GAAGxiB,MAAM,CAACyiB,MAAM;EAC1C,OAAOziB,MAAM;AACf;AACA,MAAMwvB,YAAY,GAAGA,CAAC1yB,CAAC,EAAEE,CAAC,EAAE5B,MAAM,KAAK,CAAC0B,CAAC,GAAG,CAAC,IAAIE,CAAC,GAAG,CAAC,MAAM,CAAC5B,MAAM,IAAI,CAACA,MAAM,CAACq0B,UAAU,CAAC;AAC1F,SAASC,iBAAiBA,CAAClxB,CAAC,EAAEiiB,MAAM,EAAE;EACpC,MAAMkP,OAAO,GAAGnxB,CAAC,CAACmxB,OAAO;EACzB,MAAMz0B,MAAM,GAAGy0B,OAAO,IAAIA,OAAO,CAACn1B,MAAM,GAAGm1B,OAAO,CAAC,CAAC,CAAC,GAAGnxB,CAAC;EACzD,MAAM;IAACoxB,OAAO;IAAEC;EAAO,CAAC,GAAG30B,MAAM;EACjC,IAAI40B,GAAG,GAAG,KAAK;EACf,IAAIhzB,CAAC,EAAEE,CAAC;EACR,IAAIwyB,YAAY,CAACI,OAAO,EAAEC,OAAO,EAAErxB,CAAC,CAACpD,MAAM,CAAC,EAAE;IAC5C0B,CAAC,GAAG8yB,OAAO;IACX5yB,CAAC,GAAG6yB,OAAO;EACb,CAAC,MAAM;IACL,MAAM7N,IAAI,GAAGvB,MAAM,CAACsP,qBAAqB,CAAC,CAAC;IAC3CjzB,CAAC,GAAG5B,MAAM,CAAC80B,OAAO,GAAGhO,IAAI,CAAC1b,IAAI;IAC9BtJ,CAAC,GAAG9B,MAAM,CAAC+0B,OAAO,GAAGjO,IAAI,CAACQ,GAAG;IAC7BsN,GAAG,GAAG,IAAI;EACZ;EACA,OAAO;IAAChzB,CAAC;IAAEE,CAAC;IAAE8yB;EAAG,CAAC;AACpB;AACA,SAASI,mBAAmBA,CAACC,GAAG,EAAEjU,KAAK,EAAE;EACvC,IAAI,QAAQ,IAAIiU,GAAG,EAAE;IACnB,OAAOA,GAAG;EACZ;EACA,MAAM;IAAC1P,MAAM;IAAEH;EAAuB,CAAC,GAAGpE,KAAK;EAC/C,MAAMO,KAAK,GAAGqS,gBAAgB,CAACrO,MAAM,CAAC;EACtC,MAAM2P,SAAS,GAAG3T,KAAK,CAAC4T,SAAS,KAAK,YAAY;EAClD,MAAMC,QAAQ,GAAGlB,kBAAkB,CAAC3S,KAAK,EAAE,SAAS,CAAC;EACrD,MAAM8T,OAAO,GAAGnB,kBAAkB,CAAC3S,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC;EAC5D,MAAM;IAAC3f,CAAC;IAAEE,CAAC;IAAE8yB;EAAG,CAAC,GAAGJ,iBAAiB,CAACS,GAAG,EAAE1P,MAAM,CAAC;EAClD,MAAMO,OAAO,GAAGsP,QAAQ,CAAChqB,IAAI,IAAIwpB,GAAG,IAAIS,OAAO,CAACjqB,IAAI,CAAC;EACrD,MAAM2a,OAAO,GAAGqP,QAAQ,CAAC9N,GAAG,IAAIsN,GAAG,IAAIS,OAAO,CAAC/N,GAAG,CAAC;EACnD,IAAI;IAAC/C,KAAK;IAAEoB;EAAM,CAAC,GAAG3E,KAAK;EAC3B,IAAIkU,SAAS,EAAE;IACb3Q,KAAK,IAAI6Q,QAAQ,CAAC7Q,KAAK,GAAG8Q,OAAO,CAAC9Q,KAAK;IACvCoB,MAAM,IAAIyP,QAAQ,CAACzP,MAAM,GAAG0P,OAAO,CAAC1P,MAAM;EAC5C;EACA,OAAO;IACL/jB,CAAC,EAAE4B,IAAI,CAACc,KAAK,CAAC,CAAC1C,CAAC,GAAGkkB,OAAO,IAAIvB,KAAK,GAAGgB,MAAM,CAAChB,KAAK,GAAGa,uBAAuB,CAAC;IAC7EtjB,CAAC,EAAE0B,IAAI,CAACc,KAAK,CAAC,CAACxC,CAAC,GAAGikB,OAAO,IAAIJ,MAAM,GAAGJ,MAAM,CAACI,MAAM,GAAGP,uBAAuB;EAChF,CAAC;AACH;AACA,SAASkQ,gBAAgBA,CAAC/P,MAAM,EAAEhB,KAAK,EAAEoB,MAAM,EAAE;EAC/C,IAAIoD,QAAQ,EAAEwM,SAAS;EACvB,IAAIhR,KAAK,KAAKhjB,SAAS,IAAIokB,MAAM,KAAKpkB,SAAS,EAAE;IAC/C,MAAMi0B,SAAS,GAAGpC,cAAc,CAAC7N,MAAM,CAAC;IACxC,IAAI,CAACiQ,SAAS,EAAE;MACdjR,KAAK,GAAGgB,MAAM,CAACkQ,WAAW;MAC1B9P,MAAM,GAAGJ,MAAM,CAACmQ,YAAY;IAC9B,CAAC,MAAM;MACL,MAAM5O,IAAI,GAAG0O,SAAS,CAACX,qBAAqB,CAAC,CAAC;MAC9C,MAAMc,cAAc,GAAG/B,gBAAgB,CAAC4B,SAAS,CAAC;MAClD,MAAMI,eAAe,GAAG1B,kBAAkB,CAACyB,cAAc,EAAE,QAAQ,EAAE,OAAO,CAAC;MAC7E,MAAME,gBAAgB,GAAG3B,kBAAkB,CAACyB,cAAc,EAAE,SAAS,CAAC;MACtEpR,KAAK,GAAGuC,IAAI,CAACvC,KAAK,GAAGsR,gBAAgB,CAACtR,KAAK,GAAGqR,eAAe,CAACrR,KAAK;MACnEoB,MAAM,GAAGmB,IAAI,CAACnB,MAAM,GAAGkQ,gBAAgB,CAAClQ,MAAM,GAAGiQ,eAAe,CAACjQ,MAAM;MACvEoD,QAAQ,GAAGyK,aAAa,CAACmC,cAAc,CAAC5M,QAAQ,EAAEyM,SAAS,EAAE,aAAa,CAAC;MAC3ED,SAAS,GAAG/B,aAAa,CAACmC,cAAc,CAACJ,SAAS,EAAEC,SAAS,EAAE,cAAc,CAAC;IAChF;EACF;EACA,OAAO;IACLjR,KAAK;IACLoB,MAAM;IACNoD,QAAQ,EAAEA,QAAQ,IAAIplB,QAAQ;IAC9B4xB,SAAS,EAAEA,SAAS,IAAI5xB;EAC1B,CAAC;AACH;AACA,MAAMmyB,MAAM,GAAGn0B,CAAC,IAAI6B,IAAI,CAACc,KAAK,CAAC3C,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE;AAC3C,SAASo0B,cAAcA,CAACxQ,MAAM,EAAEyQ,OAAO,EAAEC,QAAQ,EAAEC,WAAW,EAAE;EAC9D,MAAM3U,KAAK,GAAGqS,gBAAgB,CAACrO,MAAM,CAAC;EACtC,MAAM4Q,OAAO,GAAGjC,kBAAkB,CAAC3S,KAAK,EAAE,QAAQ,CAAC;EACnD,MAAMwH,QAAQ,GAAGyK,aAAa,CAACjS,KAAK,CAACwH,QAAQ,EAAExD,MAAM,EAAE,aAAa,CAAC,IAAI5hB,QAAQ;EACjF,MAAM4xB,SAAS,GAAG/B,aAAa,CAACjS,KAAK,CAACgU,SAAS,EAAEhQ,MAAM,EAAE,cAAc,CAAC,IAAI5hB,QAAQ;EACpF,MAAMyyB,aAAa,GAAGd,gBAAgB,CAAC/P,MAAM,EAAEyQ,OAAO,EAAEC,QAAQ,CAAC;EACjE,IAAI;IAAC1R,KAAK;IAAEoB;EAAM,CAAC,GAAGyQ,aAAa;EACnC,IAAI7U,KAAK,CAAC4T,SAAS,KAAK,aAAa,EAAE;IACrC,MAAME,OAAO,GAAGnB,kBAAkB,CAAC3S,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC;IAC5D,MAAM6T,QAAQ,GAAGlB,kBAAkB,CAAC3S,KAAK,EAAE,SAAS,CAAC;IACrDgD,KAAK,IAAI6Q,QAAQ,CAAC7Q,KAAK,GAAG8Q,OAAO,CAAC9Q,KAAK;IACvCoB,MAAM,IAAIyP,QAAQ,CAACzP,MAAM,GAAG0P,OAAO,CAAC1P,MAAM;EAC5C;EACApB,KAAK,GAAG/gB,IAAI,CAACqC,GAAG,CAAC,CAAC,EAAE0e,KAAK,GAAG4R,OAAO,CAAC5R,KAAK,CAAC;EAC1CoB,MAAM,GAAGniB,IAAI,CAACqC,GAAG,CAAC,CAAC,EAAEqwB,WAAW,GAAG1yB,IAAI,CAACkB,KAAK,CAAC6f,KAAK,GAAG2R,WAAW,CAAC,GAAGvQ,MAAM,GAAGwQ,OAAO,CAACxQ,MAAM,CAAC;EAC7FpB,KAAK,GAAGuR,MAAM,CAACtyB,IAAI,CAACoC,GAAG,CAAC2e,KAAK,EAAEwE,QAAQ,EAAEqN,aAAa,CAACrN,QAAQ,CAAC,CAAC;EACjEpD,MAAM,GAAGmQ,MAAM,CAACtyB,IAAI,CAACoC,GAAG,CAAC+f,MAAM,EAAE4P,SAAS,EAAEa,aAAa,CAACb,SAAS,CAAC,CAAC;EACrE,IAAIhR,KAAK,IAAI,CAACoB,MAAM,EAAE;IACpBA,MAAM,GAAGmQ,MAAM,CAACvR,KAAK,GAAG,CAAC,CAAC;EAC5B;EACA,OAAO;IACLA,KAAK;IACLoB;EACF,CAAC;AACH;AACA,SAAS0Q,WAAWA,CAACrV,KAAK,EAAEsV,UAAU,EAAEC,UAAU,EAAE;EAClD,MAAMC,UAAU,GAAGF,UAAU,IAAI,CAAC;EAClC,MAAMG,YAAY,GAAGjzB,IAAI,CAACkB,KAAK,CAACsc,KAAK,CAAC2E,MAAM,GAAG6Q,UAAU,CAAC;EAC1D,MAAME,WAAW,GAAGlzB,IAAI,CAACkB,KAAK,CAACsc,KAAK,CAACuD,KAAK,GAAGiS,UAAU,CAAC;EACxDxV,KAAK,CAAC2E,MAAM,GAAG8Q,YAAY,GAAGD,UAAU;EACxCxV,KAAK,CAACuD,KAAK,GAAGmS,WAAW,GAAGF,UAAU;EACtC,MAAMjR,MAAM,GAAGvE,KAAK,CAACuE,MAAM;EAC3B,IAAIA,MAAM,CAAChE,KAAK,KAAKgV,UAAU,IAAK,CAAChR,MAAM,CAAChE,KAAK,CAACoE,MAAM,IAAI,CAACJ,MAAM,CAAChE,KAAK,CAACgD,KAAM,CAAC,EAAE;IACjFgB,MAAM,CAAChE,KAAK,CAACoE,MAAM,GAAG,GAAG3E,KAAK,CAAC2E,MAAM,IAAI;IACzCJ,MAAM,CAAChE,KAAK,CAACgD,KAAK,GAAG,GAAGvD,KAAK,CAACuD,KAAK,IAAI;EACzC;EACA,IAAIvD,KAAK,CAACoE,uBAAuB,KAAKoR,UAAU,IACzCjR,MAAM,CAACI,MAAM,KAAK8Q,YAAY,IAC9BlR,MAAM,CAAChB,KAAK,KAAKmS,WAAW,EAAE;IACnC1V,KAAK,CAACoE,uBAAuB,GAAGoR,UAAU;IAC1CjR,MAAM,CAACI,MAAM,GAAG8Q,YAAY;IAC5BlR,MAAM,CAAChB,KAAK,GAAGmS,WAAW;IAC1B1V,KAAK,CAACW,GAAG,CAACgV,YAAY,CAACH,UAAU,EAAE,CAAC,EAAE,CAAC,EAAEA,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;IAC1D,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd;AACA,MAAMI,4BAA4B,GAAI,YAAW;EAC/C,IAAIC,gBAAgB,GAAG,KAAK;EAC5B,IAAI;IACF,MAAMn2B,OAAO,GAAG;MACd,IAAIo2B,OAAOA,CAAA,EAAG;QACZD,gBAAgB,GAAG,IAAI;QACvB,OAAO,KAAK;MACd;IACF,CAAC;IACDzsB,MAAM,CAAC2sB,gBAAgB,CAAC,MAAM,EAAE,IAAI,EAAEr2B,OAAO,CAAC;IAC9C0J,MAAM,CAAC4sB,mBAAmB,CAAC,MAAM,EAAE,IAAI,EAAEt2B,OAAO,CAAC;EACnD,CAAC,CAAC,OAAO4C,CAAC,EAAE,CACZ;EACA,OAAOuzB,gBAAgB;AACzB,CAAC,CAAC,CAAE;AACJ,SAASI,YAAYA,CAACpD,OAAO,EAAEluB,QAAQ,EAAE;EACvC,MAAMrI,KAAK,GAAGw2B,QAAQ,CAACD,OAAO,EAAEluB,QAAQ,CAAC;EACzC,MAAM6kB,OAAO,GAAGltB,KAAK,IAAIA,KAAK,CAACmtB,KAAK,CAAC,mBAAmB,CAAC;EACzD,OAAOD,OAAO,GAAG,CAACA,OAAO,CAAC,CAAC,CAAC,GAAGjpB,SAAS;AAC1C;AAEA,SAAS21B,YAAYA,CAAC/kB,EAAE,EAAEC,EAAE,EAAElF,CAAC,EAAE8U,IAAI,EAAE;EACrC,OAAO;IACLpgB,CAAC,EAAEuQ,EAAE,CAACvQ,CAAC,GAAGsL,CAAC,IAAIkF,EAAE,CAACxQ,CAAC,GAAGuQ,EAAE,CAACvQ,CAAC,CAAC;IAC3BE,CAAC,EAAEqQ,EAAE,CAACrQ,CAAC,GAAGoL,CAAC,IAAIkF,EAAE,CAACtQ,CAAC,GAAGqQ,EAAE,CAACrQ,CAAC;EAC5B,CAAC;AACH;AACA,SAASq1B,qBAAqBA,CAAChlB,EAAE,EAAEC,EAAE,EAAElF,CAAC,EAAE8U,IAAI,EAAE;EAC9C,OAAO;IACLpgB,CAAC,EAAEuQ,EAAE,CAACvQ,CAAC,GAAGsL,CAAC,IAAIkF,EAAE,CAACxQ,CAAC,GAAGuQ,EAAE,CAACvQ,CAAC,CAAC;IAC3BE,CAAC,EAAEkgB,IAAI,KAAK,QAAQ,GAAG9U,CAAC,GAAG,GAAG,GAAGiF,EAAE,CAACrQ,CAAC,GAAGsQ,EAAE,CAACtQ,CAAC,GAC1CkgB,IAAI,KAAK,OAAO,GAAG9U,CAAC,GAAG,CAAC,GAAGiF,EAAE,CAACrQ,CAAC,GAAGsQ,EAAE,CAACtQ,CAAC,GACtCoL,CAAC,GAAG,CAAC,GAAGkF,EAAE,CAACtQ,CAAC,GAAGqQ,EAAE,CAACrQ;EACtB,CAAC;AACH;AACA,SAASs1B,oBAAoBA,CAACjlB,EAAE,EAAEC,EAAE,EAAElF,CAAC,EAAE8U,IAAI,EAAE;EAC7C,MAAMqV,GAAG,GAAG;IAACz1B,CAAC,EAAEuQ,EAAE,CAAC8V,IAAI;IAAEnmB,CAAC,EAAEqQ,EAAE,CAACgW;EAAI,CAAC;EACpC,MAAMmP,GAAG,GAAG;IAAC11B,CAAC,EAAEwQ,EAAE,CAAC4V,IAAI;IAAElmB,CAAC,EAAEsQ,EAAE,CAAC8V;EAAI,CAAC;EACpC,MAAMllB,CAAC,GAAGk0B,YAAY,CAAC/kB,EAAE,EAAEklB,GAAG,EAAEnqB,CAAC,CAAC;EAClC,MAAMjK,CAAC,GAAGi0B,YAAY,CAACG,GAAG,EAAEC,GAAG,EAAEpqB,CAAC,CAAC;EACnC,MAAMqD,CAAC,GAAG2mB,YAAY,CAACI,GAAG,EAAEllB,EAAE,EAAElF,CAAC,CAAC;EAClC,MAAMqC,CAAC,GAAG2nB,YAAY,CAACl0B,CAAC,EAAEC,CAAC,EAAEiK,CAAC,CAAC;EAC/B,MAAM5J,CAAC,GAAG4zB,YAAY,CAACj0B,CAAC,EAAEsN,CAAC,EAAErD,CAAC,CAAC;EAC/B,OAAOgqB,YAAY,CAAC3nB,CAAC,EAAEjM,CAAC,EAAE4J,CAAC,CAAC;AAC9B;AAEA,MAAMqqB,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC3B,SAASC,eAAeA,CAACC,MAAM,EAAEh3B,OAAO,EAAE;EACxCA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvB,MAAMi3B,QAAQ,GAAGD,MAAM,GAAGE,IAAI,CAACC,SAAS,CAACn3B,OAAO,CAAC;EACjD,IAAIo3B,SAAS,GAAGP,SAAS,CAACzU,GAAG,CAAC6U,QAAQ,CAAC;EACvC,IAAI,CAACG,SAAS,EAAE;IACdA,SAAS,GAAG,IAAIC,IAAI,CAACC,YAAY,CAACN,MAAM,EAAEh3B,OAAO,CAAC;IAClD62B,SAAS,CAAC5tB,GAAG,CAACguB,QAAQ,EAAEG,SAAS,CAAC;EACpC;EACA,OAAOA,SAAS;AAClB;AACA,SAASG,YAAYA,CAACC,GAAG,EAAER,MAAM,EAAEh3B,OAAO,EAAE;EAC1C,OAAO+2B,eAAe,CAACC,MAAM,EAAEh3B,OAAO,CAAC,CAACy3B,MAAM,CAACD,GAAG,CAAC;AACrD;AAEA,MAAME,qBAAqB,GAAG,SAAAA,CAASC,KAAK,EAAE9T,KAAK,EAAE;EACnD,OAAO;IACL3iB,CAACA,CAACA,CAAC,EAAE;MACH,OAAOy2B,KAAK,GAAGA,KAAK,GAAG9T,KAAK,GAAG3iB,CAAC;IAClC,CAAC;IACD02B,QAAQA,CAAC9mB,CAAC,EAAE;MACV+S,KAAK,GAAG/S,CAAC;IACX,CAAC;IACD4X,SAASA,CAACne,KAAK,EAAE;MACf,IAAIA,KAAK,KAAK,QAAQ,EAAE;QACtB,OAAOA,KAAK;MACd;MACA,OAAOA,KAAK,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO;IAC7C,CAAC;IACDstB,KAAKA,CAAC32B,CAAC,EAAEtE,KAAK,EAAE;MACd,OAAOsE,CAAC,GAAGtE,KAAK;IAClB,CAAC;IACDk7B,UAAUA,CAAC52B,CAAC,EAAE62B,SAAS,EAAE;MACvB,OAAO72B,CAAC,GAAG62B,SAAS;IACtB;EACF,CAAC;AACH,CAAC;AACD,MAAMC,qBAAqB,GAAG,SAAAA,CAAA,EAAW;EACvC,OAAO;IACL92B,CAACA,CAACA,CAAC,EAAE;MACH,OAAOA,CAAC;IACV,CAAC;IACD02B,QAAQA,CAAC9mB,CAAC,EAAE,CACZ,CAAC;IACD4X,SAASA,CAACne,KAAK,EAAE;MACf,OAAOA,KAAK;IACd,CAAC;IACDstB,KAAKA,CAAC32B,CAAC,EAAEtE,KAAK,EAAE;MACd,OAAOsE,CAAC,GAAGtE,KAAK;IAClB,CAAC;IACDk7B,UAAUA,CAAC52B,CAAC,EAAE+2B,UAAU,EAAE;MACxB,OAAO/2B,CAAC;IACV;EACF,CAAC;AACH,CAAC;AACD,SAASg3B,aAAaA,CAACttB,GAAG,EAAE+sB,KAAK,EAAE9T,KAAK,EAAE;EACxC,OAAOjZ,GAAG,GAAG8sB,qBAAqB,CAACC,KAAK,EAAE9T,KAAK,CAAC,GAAGmU,qBAAqB,CAAC,CAAC;AAC5E;AACA,SAASG,qBAAqBA,CAAClX,GAAG,EAAEmX,SAAS,EAAE;EAC7C,IAAIvX,KAAK,EAAEwX,QAAQ;EACnB,IAAID,SAAS,KAAK,KAAK,IAAIA,SAAS,KAAK,KAAK,EAAE;IAC9CvX,KAAK,GAAGI,GAAG,CAAC4D,MAAM,CAAChE,KAAK;IACxBwX,QAAQ,GAAG,CACTxX,KAAK,CAACyS,gBAAgB,CAAC,WAAW,CAAC,EACnCzS,KAAK,CAACyX,mBAAmB,CAAC,WAAW,CAAC,CACvC;IACDzX,KAAK,CAAC0X,WAAW,CAAC,WAAW,EAAEH,SAAS,EAAE,WAAW,CAAC;IACtDnX,GAAG,CAACuX,iBAAiB,GAAGH,QAAQ;EAClC;AACF;AACA,SAASI,oBAAoBA,CAACxX,GAAG,EAAEoX,QAAQ,EAAE;EAC3C,IAAIA,QAAQ,KAAKx3B,SAAS,EAAE;IAC1B,OAAOogB,GAAG,CAACuX,iBAAiB;IAC5BvX,GAAG,CAAC4D,MAAM,CAAChE,KAAK,CAAC0X,WAAW,CAAC,WAAW,EAAEF,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC;EACrE;AACF;AAEA,SAASK,UAAUA,CAACzzB,QAAQ,EAAE;EAC5B,IAAIA,QAAQ,KAAK,OAAO,EAAE;IACxB,OAAO;MACL0zB,OAAO,EAAEnyB,aAAa;MACtBoyB,OAAO,EAAEtyB,UAAU;MACnBuyB,SAAS,EAAEtyB;IACb,CAAC;EACH;EACA,OAAO;IACLoyB,OAAO,EAAExxB,UAAU;IACnByxB,OAAO,EAAEA,CAACt2B,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC;IACxBs2B,SAAS,EAAE33B,CAAC,IAAIA;EAClB,CAAC;AACH;AACA,SAAS43B,gBAAgBA,CAAC;EAACryB,KAAK;EAAEC,GAAG;EAAEyE,KAAK;EAAE+mB,IAAI;EAAErR;AAAK,CAAC,EAAE;EAC1D,OAAO;IACLpa,KAAK,EAAEA,KAAK,GAAG0E,KAAK;IACpBzE,GAAG,EAAEA,GAAG,GAAGyE,KAAK;IAChB+mB,IAAI,EAAEA,IAAI,IAAI,CAACxrB,GAAG,GAAGD,KAAK,GAAG,CAAC,IAAI0E,KAAK,KAAK,CAAC;IAC7C0V;EACF,CAAC;AACH;AACA,SAASkY,UAAUA,CAACC,OAAO,EAAEhuB,MAAM,EAAEiuB,MAAM,EAAE;EAC3C,MAAM;IAACh0B,QAAQ;IAAEwB,KAAK,EAAEyyB,UAAU;IAAExyB,GAAG,EAAEyyB;EAAQ,CAAC,GAAGF,MAAM;EAC3D,MAAM;IAACN,OAAO;IAAEE;EAAS,CAAC,GAAGH,UAAU,CAACzzB,QAAQ,CAAC;EACjD,MAAMkG,KAAK,GAAGH,MAAM,CAACpM,MAAM;EAC3B,IAAI;IAAC6H,KAAK;IAAEC,GAAG;IAAEwrB;EAAI,CAAC,GAAG8G,OAAO;EAChC,IAAIv6B,CAAC,EAAEO,IAAI;EACX,IAAIkzB,IAAI,EAAE;IACRzrB,KAAK,IAAI0E,KAAK;IACdzE,GAAG,IAAIyE,KAAK;IACZ,KAAK1M,CAAC,GAAG,CAAC,EAAEO,IAAI,GAAGmM,KAAK,EAAE1M,CAAC,GAAGO,IAAI,EAAE,EAAEP,CAAC,EAAE;MACvC,IAAI,CAACk6B,OAAO,CAACE,SAAS,CAAC7tB,MAAM,CAACvE,KAAK,GAAG0E,KAAK,CAAC,CAAClG,QAAQ,CAAC,CAAC,EAAEi0B,UAAU,EAAEC,QAAQ,CAAC,EAAE;QAC9E;MACF;MACA1yB,KAAK,EAAE;MACPC,GAAG,EAAE;IACP;IACAD,KAAK,IAAI0E,KAAK;IACdzE,GAAG,IAAIyE,KAAK;EACd;EACA,IAAIzE,GAAG,GAAGD,KAAK,EAAE;IACfC,GAAG,IAAIyE,KAAK;EACd;EACA,OAAO;IAAC1E,KAAK;IAAEC,GAAG;IAAEwrB,IAAI;IAAErR,KAAK,EAAEmY,OAAO,CAACnY;EAAK,CAAC;AACjD;AACA,SAASuY,aAAaA,CAACJ,OAAO,EAAEhuB,MAAM,EAAEiuB,MAAM,EAAE;EAC9C,IAAI,CAACA,MAAM,EAAE;IACX,OAAO,CAACD,OAAO,CAAC;EAClB;EACA,MAAM;IAAC/zB,QAAQ;IAAEwB,KAAK,EAAEyyB,UAAU;IAAExyB,GAAG,EAAEyyB;EAAQ,CAAC,GAAGF,MAAM;EAC3D,MAAM9tB,KAAK,GAAGH,MAAM,CAACpM,MAAM;EAC3B,MAAM;IAACg6B,OAAO;IAAED,OAAO;IAAEE;EAAS,CAAC,GAAGH,UAAU,CAACzzB,QAAQ,CAAC;EAC1D,MAAM;IAACwB,KAAK;IAAEC,GAAG;IAAEwrB,IAAI;IAAErR;EAAK,CAAC,GAAGkY,UAAU,CAACC,OAAO,EAAEhuB,MAAM,EAAEiuB,MAAM,CAAC;EACrE,MAAM70B,MAAM,GAAG,EAAE;EACjB,IAAIi1B,MAAM,GAAG,KAAK;EAClB,IAAIC,QAAQ,GAAG,IAAI;EACnB,IAAI18B,KAAK,EAAE6pB,KAAK,EAAE8S,SAAS;EAC3B,MAAMC,aAAa,GAAGA,CAAA,KAAMb,OAAO,CAACO,UAAU,EAAEK,SAAS,EAAE38B,KAAK,CAAC,IAAIg8B,OAAO,CAACM,UAAU,EAAEK,SAAS,CAAC,KAAK,CAAC;EACzG,MAAME,WAAW,GAAGA,CAAA,KAAMb,OAAO,CAACO,QAAQ,EAAEv8B,KAAK,CAAC,KAAK,CAAC,IAAI+7B,OAAO,CAACQ,QAAQ,EAAEI,SAAS,EAAE38B,KAAK,CAAC;EAC/F,MAAM88B,WAAW,GAAGA,CAAA,KAAML,MAAM,IAAIG,aAAa,CAAC,CAAC;EACnD,MAAMG,UAAU,GAAGA,CAAA,KAAM,CAACN,MAAM,IAAII,WAAW,CAAC,CAAC;EACjD,KAAK,IAAIh7B,CAAC,GAAGgI,KAAK,EAAE6rB,IAAI,GAAG7rB,KAAK,EAAEhI,CAAC,IAAIiI,GAAG,EAAE,EAAEjI,CAAC,EAAE;IAC/CgoB,KAAK,GAAGzb,MAAM,CAACvM,CAAC,GAAG0M,KAAK,CAAC;IACzB,IAAIsb,KAAK,CAACmJ,IAAI,EAAE;MACd;IACF;IACAhzB,KAAK,GAAGi8B,SAAS,CAACpS,KAAK,CAACxhB,QAAQ,CAAC,CAAC;IAClC,IAAIrI,KAAK,KAAK28B,SAAS,EAAE;MACvB;IACF;IACAF,MAAM,GAAGV,OAAO,CAAC/7B,KAAK,EAAEs8B,UAAU,EAAEC,QAAQ,CAAC;IAC7C,IAAIG,QAAQ,KAAK,IAAI,IAAII,WAAW,CAAC,CAAC,EAAE;MACtCJ,QAAQ,GAAGV,OAAO,CAACh8B,KAAK,EAAEs8B,UAAU,CAAC,KAAK,CAAC,GAAGz6B,CAAC,GAAG6zB,IAAI;IACxD;IACA,IAAIgH,QAAQ,KAAK,IAAI,IAAIK,UAAU,CAAC,CAAC,EAAE;MACrCv1B,MAAM,CAACtC,IAAI,CAACg3B,gBAAgB,CAAC;QAACryB,KAAK,EAAE6yB,QAAQ;QAAE5yB,GAAG,EAAEjI,CAAC;QAAEyzB,IAAI;QAAE/mB,KAAK;QAAE0V;MAAK,CAAC,CAAC,CAAC;MAC5EyY,QAAQ,GAAG,IAAI;IACjB;IACAhH,IAAI,GAAG7zB,CAAC;IACR86B,SAAS,GAAG38B,KAAK;EACnB;EACA,IAAI08B,QAAQ,KAAK,IAAI,EAAE;IACrBl1B,MAAM,CAACtC,IAAI,CAACg3B,gBAAgB,CAAC;MAACryB,KAAK,EAAE6yB,QAAQ;MAAE5yB,GAAG;MAAEwrB,IAAI;MAAE/mB,KAAK;MAAE0V;IAAK,CAAC,CAAC,CAAC;EAC3E;EACA,OAAOzc,MAAM;AACf;AACA,SAASw1B,cAAcA,CAAC5R,IAAI,EAAEiR,MAAM,EAAE;EACpC,MAAM70B,MAAM,GAAG,EAAE;EACjB,MAAMy1B,QAAQ,GAAG7R,IAAI,CAAC6R,QAAQ;EAC9B,KAAK,IAAIp7B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGo7B,QAAQ,CAACj7B,MAAM,EAAEH,CAAC,EAAE,EAAE;IACxC,MAAMq7B,GAAG,GAAGV,aAAa,CAACS,QAAQ,CAACp7B,CAAC,CAAC,EAAEupB,IAAI,CAAChd,MAAM,EAAEiuB,MAAM,CAAC;IAC3D,IAAIa,GAAG,CAACl7B,MAAM,EAAE;MACdwF,MAAM,CAACtC,IAAI,CAAC,GAAGg4B,GAAG,CAAC;IACrB;EACF;EACA,OAAO11B,MAAM;AACf;AACA,SAAS21B,eAAeA,CAAC/uB,MAAM,EAAEG,KAAK,EAAE+mB,IAAI,EAAEE,QAAQ,EAAE;EACtD,IAAI3rB,KAAK,GAAG,CAAC;EACb,IAAIC,GAAG,GAAGyE,KAAK,GAAG,CAAC;EACnB,IAAI+mB,IAAI,IAAI,CAACE,QAAQ,EAAE;IACrB,OAAO3rB,KAAK,GAAG0E,KAAK,IAAI,CAACH,MAAM,CAACvE,KAAK,CAAC,CAACmpB,IAAI,EAAE;MAC3CnpB,KAAK,EAAE;IACT;EACF;EACA,OAAOA,KAAK,GAAG0E,KAAK,IAAIH,MAAM,CAACvE,KAAK,CAAC,CAACmpB,IAAI,EAAE;IAC1CnpB,KAAK,EAAE;EACT;EACAA,KAAK,IAAI0E,KAAK;EACd,IAAI+mB,IAAI,EAAE;IACRxrB,GAAG,IAAID,KAAK;EACd;EACA,OAAOC,GAAG,GAAGD,KAAK,IAAIuE,MAAM,CAACtE,GAAG,GAAGyE,KAAK,CAAC,CAACykB,IAAI,EAAE;IAC9ClpB,GAAG,EAAE;EACP;EACAA,GAAG,IAAIyE,KAAK;EACZ,OAAO;IAAC1E,KAAK;IAAEC;EAAG,CAAC;AACrB;AACA,SAASszB,aAAaA,CAAChvB,MAAM,EAAEvE,KAAK,EAAEtB,GAAG,EAAE+sB,IAAI,EAAE;EAC/C,MAAM/mB,KAAK,GAAGH,MAAM,CAACpM,MAAM;EAC3B,MAAMwF,MAAM,GAAG,EAAE;EACjB,IAAIuD,IAAI,GAAGlB,KAAK;EAChB,IAAI6rB,IAAI,GAAGtnB,MAAM,CAACvE,KAAK,CAAC;EACxB,IAAIC,GAAG;EACP,KAAKA,GAAG,GAAGD,KAAK,GAAG,CAAC,EAAEC,GAAG,IAAIvB,GAAG,EAAE,EAAEuB,GAAG,EAAE;IACvC,MAAMuzB,GAAG,GAAGjvB,MAAM,CAACtE,GAAG,GAAGyE,KAAK,CAAC;IAC/B,IAAI8uB,GAAG,CAACrK,IAAI,IAAIqK,GAAG,CAACC,IAAI,EAAE;MACxB,IAAI,CAAC5H,IAAI,CAAC1C,IAAI,EAAE;QACdsC,IAAI,GAAG,KAAK;QACZ9tB,MAAM,CAACtC,IAAI,CAAC;UAAC2E,KAAK,EAAEA,KAAK,GAAG0E,KAAK;UAAEzE,GAAG,EAAE,CAACA,GAAG,GAAG,CAAC,IAAIyE,KAAK;UAAE+mB;QAAI,CAAC,CAAC;QACjEzrB,KAAK,GAAGkB,IAAI,GAAGsyB,GAAG,CAACC,IAAI,GAAGxzB,GAAG,GAAG,IAAI;MACtC;IACF,CAAC,MAAM;MACLiB,IAAI,GAAGjB,GAAG;MACV,IAAI4rB,IAAI,CAAC1C,IAAI,EAAE;QACbnpB,KAAK,GAAGC,GAAG;MACb;IACF;IACA4rB,IAAI,GAAG2H,GAAG;EACZ;EACA,IAAItyB,IAAI,KAAK,IAAI,EAAE;IACjBvD,MAAM,CAACtC,IAAI,CAAC;MAAC2E,KAAK,EAAEA,KAAK,GAAG0E,KAAK;MAAEzE,GAAG,EAAEiB,IAAI,GAAGwD,KAAK;MAAE+mB;IAAI,CAAC,CAAC;EAC9D;EACA,OAAO9tB,MAAM;AACf;AACA,SAAS+1B,gBAAgBA,CAACnS,IAAI,EAAEoS,cAAc,EAAE;EAC9C,MAAMpvB,MAAM,GAAGgd,IAAI,CAAChd,MAAM;EAC1B,MAAMonB,QAAQ,GAAGpK,IAAI,CAAChoB,OAAO,CAACoyB,QAAQ;EACtC,MAAMjnB,KAAK,GAAGH,MAAM,CAACpM,MAAM;EAC3B,IAAI,CAACuM,KAAK,EAAE;IACV,OAAO,EAAE;EACX;EACA,MAAM+mB,IAAI,GAAG,CAAC,CAAClK,IAAI,CAACqS,KAAK;EACzB,MAAM;IAAC5zB,KAAK;IAAEC;EAAG,CAAC,GAAGqzB,eAAe,CAAC/uB,MAAM,EAAEG,KAAK,EAAE+mB,IAAI,EAAEE,QAAQ,CAAC;EACnE,IAAIA,QAAQ,KAAK,IAAI,EAAE;IACrB,OAAOkI,aAAa,CAACtS,IAAI,EAAE,CAAC;MAACvhB,KAAK;MAAEC,GAAG;MAAEwrB;IAAI,CAAC,CAAC,EAAElnB,MAAM,EAAEovB,cAAc,CAAC;EAC1E;EACA,MAAMj1B,GAAG,GAAGuB,GAAG,GAAGD,KAAK,GAAGC,GAAG,GAAGyE,KAAK,GAAGzE,GAAG;EAC3C,MAAM6zB,YAAY,GAAG,CAAC,CAACvS,IAAI,CAACwS,SAAS,IAAI/zB,KAAK,KAAK,CAAC,IAAIC,GAAG,KAAKyE,KAAK,GAAG,CAAC;EACzE,OAAOmvB,aAAa,CAACtS,IAAI,EAAEgS,aAAa,CAAChvB,MAAM,EAAEvE,KAAK,EAAEtB,GAAG,EAAEo1B,YAAY,CAAC,EAAEvvB,MAAM,EAAEovB,cAAc,CAAC;AACrG;AACA,SAASE,aAAaA,CAACtS,IAAI,EAAE6R,QAAQ,EAAE7uB,MAAM,EAAEovB,cAAc,EAAE;EAC7D,IAAI,CAACA,cAAc,IAAI,CAACA,cAAc,CAAC5M,UAAU,IAAI,CAACxiB,MAAM,EAAE;IAC5D,OAAO6uB,QAAQ;EACjB;EACA,OAAOY,eAAe,CAACzS,IAAI,EAAE6R,QAAQ,EAAE7uB,MAAM,EAAEovB,cAAc,CAAC;AAChE;AACA,SAASK,eAAeA,CAACzS,IAAI,EAAE6R,QAAQ,EAAE7uB,MAAM,EAAEovB,cAAc,EAAE;EAC/D,MAAMM,YAAY,GAAG1S,IAAI,CAAC2S,MAAM,CAAC7V,UAAU,CAAC,CAAC;EAC7C,MAAM8V,SAAS,GAAGC,SAAS,CAAC7S,IAAI,CAAChoB,OAAO,CAAC;EACzC,MAAM;IAAC86B,aAAa,EAAE37B,YAAY;IAAEa,OAAO,EAAE;MAACoyB;IAAQ;EAAC,CAAC,GAAGpK,IAAI;EAC/D,MAAM7c,KAAK,GAAGH,MAAM,CAACpM,MAAM;EAC3B,MAAMwF,MAAM,GAAG,EAAE;EACjB,IAAI22B,SAAS,GAAGH,SAAS;EACzB,IAAIn0B,KAAK,GAAGozB,QAAQ,CAAC,CAAC,CAAC,CAACpzB,KAAK;EAC7B,IAAIhI,CAAC,GAAGgI,KAAK;EACb,SAASu0B,QAAQA,CAACp0B,CAAC,EAAEhE,CAAC,EAAEoM,CAAC,EAAEisB,EAAE,EAAE;IAC7B,MAAMC,GAAG,GAAG9I,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC;IAC7B,IAAIxrB,CAAC,KAAKhE,CAAC,EAAE;MACX;IACF;IACAgE,CAAC,IAAIuE,KAAK;IACV,OAAOH,MAAM,CAACpE,CAAC,GAAGuE,KAAK,CAAC,CAACykB,IAAI,EAAE;MAC7BhpB,CAAC,IAAIs0B,GAAG;IACV;IACA,OAAOlwB,MAAM,CAACpI,CAAC,GAAGuI,KAAK,CAAC,CAACykB,IAAI,EAAE;MAC7BhtB,CAAC,IAAIs4B,GAAG;IACV;IACA,IAAIt0B,CAAC,GAAGuE,KAAK,KAAKvI,CAAC,GAAGuI,KAAK,EAAE;MAC3B/G,MAAM,CAACtC,IAAI,CAAC;QAAC2E,KAAK,EAAEG,CAAC,GAAGuE,KAAK;QAAEzE,GAAG,EAAE9D,CAAC,GAAGuI,KAAK;QAAE+mB,IAAI,EAAEljB,CAAC;QAAE6R,KAAK,EAAEoa;MAAE,CAAC,CAAC;MACnEF,SAAS,GAAGE,EAAE;MACdx0B,KAAK,GAAG7D,CAAC,GAAGuI,KAAK;IACnB;EACF;EACA,KAAK,MAAM6tB,OAAO,IAAIa,QAAQ,EAAE;IAC9BpzB,KAAK,GAAG2rB,QAAQ,GAAG3rB,KAAK,GAAGuyB,OAAO,CAACvyB,KAAK;IACxC,IAAI6rB,IAAI,GAAGtnB,MAAM,CAACvE,KAAK,GAAG0E,KAAK,CAAC;IAChC,IAAI0V,KAAK;IACT,KAAKpiB,CAAC,GAAGgI,KAAK,GAAG,CAAC,EAAEhI,CAAC,IAAIu6B,OAAO,CAACtyB,GAAG,EAAEjI,CAAC,EAAE,EAAE;MACzC,MAAMmzB,EAAE,GAAG5mB,MAAM,CAACvM,CAAC,GAAG0M,KAAK,CAAC;MAC5B0V,KAAK,GAAGga,SAAS,CAACT,cAAc,CAAC5M,UAAU,CAACnC,aAAa,CAACqP,YAAY,EAAE;QACtE39B,IAAI,EAAE,SAAS;QACfo+B,EAAE,EAAE7I,IAAI;QACR7gB,EAAE,EAAEmgB,EAAE;QACNwJ,WAAW,EAAE,CAAC38B,CAAC,GAAG,CAAC,IAAI0M,KAAK;QAC5BkwB,WAAW,EAAE58B,CAAC,GAAG0M,KAAK;QACtBhM;MACF,CAAC,CAAC,CAAC,CAAC;MACJ,IAAIm8B,YAAY,CAACza,KAAK,EAAEka,SAAS,CAAC,EAAE;QAClCC,QAAQ,CAACv0B,KAAK,EAAEhI,CAAC,GAAG,CAAC,EAAEu6B,OAAO,CAAC9G,IAAI,EAAE6I,SAAS,CAAC;MACjD;MACAzI,IAAI,GAAGV,EAAE;MACTmJ,SAAS,GAAGla,KAAK;IACnB;IACA,IAAIpa,KAAK,GAAGhI,CAAC,GAAG,CAAC,EAAE;MACjBu8B,QAAQ,CAACv0B,KAAK,EAAEhI,CAAC,GAAG,CAAC,EAAEu6B,OAAO,CAAC9G,IAAI,EAAE6I,SAAS,CAAC;IACjD;EACF;EACA,OAAO32B,MAAM;AACf;AACA,SAASy2B,SAASA,CAAC76B,OAAO,EAAE;EAC1B,OAAO;IACLigB,eAAe,EAAEjgB,OAAO,CAACigB,eAAe;IACxCsb,cAAc,EAAEv7B,OAAO,CAACu7B,cAAc;IACtCC,UAAU,EAAEx7B,OAAO,CAACw7B,UAAU;IAC9BC,gBAAgB,EAAEz7B,OAAO,CAACy7B,gBAAgB;IAC1CC,eAAe,EAAE17B,OAAO,CAAC07B,eAAe;IACxCpV,WAAW,EAAEtmB,OAAO,CAACsmB,WAAW;IAChCpG,WAAW,EAAElgB,OAAO,CAACkgB;EACvB,CAAC;AACH;AACA,SAASob,YAAYA,CAACza,KAAK,EAAEka,SAAS,EAAE;EACtC,OAAOA,SAAS,IAAI7D,IAAI,CAACC,SAAS,CAACtW,KAAK,CAAC,KAAKqW,IAAI,CAACC,SAAS,CAAC4D,SAAS,CAAC;AACzE;AAEA,SAASvU,cAAc,IAAImV,CAAC,EAAEx3B,UAAU,IAAIoL,CAAC,EAAE9R,eAAe,IAAI+R,CAAC,EAAEvR,QAAQ,IAAIwR,CAAC,EAAEsb,SAAS,IAAIrb,CAAC,EAAEzI,WAAW,IAAI0I,CAAC,EAAErK,SAAS,IAAIsK,CAAC,EAAE0T,YAAY,IAAIxQ,CAAC,EAAE1P,OAAO,IAAI2P,CAAC,EAAE7L,WAAW,IAAI8L,CAAC,EAAEwR,WAAW,IAAIvR,CAAC,EAAEuX,SAAS,IAAI3X,CAAC,EAAEiU,QAAQ,IAAIlU,CAAC,EAAE8U,UAAU,IAAI/U,CAAC,EAAEqU,UAAU,IAAItU,CAAC,EAAE+X,MAAM,IAAIhY,CAAC,EAAE5P,EAAE,IAAI2P,CAAC,EAAElU,IAAI,IAAIiU,CAAC,EAAEjI,kBAAkB,IAAIgI,CAAC,EAAE9H,cAAc,IAAI6H,CAAC,EAAEtP,GAAG,IAAIqP,CAAC,EAAEqN,SAAS,IAAItN,CAAC,EAAEhS,KAAK,IAAI+R,CAAC,EAAEnQ,WAAW,IAAIkQ,CAAC,EAAEqiB,mBAAmB,IAAItiB,CAAC,EAAEpK,aAAa,IAAImK,CAAC,EAAErK,YAAY,IAAIoK,CAAC,EAAE/I,YAAY,IAAI6yB,CAAC,EAAEjR,OAAO,IAAIroB,CAAC,EAAEunB,YAAY,IAAIgS,EAAE,EAAEn2B,iBAAiB,IAAI5G,EAAE,EAAEu2B,cAAc,IAAIt2B,EAAE,EAAE2zB,cAAc,IAAIoJ,EAAE,EAAEvF,YAAY,IAAIwF,EAAE,EAAEnyB,SAAS,IAAIoyB,EAAE,EAAE9F,4BAA4B,IAAI+F,EAAE,EAAEzJ,eAAe,IAAI0J,EAAE,EAAExc,WAAW,IAAIyc,EAAE,EAAE/5B,UAAU,IAAIg6B,EAAE,EAAEpP,cAAc,IAAIqP,EAAE,EAAEnE,aAAa,IAAIoE,EAAE,EAAEnE,qBAAqB,IAAIoE,EAAE,EAAE9xB,MAAM,IAAI+xB,EAAE,EAAE/D,oBAAoB,IAAIgE,EAAE,EAAEtX,eAAe,IAAIuX,EAAE,EAAElgC,IAAI,IAAImgC,EAAE,EAAEx2B,qBAAqB,IAAIy2B,EAAE,EAAE73B,kBAAkB,IAAI83B,EAAE,EAAEp5B,OAAO,IAAIq5B,EAAE,EAAEj4B,WAAW,IAAIk4B,EAAE,EAAEl5B,YAAY,IAAIm5B,EAAE,EAAEx3B,cAAc,IAAIy3B,EAAE,EAAEnZ,YAAY,IAAIoZ,EAAE,EAAEr1B,cAAc,IAAIs1B,EAAE,EAAE/1B,OAAO,IAAIg2B,EAAE,EAAE7d,mBAAmB,IAAI8d,EAAE,EAAE7d,aAAa,IAAI8d,EAAE,EAAEj+B,OAAO,IAAIk+B,EAAE,EAAEx9B,OAAO,IAAIy9B,EAAE,EAAEj9B,SAAS,IAAIk9B,EAAE,EAAEh9B,WAAW,IAAIi9B,EAAE,EAAEj8B,SAAS,IAAIk8B,EAAE,EAAEta,YAAY,IAAIua,EAAE,EAAE9N,WAAW,IAAI+N,EAAE,EAAEpM,mBAAmB,IAAIqM,EAAE,EAAE1K,QAAQ,IAAI2K,EAAE,EAAE10B,UAAU,IAAI20B,EAAE,EAAEzS,eAAe,IAAI0S,EAAE,EAAEle,YAAY,IAAIme,EAAE,EAAE59B,OAAO,IAAI69B,EAAE,EAAE1hC,GAAG,IAAI2hC,EAAE,EAAEn0B,QAAQ,IAAIo0B,EAAE,EAAE1I,WAAW,IAAI2I,EAAE,EAAE1Z,WAAW,IAAI2Z,EAAE,EAAEl8B,SAAS,IAAIm8B,EAAE,EAAE3/B,cAAc,IAAI4/B,EAAE,EAAE97B,aAAa,IAAI+7B,EAAE,EAAEv3B,UAAU,IAAIw3B,EAAE,EAAE1U,iBAAiB,IAAI2U,EAAE,EAAE3M,0BAA0B,IAAI4M,EAAE,EAAE1E,gBAAgB,IAAI2E,EAAE,EAAElF,cAAc,IAAImF,EAAE,EAAEtI,qBAAqB,IAAIuI,EAAE,EAAEtI,oBAAoB,IAAIuI,EAAE,EAAEzI,YAAY,IAAI0I,EAAE,EAAEjY,cAAc,IAAIkY,EAAE,EAAE/X,cAAc,IAAIgY,EAAE,EAAEla,SAAS,IAAIma,EAAE,EAAEhW,kBAAkB,IAAIiW,EAAE,EAAEhV,MAAM,IAAIiV,EAAE,EAAEhV,aAAa,IAAIiV,EAAE,EAAEpG,aAAa,IAAIqG,EAAE,EAAEl5B,eAAe,IAAIm5B,EAAE,EAAE7iC,OAAO,IAAI0F,CAAC,EAAES,KAAK,IAAI28B,EAAE,EAAE18B,QAAQ,IAAI28B,EAAE,EAAEz8B,WAAW,IAAI08B,EAAE,EAAEx8B,UAAU,IAAIy8B,EAAE,EAAEx8B,aAAa,IAAIy8B,EAAE,EAAEz5B,UAAU,IAAI05B,EAAE,EAAEzhB,KAAK,IAAI1O,CAAC,EAAEmT,QAAQ,IAAInU,CAAC,EAAEjC,OAAO,IAAIhK,CAAC,EAAEvB,gBAAgB,IAAIyO,CAAC,EAAExS,cAAc,IAAI+S,CAAC,EAAEgb,aAAa,IAAIpc,CAAC,EAAE5R,QAAQ,IAAIoB,CAAC,EAAE0D,OAAO,IAAIua,CAAC,EAAE/f,aAAa,IAAIgD,CAAC,EAAEqI,iBAAiB,IAAIgH,CAAC,EAAEpR,YAAY,IAAIgR,CAAC,EAAE5Q,WAAW,IAAIyG,CAAC,EAAE8yB,YAAY,IAAIp2B,CAAC,EAAEqF,aAAa,IAAIf,CAAC,EAAEqF,gCAAgC,IAAIm1B,CAAC,EAAEx2B,gBAAgB,IAAI2G,CAAC,EAAE5M,IAAI,IAAIoD,CAAC,EAAExB,SAAS,IAAIoH,CAAC,EAAE5D,mBAAmB,IAAIs3B,CAAC,EAAEviC,cAAc,IAAIsD,CAAC,EAAE2K,mBAAmB,IAAIkF,CAAC,EAAEtM,QAAQ,IAAItD,CAAC,EAAEouB,2BAA2B,IAAIluB,CAAC,EAAEmC,KAAK,IAAI48B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}