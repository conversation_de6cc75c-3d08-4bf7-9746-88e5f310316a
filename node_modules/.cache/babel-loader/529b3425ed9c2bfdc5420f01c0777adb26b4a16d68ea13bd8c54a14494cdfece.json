{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Charts/UsageBarChart.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport Chart from 'chart.js/auto';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction UsageBarChart({\n  members,\n  usageData\n}) {\n  _s();\n  const chartRef = useRef(null);\n  const chartInstance = useRef(null);\n  useEffect(() => {\n    if (!members || members.length === 0 || !usageData) return;\n\n    // 销毁之前的图表实例\n    if (chartInstance.current) {\n      chartInstance.current.destroy();\n    }\n    const ctx = chartRef.current.getContext('2d');\n\n    // 准备数据\n    const labels = members.map(member => member.name || member.email || member.id);\n    const promptsData = members.map(member => {\n      var _usageData$memberId;\n      const memberId = member.email || member.id || member.userId;\n      return ((_usageData$memberId = usageData[memberId]) === null || _usageData$memberId === void 0 ? void 0 : _usageData$memberId.totalPrompts) || 0;\n    });\n    const tokensData = members.map(member => {\n      const memberId = member.email || member.id || member.userId;\n      const usage = usageData[memberId] || {};\n      return (usage.totalLinesAdded || 0) + (usage.totalLinesDeleted || 0);\n    });\n\n    // 创建新图表\n    chartInstance.current = new Chart(ctx, {\n      type: 'bar',\n      data: {\n        labels: labels,\n        datasets: [{\n          label: '提示次数',\n          data: promptsData,\n          backgroundColor: 'rgba(75, 192, 192, 0.6)',\n          borderColor: 'rgb(75, 192, 192)',\n          borderWidth: 1,\n          yAxisID: 'y'\n        }, {\n          label: '代码行数',\n          data: tokensData,\n          backgroundColor: 'rgba(255, 99, 132, 0.6)',\n          borderColor: 'rgb(255, 99, 132)',\n          borderWidth: 1,\n          yAxisID: 'y1'\n        }]\n      },\n      options: {\n        responsive: true,\n        scales: {\n          y: {\n            type: 'linear',\n            display: true,\n            position: 'left',\n            title: {\n              display: true,\n              text: '提示次数'\n            },\n            beginAtZero: true\n          },\n          y1: {\n            type: 'linear',\n            display: true,\n            position: 'right',\n            title: {\n              display: true,\n              text: '消耗Token (千)'\n            },\n            beginAtZero: true,\n            grid: {\n              drawOnChartArea: false\n            }\n          }\n        }\n      }\n    });\n    return () => {\n      if (chartInstance.current) {\n        chartInstance.current.destroy();\n      }\n    };\n  }, [members, usageData]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"chart-wrapper\",\n    children: /*#__PURE__*/_jsxDEV(\"canvas\", {\n      ref: chartRef\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 92,\n    columnNumber: 5\n  }, this);\n}\n_s(UsageBarChart, \"u5+iHnwD4hjVcMuzTE/TbI78erc=\");\n_c = UsageBarChart;\nexport default UsageBarChart;\nvar _c;\n$RefreshReg$(_c, \"UsageBarChart\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "Chart", "jsxDEV", "_jsxDEV", "UsageBarChart", "members", "usageData", "_s", "chartRef", "chartInstance", "length", "current", "destroy", "ctx", "getContext", "labels", "map", "member", "name", "email", "id", "promptsData", "_usageData$memberId", "memberId", "userId", "totalPrompts", "tokensData", "usage", "totalLinesAdded", "totalLinesDeleted", "type", "data", "datasets", "label", "backgroundColor", "borderColor", "borderWidth", "yAxisID", "options", "responsive", "scales", "y", "display", "position", "title", "text", "beginAtZero", "y1", "grid", "drawOnChartArea", "className", "children", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Charts/UsageBarChart.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\nimport Chart from 'chart.js/auto';\n\nfunction UsageBarChart({ members, usageData }) {\n  const chartRef = useRef(null);\n  const chartInstance = useRef(null);\n\n  useEffect(() => {\n    if (!members || members.length === 0 || !usageData) return;\n\n    // 销毁之前的图表实例\n    if (chartInstance.current) {\n      chartInstance.current.destroy();\n    }\n\n    const ctx = chartRef.current.getContext('2d');\n    \n    // 准备数据\n    const labels = members.map(member => member.name || member.email || member.id);\n    const promptsData = members.map(member => {\n      const memberId = member.email || member.id || member.userId;\n      return usageData[memberId]?.totalPrompts || 0;\n    });\n    const tokensData = members.map(member => {\n      const memberId = member.email || member.id || member.userId;\n      const usage = usageData[memberId] || {};\n      return (usage.totalLinesAdded || 0) + (usage.totalLinesDeleted || 0);\n    });\n\n    // 创建新图表\n    chartInstance.current = new Chart(ctx, {\n      type: 'bar',\n      data: {\n        labels: labels,\n        datasets: [\n          {\n            label: '提示次数',\n            data: promptsData,\n            backgroundColor: 'rgba(75, 192, 192, 0.6)',\n            borderColor: 'rgb(75, 192, 192)',\n            borderWidth: 1,\n            yAxisID: 'y'\n          },\n          {\n            label: '代码行数',\n            data: tokensData,\n            backgroundColor: 'rgba(255, 99, 132, 0.6)',\n            borderColor: 'rgb(255, 99, 132)',\n            borderWidth: 1,\n            yAxisID: 'y1'\n          }\n        ]\n      },\n      options: {\n        responsive: true,\n        scales: {\n          y: {\n            type: 'linear',\n            display: true,\n            position: 'left',\n            title: {\n              display: true,\n              text: '提示次数'\n            },\n            beginAtZero: true\n          },\n          y1: {\n            type: 'linear',\n            display: true,\n            position: 'right',\n            title: {\n              display: true,\n              text: '消耗Token (千)'\n            },\n            beginAtZero: true,\n            grid: {\n              drawOnChartArea: false,\n            },\n          }\n        }\n      }\n    });\n\n    return () => {\n      if (chartInstance.current) {\n        chartInstance.current.destroy();\n      }\n    };\n  }, [members, usageData]);\n\n  return (\n    <div className=\"chart-wrapper\">\n      <canvas ref={chartRef}></canvas>\n    </div>\n  );\n}\n\nexport default UsageBarChart;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,OAAOC,KAAK,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,SAASC,aAAaA,CAAC;EAAEC,OAAO;EAAEC;AAAU,CAAC,EAAE;EAAAC,EAAA;EAC7C,MAAMC,QAAQ,GAAGR,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMS,aAAa,GAAGT,MAAM,CAAC,IAAI,CAAC;EAElCD,SAAS,CAAC,MAAM;IACd,IAAI,CAACM,OAAO,IAAIA,OAAO,CAACK,MAAM,KAAK,CAAC,IAAI,CAACJ,SAAS,EAAE;;IAEpD;IACA,IAAIG,aAAa,CAACE,OAAO,EAAE;MACzBF,aAAa,CAACE,OAAO,CAACC,OAAO,CAAC,CAAC;IACjC;IAEA,MAAMC,GAAG,GAAGL,QAAQ,CAACG,OAAO,CAACG,UAAU,CAAC,IAAI,CAAC;;IAE7C;IACA,MAAMC,MAAM,GAAGV,OAAO,CAACW,GAAG,CAACC,MAAM,IAAIA,MAAM,CAACC,IAAI,IAAID,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACG,EAAE,CAAC;IAC9E,MAAMC,WAAW,GAAGhB,OAAO,CAACW,GAAG,CAACC,MAAM,IAAI;MAAA,IAAAK,mBAAA;MACxC,MAAMC,QAAQ,GAAGN,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACG,EAAE,IAAIH,MAAM,CAACO,MAAM;MAC3D,OAAO,EAAAF,mBAAA,GAAAhB,SAAS,CAACiB,QAAQ,CAAC,cAAAD,mBAAA,uBAAnBA,mBAAA,CAAqBG,YAAY,KAAI,CAAC;IAC/C,CAAC,CAAC;IACF,MAAMC,UAAU,GAAGrB,OAAO,CAACW,GAAG,CAACC,MAAM,IAAI;MACvC,MAAMM,QAAQ,GAAGN,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACG,EAAE,IAAIH,MAAM,CAACO,MAAM;MAC3D,MAAMG,KAAK,GAAGrB,SAAS,CAACiB,QAAQ,CAAC,IAAI,CAAC,CAAC;MACvC,OAAO,CAACI,KAAK,CAACC,eAAe,IAAI,CAAC,KAAKD,KAAK,CAACE,iBAAiB,IAAI,CAAC,CAAC;IACtE,CAAC,CAAC;;IAEF;IACApB,aAAa,CAACE,OAAO,GAAG,IAAIV,KAAK,CAACY,GAAG,EAAE;MACrCiB,IAAI,EAAE,KAAK;MACXC,IAAI,EAAE;QACJhB,MAAM,EAAEA,MAAM;QACdiB,QAAQ,EAAE,CACR;UACEC,KAAK,EAAE,MAAM;UACbF,IAAI,EAAEV,WAAW;UACjBa,eAAe,EAAE,yBAAyB;UAC1CC,WAAW,EAAE,mBAAmB;UAChCC,WAAW,EAAE,CAAC;UACdC,OAAO,EAAE;QACX,CAAC,EACD;UACEJ,KAAK,EAAE,MAAM;UACbF,IAAI,EAAEL,UAAU;UAChBQ,eAAe,EAAE,yBAAyB;UAC1CC,WAAW,EAAE,mBAAmB;UAChCC,WAAW,EAAE,CAAC;UACdC,OAAO,EAAE;QACX,CAAC;MAEL,CAAC;MACDC,OAAO,EAAE;QACPC,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;UACNC,CAAC,EAAE;YACDX,IAAI,EAAE,QAAQ;YACdY,OAAO,EAAE,IAAI;YACbC,QAAQ,EAAE,MAAM;YAChBC,KAAK,EAAE;cACLF,OAAO,EAAE,IAAI;cACbG,IAAI,EAAE;YACR,CAAC;YACDC,WAAW,EAAE;UACf,CAAC;UACDC,EAAE,EAAE;YACFjB,IAAI,EAAE,QAAQ;YACdY,OAAO,EAAE,IAAI;YACbC,QAAQ,EAAE,OAAO;YACjBC,KAAK,EAAE;cACLF,OAAO,EAAE,IAAI;cACbG,IAAI,EAAE;YACR,CAAC;YACDC,WAAW,EAAE,IAAI;YACjBE,IAAI,EAAE;cACJC,eAAe,EAAE;YACnB;UACF;QACF;MACF;IACF,CAAC,CAAC;IAEF,OAAO,MAAM;MACX,IAAIxC,aAAa,CAACE,OAAO,EAAE;QACzBF,aAAa,CAACE,OAAO,CAACC,OAAO,CAAC,CAAC;MACjC;IACF,CAAC;EACH,CAAC,EAAE,CAACP,OAAO,EAAEC,SAAS,CAAC,CAAC;EAExB,oBACEH,OAAA;IAAK+C,SAAS,EAAC,eAAe;IAAAC,QAAA,eAC5BhD,OAAA;MAAQiD,GAAG,EAAE5C;IAAS;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAS;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC7B,CAAC;AAEV;AAACjD,EAAA,CA5FQH,aAAa;AAAAqD,EAAA,GAAbrD,aAAa;AA8FtB,eAAeA,aAAa;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}