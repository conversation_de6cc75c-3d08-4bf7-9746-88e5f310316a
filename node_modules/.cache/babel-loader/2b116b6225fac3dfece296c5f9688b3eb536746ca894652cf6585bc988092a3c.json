{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Dashboard/MembersList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { getTeamMembers, getMembersUsage } from '../../services/api';\nimport UsageBarChart from '../Charts/UsageBarChart';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction MembersList({\n  onSelectMember\n}) {\n  _s();\n  const [members, setMembers] = useState([]);\n  const [usageData, setUsageData] = useState({});\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  useEffect(() => {\n    fetchData();\n  }, []);\n  const fetchData = async () => {\n    setLoading(true);\n    try {\n      console.log('正在获取成员数据...');\n\n      // 先只获取成员列表\n      const membersData = await getTeamMembers();\n      console.log('成员列表响应:', membersData);\n\n      // 设置成员数据 - 直接使用数组，不需要.members属性\n      const members = Array.isArray(membersData) ? membersData : membersData.members || [];\n      setMembers(members);\n\n      // 暂时跳过使用数据，先显示成员列表\n      console.log('成员数量:', members.length);\n\n      // 创建空的使用数据对象\n      const usageByMember = {};\n      members.forEach(member => {\n        usageByMember[member.id || member.userId || member.email] = {\n          totalPrompts: 0,\n          totalTokens: 0\n        };\n      });\n      setUsageData(usageByMember);\n    } catch (err) {\n      console.error('获取成员数据失败:', err);\n      setError(`获取成员数据失败: ${err.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (loading) return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"\\u52A0\\u8F7D\\u4E2D...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 23\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"error-message\",\n    children: error\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 21\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"members-list\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"\\u56E2\\u961F\\u6210\\u5458\\u4F7F\\u7528\\u60C5\\u51B5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(UsageBarChart, {\n      members: members,\n      usageData: usageData\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"members-table\",\n      children: /*#__PURE__*/_jsxDEV(\"table\", {\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u6210\\u5458\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u63D0\\u793A\\u6B21\\u6570\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u6D88\\u8017Token\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u64CD\\u4F5C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: members.map(member => {\n            var _usageData$member$use, _usageData$member$use2;\n            return /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: member.name || member.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: ((_usageData$member$use = usageData[member.userId]) === null || _usageData$member$use === void 0 ? void 0 : _usageData$member$use.totalPrompts) || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: ((_usageData$member$use2 = usageData[member.userId]) === null || _usageData$member$use2 === void 0 ? void 0 : _usageData$member$use2.totalTokens) || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => onSelectMember(member.userId),\n                  children: \"\\u67E5\\u770B\\u8BE6\\u60C5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 75,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 17\n              }, this)]\n            }, member.userId, true, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 15\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n}\n_s(MembersList, \"DDwlpJdin/LFqQxRgm8LT+3JIJs=\");\n_c = MembersList;\nexport default MembersList;\nvar _c;\n$RefreshReg$(_c, \"MembersList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "getTeamMembers", "getMembersUsage", "UsageBarChart", "jsxDEV", "_jsxDEV", "MembersList", "onSelectMember", "_s", "members", "setMembers", "usageData", "setUsageData", "loading", "setLoading", "error", "setError", "fetchData", "console", "log", "membersData", "Array", "isArray", "length", "usageByMember", "for<PERSON>ach", "member", "id", "userId", "email", "totalPrompts", "totalTokens", "err", "message", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "map", "_usageData$member$use", "_usageData$member$use2", "name", "onClick", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Dashboard/MembersList.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { getTeamMembers, getMembersUsage } from '../../services/api';\nimport UsageBarChart from '../Charts/UsageBarChart';\n\nfunction MembersList({ onSelectMember }) {\n  const [members, setMembers] = useState([]);\n  const [usageData, setUsageData] = useState({});\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n\n  useEffect(() => {\n    fetchData();\n  }, []);\n\n  const fetchData = async () => {\n    setLoading(true);\n    try {\n      console.log('正在获取成员数据...');\n\n      // 先只获取成员列表\n      const membersData = await getTeamMembers();\n      console.log('成员列表响应:', membersData);\n\n      // 设置成员数据 - 直接使用数组，不需要.members属性\n      const members = Array.isArray(membersData) ? membersData : (membersData.members || []);\n      setMembers(members);\n\n      // 暂时跳过使用数据，先显示成员列表\n      console.log('成员数量:', members.length);\n\n      // 创建空的使用数据对象\n      const usageByMember = {};\n      members.forEach(member => {\n        usageByMember[member.id || member.userId || member.email] = {\n          totalPrompts: 0,\n          totalTokens: 0\n        };\n      });\n\n      setUsageData(usageByMember);\n    } catch (err) {\n      console.error('获取成员数据失败:', err);\n      setError(`获取成员数据失败: ${err.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) return <div>加载中...</div>;\n  if (error) return <div className=\"error-message\">{error}</div>;\n\n  return (\n    <div className=\"members-list\">\n      <h2>团队成员使用情况</h2>\n      \n      <UsageBarChart members={members} usageData={usageData} />\n      \n      <div className=\"members-table\">\n        <table>\n          <thead>\n            <tr>\n              <th>成员</th>\n              <th>提示次数</th>\n              <th>消耗Token</th>\n              <th>操作</th>\n            </tr>\n          </thead>\n          <tbody>\n            {members.map(member => (\n              <tr key={member.userId}>\n                <td>{member.name || member.email}</td>\n                <td>{usageData[member.userId]?.totalPrompts || 0}</td>\n                <td>{usageData[member.userId]?.totalTokens || 0}</td>\n                <td>\n                  <button onClick={() => onSelectMember(member.userId)}>\n                    查看详情\n                  </button>\n                </td>\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      </div>\n    </div>\n  );\n}\n\nexport default MembersList;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,cAAc,EAAEC,eAAe,QAAQ,oBAAoB;AACpE,OAAOC,aAAa,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,SAASC,WAAWA,CAAC;EAAEC;AAAe,CAAC,EAAE;EAAAC,EAAA;EACvC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9C,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAEtCC,SAAS,CAAC,MAAM;IACdiB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5BH,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACFI,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;;MAE1B;MACA,MAAMC,WAAW,GAAG,MAAMnB,cAAc,CAAC,CAAC;MAC1CiB,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEC,WAAW,CAAC;;MAEnC;MACA,MAAMX,OAAO,GAAGY,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,GAAGA,WAAW,GAAIA,WAAW,CAACX,OAAO,IAAI,EAAG;MACtFC,UAAU,CAACD,OAAO,CAAC;;MAEnB;MACAS,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEV,OAAO,CAACc,MAAM,CAAC;;MAEpC;MACA,MAAMC,aAAa,GAAG,CAAC,CAAC;MACxBf,OAAO,CAACgB,OAAO,CAACC,MAAM,IAAI;QACxBF,aAAa,CAACE,MAAM,CAACC,EAAE,IAAID,MAAM,CAACE,MAAM,IAAIF,MAAM,CAACG,KAAK,CAAC,GAAG;UAC1DC,YAAY,EAAE,CAAC;UACfC,WAAW,EAAE;QACf,CAAC;MACH,CAAC,CAAC;MAEFnB,YAAY,CAACY,aAAa,CAAC;IAC7B,CAAC,CAAC,OAAOQ,GAAG,EAAE;MACZd,OAAO,CAACH,KAAK,CAAC,WAAW,EAAEiB,GAAG,CAAC;MAC/BhB,QAAQ,CAAC,aAAagB,GAAG,CAACC,OAAO,EAAE,CAAC;IACtC,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAID,OAAO,EAAE,oBAAOR,OAAA;IAAA6B,QAAA,EAAK;EAAM;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EACrC,IAAIvB,KAAK,EAAE,oBAAOV,OAAA;IAAKkC,SAAS,EAAC,eAAe;IAAAL,QAAA,EAAEnB;EAAK;IAAAoB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAE9D,oBACEjC,OAAA;IAAKkC,SAAS,EAAC,cAAc;IAAAL,QAAA,gBAC3B7B,OAAA;MAAA6B,QAAA,EAAI;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAEjBjC,OAAA,CAACF,aAAa;MAACM,OAAO,EAAEA,OAAQ;MAACE,SAAS,EAAEA;IAAU;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEzDjC,OAAA;MAAKkC,SAAS,EAAC,eAAe;MAAAL,QAAA,eAC5B7B,OAAA;QAAA6B,QAAA,gBACE7B,OAAA;UAAA6B,QAAA,eACE7B,OAAA;YAAA6B,QAAA,gBACE7B,OAAA;cAAA6B,QAAA,EAAI;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACXjC,OAAA;cAAA6B,QAAA,EAAI;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACbjC,OAAA;cAAA6B,QAAA,EAAI;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChBjC,OAAA;cAAA6B,QAAA,EAAI;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACRjC,OAAA;UAAA6B,QAAA,EACGzB,OAAO,CAAC+B,GAAG,CAACd,MAAM;YAAA,IAAAe,qBAAA,EAAAC,sBAAA;YAAA,oBACjBrC,OAAA;cAAA6B,QAAA,gBACE7B,OAAA;gBAAA6B,QAAA,EAAKR,MAAM,CAACiB,IAAI,IAAIjB,MAAM,CAACG;cAAK;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtCjC,OAAA;gBAAA6B,QAAA,EAAK,EAAAO,qBAAA,GAAA9B,SAAS,CAACe,MAAM,CAACE,MAAM,CAAC,cAAAa,qBAAA,uBAAxBA,qBAAA,CAA0BX,YAAY,KAAI;cAAC;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtDjC,OAAA;gBAAA6B,QAAA,EAAK,EAAAQ,sBAAA,GAAA/B,SAAS,CAACe,MAAM,CAACE,MAAM,CAAC,cAAAc,sBAAA,uBAAxBA,sBAAA,CAA0BX,WAAW,KAAI;cAAC;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrDjC,OAAA;gBAAA6B,QAAA,eACE7B,OAAA;kBAAQuC,OAAO,EAAEA,CAAA,KAAMrC,cAAc,CAACmB,MAAM,CAACE,MAAM,CAAE;kBAAAM,QAAA,EAAC;gBAEtD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA,GAREZ,MAAM,CAACE,MAAM;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASlB,CAAC;UAAA,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC9B,EAAA,CAjFQF,WAAW;AAAAuC,EAAA,GAAXvC,WAAW;AAmFpB,eAAeA,WAAW;AAAC,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}