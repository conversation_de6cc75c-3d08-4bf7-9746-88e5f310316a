{"ast": null, "code": "// 调试工具函数\n\n// 记录API请求\nexport const logApiRequest = (endpoint, options) => {\n  if (process.env.NODE_ENV !== 'production') {\n    console.group('API请求');\n    console.log('端点:', endpoint);\n    console.log('选项:', {\n      ...options,\n      headers: {\n        ...options.headers,\n        Authorization: '已隐藏'\n      }\n    });\n    console.groupEnd();\n  }\n};\n\n// 记录API响应\nexport const logApiResponse = (endpoint, response, data) => {\n  if (process.env.NODE_ENV !== 'production') {\n    console.group('API响应');\n    console.log('端点:', endpoint);\n    console.log('状态:', response.status);\n    console.log('数据:', data);\n    console.groupEnd();\n  }\n};\n\n// 记录API错误\nexport const logApiError = (endpoint, error) => {\n  if (process.env.NODE_ENV !== 'production') {\n    console.group('API错误');\n    console.log('端点:', endpoint);\n    console.error('错误:', error);\n    console.groupEnd();\n  }\n};", "map": {"version": 3, "names": ["logApiRequest", "endpoint", "options", "process", "env", "NODE_ENV", "console", "group", "log", "headers", "Authorization", "groupEnd", "logApiResponse", "response", "data", "status", "logApiError", "error"], "sources": ["/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/utils/debug.js"], "sourcesContent": ["// 调试工具函数\n\n// 记录API请求\nexport const logApiRequest = (endpoint, options) => {\n  if (process.env.NODE_ENV !== 'production') {\n    console.group('API请求');\n    console.log('端点:', endpoint);\n    console.log('选项:', {...options, headers: {...options.headers, Authorization: '已隐藏'}});\n    console.groupEnd();\n  }\n};\n\n// 记录API响应\nexport const logApiResponse = (endpoint, response, data) => {\n  if (process.env.NODE_ENV !== 'production') {\n    console.group('API响应');\n    console.log('端点:', endpoint);\n    console.log('状态:', response.status);\n    console.log('数据:', data);\n    console.groupEnd();\n  }\n};\n\n// 记录API错误\nexport const logApiError = (endpoint, error) => {\n  if (process.env.NODE_ENV !== 'production') {\n    console.group('API错误');\n    console.log('端点:', endpoint);\n    console.error('错误:', error);\n    console.groupEnd();\n  }\n};"], "mappings": "AAAA;;AAEA;AACA,OAAO,MAAMA,aAAa,GAAGA,CAACC,QAAQ,EAAEC,OAAO,KAAK;EAClD,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCC,OAAO,CAACC,KAAK,CAAC,OAAO,CAAC;IACtBD,OAAO,CAACE,GAAG,CAAC,KAAK,EAAEP,QAAQ,CAAC;IAC5BK,OAAO,CAACE,GAAG,CAAC,KAAK,EAAE;MAAC,GAAGN,OAAO;MAAEO,OAAO,EAAE;QAAC,GAAGP,OAAO,CAACO,OAAO;QAAEC,aAAa,EAAE;MAAK;IAAC,CAAC,CAAC;IACrFJ,OAAO,CAACK,QAAQ,CAAC,CAAC;EACpB;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,cAAc,GAAGA,CAACX,QAAQ,EAAEY,QAAQ,EAAEC,IAAI,KAAK;EAC1D,IAAIX,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCC,OAAO,CAACC,KAAK,CAAC,OAAO,CAAC;IACtBD,OAAO,CAACE,GAAG,CAAC,KAAK,EAAEP,QAAQ,CAAC;IAC5BK,OAAO,CAACE,GAAG,CAAC,KAAK,EAAEK,QAAQ,CAACE,MAAM,CAAC;IACnCT,OAAO,CAACE,GAAG,CAAC,KAAK,EAAEM,IAAI,CAAC;IACxBR,OAAO,CAACK,QAAQ,CAAC,CAAC;EACpB;AACF,CAAC;;AAED;AACA,OAAO,MAAMK,WAAW,GAAGA,CAACf,QAAQ,EAAEgB,KAAK,KAAK;EAC9C,IAAId,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCC,OAAO,CAACC,KAAK,CAAC,OAAO,CAAC;IACtBD,OAAO,CAACE,GAAG,CAAC,KAAK,EAAEP,QAAQ,CAAC;IAC5BK,OAAO,CAACW,KAAK,CAAC,KAAK,EAAEA,KAAK,CAAC;IAC3BX,OAAO,CAACK,QAAQ,CAAC,CAAC;EACpB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}