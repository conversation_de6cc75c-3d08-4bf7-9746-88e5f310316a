{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Charts/TrendLineChart.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport Chart from 'chart.js/auto';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction TrendLineChart({\n  data\n}) {\n  _s();\n  const chartRef = useRef(null);\n  const chartInstance = useRef(null);\n  useEffect(() => {\n    if (!data || data.length === 0) return;\n\n    // 销毁之前的图表实例\n    if (chartInstance.current) {\n      chartInstance.current.destroy();\n    }\n    const ctx = chartRef.current.getContext('2d');\n\n    // 准备数据\n    const dates = data.map(item => item.date);\n    const promptsData = data.map(item => item.prompts || 0);\n    const tokensData = data.map(item => item.tokens || 0);\n\n    // 创建新图表\n    chartInstance.current = new Chart(ctx, {\n      type: 'line',\n      data: {\n        labels: dates,\n        datasets: [{\n          label: '提示次数',\n          data: promptsData,\n          borderColor: 'rgb(75, 192, 192)',\n          backgroundColor: 'rgba(75, 192, 192, 0.2)',\n          tension: 0.1,\n          yAxisID: 'y'\n        }, {\n          label: '代码行数',\n          data: tokensData,\n          borderColor: 'rgb(255, 99, 132)',\n          backgroundColor: 'rgba(255, 99, 132, 0.2)',\n          tension: 0.1,\n          yAxisID: 'y1'\n        }]\n      },\n      options: {\n        responsive: true,\n        interaction: {\n          mode: 'index',\n          intersect: false\n        },\n        scales: {\n          y: {\n            type: 'linear',\n            display: true,\n            position: 'left',\n            title: {\n              display: true,\n              text: '提示次数'\n            }\n          },\n          y1: {\n            type: 'linear',\n            display: true,\n            position: 'right',\n            title: {\n              display: true,\n              text: '消耗Token'\n            },\n            grid: {\n              drawOnChartArea: false\n            }\n          }\n        }\n      }\n    });\n    return () => {\n      if (chartInstance.current) {\n        chartInstance.current.destroy();\n      }\n    };\n  }, [data]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"chart-wrapper\",\n    children: /*#__PURE__*/_jsxDEV(\"canvas\", {\n      ref: chartRef\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 87,\n    columnNumber: 5\n  }, this);\n}\n_s(TrendLineChart, \"u5+iHnwD4hjVcMuzTE/TbI78erc=\");\n_c = TrendLineChart;\nexport default TrendLineChart;\nvar _c;\n$RefreshReg$(_c, \"TrendLineChart\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "Chart", "jsxDEV", "_jsxDEV", "TrendLineChart", "data", "_s", "chartRef", "chartInstance", "length", "current", "destroy", "ctx", "getContext", "dates", "map", "item", "date", "promptsData", "prompts", "tokensData", "tokens", "type", "labels", "datasets", "label", "borderColor", "backgroundColor", "tension", "yAxisID", "options", "responsive", "interaction", "mode", "intersect", "scales", "y", "display", "position", "title", "text", "y1", "grid", "drawOnChartArea", "className", "children", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Charts/TrendLineChart.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\nimport Chart from 'chart.js/auto';\n\nfunction TrendLineChart({ data }) {\n  const chartRef = useRef(null);\n  const chartInstance = useRef(null);\n\n  useEffect(() => {\n    if (!data || data.length === 0) return;\n\n    // 销毁之前的图表实例\n    if (chartInstance.current) {\n      chartInstance.current.destroy();\n    }\n\n    const ctx = chartRef.current.getContext('2d');\n    \n    // 准备数据\n    const dates = data.map(item => item.date);\n    const promptsData = data.map(item => item.prompts || 0);\n    const tokensData = data.map(item => item.tokens || 0);\n\n    // 创建新图表\n    chartInstance.current = new Chart(ctx, {\n      type: 'line',\n      data: {\n        labels: dates,\n        datasets: [\n          {\n            label: '提示次数',\n            data: promptsData,\n            borderColor: 'rgb(75, 192, 192)',\n            backgroundColor: 'rgba(75, 192, 192, 0.2)',\n            tension: 0.1,\n            yAxisID: 'y'\n          },\n          {\n            label: '代码行数',\n            data: tokensData,\n            borderColor: 'rgb(255, 99, 132)',\n            backgroundColor: 'rgba(255, 99, 132, 0.2)',\n            tension: 0.1,\n            yAxisID: 'y1'\n          }\n        ]\n      },\n      options: {\n        responsive: true,\n        interaction: {\n          mode: 'index',\n          intersect: false,\n        },\n        scales: {\n          y: {\n            type: 'linear',\n            display: true,\n            position: 'left',\n            title: {\n              display: true,\n              text: '提示次数'\n            }\n          },\n          y1: {\n            type: 'linear',\n            display: true,\n            position: 'right',\n            title: {\n              display: true,\n              text: '消耗Token'\n            },\n            grid: {\n              drawOnChartArea: false,\n            },\n          }\n        }\n      }\n    });\n\n    return () => {\n      if (chartInstance.current) {\n        chartInstance.current.destroy();\n      }\n    };\n  }, [data]);\n\n  return (\n    <div className=\"chart-wrapper\">\n      <canvas ref={chartRef}></canvas>\n    </div>\n  );\n}\n\nexport default TrendLineChart;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,OAAOC,KAAK,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,SAASC,cAAcA,CAAC;EAAEC;AAAK,CAAC,EAAE;EAAAC,EAAA;EAChC,MAAMC,QAAQ,GAAGP,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMQ,aAAa,GAAGR,MAAM,CAAC,IAAI,CAAC;EAElCD,SAAS,CAAC,MAAM;IACd,IAAI,CAACM,IAAI,IAAIA,IAAI,CAACI,MAAM,KAAK,CAAC,EAAE;;IAEhC;IACA,IAAID,aAAa,CAACE,OAAO,EAAE;MACzBF,aAAa,CAACE,OAAO,CAACC,OAAO,CAAC,CAAC;IACjC;IAEA,MAAMC,GAAG,GAAGL,QAAQ,CAACG,OAAO,CAACG,UAAU,CAAC,IAAI,CAAC;;IAE7C;IACA,MAAMC,KAAK,GAAGT,IAAI,CAACU,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC;IACzC,MAAMC,WAAW,GAAGb,IAAI,CAACU,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACG,OAAO,IAAI,CAAC,CAAC;IACvD,MAAMC,UAAU,GAAGf,IAAI,CAACU,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACK,MAAM,IAAI,CAAC,CAAC;;IAErD;IACAb,aAAa,CAACE,OAAO,GAAG,IAAIT,KAAK,CAACW,GAAG,EAAE;MACrCU,IAAI,EAAE,MAAM;MACZjB,IAAI,EAAE;QACJkB,MAAM,EAAET,KAAK;QACbU,QAAQ,EAAE,CACR;UACEC,KAAK,EAAE,MAAM;UACbpB,IAAI,EAAEa,WAAW;UACjBQ,WAAW,EAAE,mBAAmB;UAChCC,eAAe,EAAE,yBAAyB;UAC1CC,OAAO,EAAE,GAAG;UACZC,OAAO,EAAE;QACX,CAAC,EACD;UACEJ,KAAK,EAAE,MAAM;UACbpB,IAAI,EAAEe,UAAU;UAChBM,WAAW,EAAE,mBAAmB;UAChCC,eAAe,EAAE,yBAAyB;UAC1CC,OAAO,EAAE,GAAG;UACZC,OAAO,EAAE;QACX,CAAC;MAEL,CAAC;MACDC,OAAO,EAAE;QACPC,UAAU,EAAE,IAAI;QAChBC,WAAW,EAAE;UACXC,IAAI,EAAE,OAAO;UACbC,SAAS,EAAE;QACb,CAAC;QACDC,MAAM,EAAE;UACNC,CAAC,EAAE;YACDd,IAAI,EAAE,QAAQ;YACde,OAAO,EAAE,IAAI;YACbC,QAAQ,EAAE,MAAM;YAChBC,KAAK,EAAE;cACLF,OAAO,EAAE,IAAI;cACbG,IAAI,EAAE;YACR;UACF,CAAC;UACDC,EAAE,EAAE;YACFnB,IAAI,EAAE,QAAQ;YACde,OAAO,EAAE,IAAI;YACbC,QAAQ,EAAE,OAAO;YACjBC,KAAK,EAAE;cACLF,OAAO,EAAE,IAAI;cACbG,IAAI,EAAE;YACR,CAAC;YACDE,IAAI,EAAE;cACJC,eAAe,EAAE;YACnB;UACF;QACF;MACF;IACF,CAAC,CAAC;IAEF,OAAO,MAAM;MACX,IAAInC,aAAa,CAACE,OAAO,EAAE;QACzBF,aAAa,CAACE,OAAO,CAACC,OAAO,CAAC,CAAC;MACjC;IACF,CAAC;EACH,CAAC,EAAE,CAACN,IAAI,CAAC,CAAC;EAEV,oBACEF,OAAA;IAAKyC,SAAS,EAAC,eAAe;IAAAC,QAAA,eAC5B1C,OAAA;MAAQ2C,GAAG,EAAEvC;IAAS;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAS;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC7B,CAAC;AAEV;AAAC5C,EAAA,CAvFQF,cAAc;AAAA+C,EAAA,GAAd/C,cAAc;AAyFvB,eAAeA,cAAc;AAAC,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}