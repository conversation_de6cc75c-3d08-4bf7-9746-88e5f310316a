{"ast": null, "code": "import { logApiRequest, logApiResponse, logApiError } from '../utils/debug';\nimport { API_CONFIG, DEBUG_CONFIG } from '../config';\n\n// 从本地存储获取API Key\nconst getApiKey = () => localStorage.getItem('cursor_api_key');\n\n// 创建Basic Auth凭证\nconst createBasicAuth = apiKey => {\n  // 根据文档，用户名是API Key，密码为空\n  return 'Basic ' + btoa(`${apiKey}:`);\n};\n\n// API请求通用方法\nconst fetchWithAuth = async (endpoint, method = 'GET', body = null) => {\n  const apiKey = getApiKey();\n  if (!apiKey) throw new Error('API Key未设置');\n  const headers = {\n    'Content-Type': 'application/json',\n    'Authorization': createBasicAuth(apiKey)\n  };\n  const options = {\n    method,\n    headers,\n    // 添加跨域支持\n    mode: 'cors',\n    credentials: 'same-origin'\n  };\n\n  // 如果是POST/PUT请求并且有body，添加请求体\n  if ((method === 'POST' || method === 'PUT') && body) {\n    options.body = JSON.stringify(body);\n  }\n  const url = `${API_CONFIG.BASE_URL}${endpoint}`;\n  if (DEBUG_CONFIG.LOG_API_CALLS) {\n    logApiRequest(url, options);\n  }\n  try {\n    const response = await fetch(url, options);\n\n    // 尝试解析响应内容\n    let responseData;\n    const contentType = response.headers.get('content-type');\n    if (contentType && contentType.includes('application/json')) {\n      responseData = await response.json();\n    } else {\n      responseData = await response.text();\n    }\n    if (DEBUG_CONFIG.LOG_API_CALLS) {\n      logApiResponse(url, response, responseData);\n    }\n    if (!response.ok) {\n      const errorMessage = typeof responseData === 'object' && responseData.message ? responseData.message : `API请求失败: ${response.status}`;\n      throw new Error(errorMessage);\n    }\n    return responseData;\n  } catch (error) {\n    if (DEBUG_CONFIG.LOG_API_CALLS) {\n      logApiError(url, error);\n    }\n    throw error;\n  }\n};\n\n// 测试API Key是否有效\nexport const testApiKey = async apiKey => {\n  // 临时保存API Key用于测试\n  localStorage.setItem('cursor_api_key', apiKey);\n\n  // 尝试所有可能的基础URL\n  const baseUrls = [API_CONFIG.BASE_URL, ...API_CONFIG.ALTERNATE_BASE_URLS];\n  for (const baseUrl of baseUrls) {\n    try {\n      // 临时覆盖基础URL\n      const originalBaseUrl = API_CONFIG.BASE_URL;\n      API_CONFIG.BASE_URL = baseUrl;\n      console.log(`尝试基础URL: ${baseUrl}`);\n\n      // 尝试获取团队成员列表来验证API Key\n      const result = await getTeamMembers();\n\n      // 如果成功，保持这个基础URL\n      console.log(`成功连接到: ${baseUrl}`);\n      return {\n        valid: true,\n        data: result\n      };\n    } catch (error) {\n      console.error(`使用 ${baseUrl} 验证失败:`, error);\n      // 继续尝试下一个URL\n    }\n  }\n\n  // 所有URL都失败了\n  localStorage.removeItem('cursor_api_key');\n  return {\n    valid: false,\n    error: '无法连接到Cursor API。请检查API密钥和网络连接。'\n  };\n};\n\n// 获取团队成员列表\nexport const getTeamMembers = () => {\n  return fetchWithAuth(API_CONFIG.ENDPOINTS.TEAM_MEMBERS);\n};\n\n// 获取团队使用统计\nexport const getTeamUsage = (startDate, endDate) => {\n  // 将日期转换为毫秒时间戳\n  const start = new Date(startDate).getTime();\n  const end = new Date(endDate).getTime();\n  return fetchWithAuth(API_CONFIG.ENDPOINTS.TEAM_USAGE, 'POST', {\n    startDate: start,\n    endDate: end\n  });\n};\n\n// 获取团队支出数据\nexport const getTeamSpend = (searchTerm = '', page = 1, pageSize = 10) => {\n  return fetchWithAuth(API_CONFIG.ENDPOINTS.TEAM_SPEND, 'POST', {\n    searchTerm,\n    page,\n    pageSize\n  });\n};\n\n// 获取特定成员使用情况 (通过筛选团队使用数据)\nexport const getMemberUsage = async (userId, startDate, endDate) => {\n  var _teamData$memberUsage;\n  // 获取团队使用数据\n  const teamData = await getTeamUsage(startDate, endDate);\n\n  // 获取成员列表\n  const membersData = await getTeamMembers();\n\n  // 找到指定成员\n  const member = membersData.find(m => m.email === userId || m.id === userId);\n  if (!member) {\n    throw new Error('找不到指定成员');\n  }\n\n  // 筛选该成员的使用数据\n  // 注意：这里的实现取决于API返回的数据结构\n  // 可能需要根据实际返回的数据结构进行调整\n  const memberData = {\n    ...member,\n    // 假设API返回的数据中包含成员使用情况\n    usage: ((_teamData$memberUsage = teamData.memberUsage) === null || _teamData$memberUsage === void 0 ? void 0 : _teamData$memberUsage.find(u => u.email === member.email || u.id === member.id)) || {}\n  };\n  return memberData;\n};\n\n// 获取所有成员使用情况\nexport const getMembersUsage = async (startDate, endDate) => {\n  // 获取团队使用数据\n  const teamData = await getTeamUsage(startDate, endDate);\n\n  // 获取成员列表\n  const membersData = await getTeamMembers();\n\n  // 合并成员信息和使用数据\n  // 注意：这里的实现取决于API返回的数据结构\n  // 可能需要根据实际返回的数据结构进行调整\n  const membersWithUsage = membersData.map(member => {\n    var _teamData$memberUsage2;\n    return {\n      ...member,\n      // 假设API返回的数据中包含成员使用情况\n      usage: ((_teamData$memberUsage2 = teamData.memberUsage) === null || _teamData$memberUsage2 === void 0 ? void 0 : _teamData$memberUsage2.find(u => u.email === member.email || u.id === member.id)) || {}\n    };\n  });\n  return {\n    members: membersWithUsage\n  };\n};", "map": {"version": 3, "names": ["logApiRequest", "logApiResponse", "logApiError", "API_CONFIG", "DEBUG_CONFIG", "getApi<PERSON>ey", "localStorage", "getItem", "createBasicAuth", "<PERSON><PERSON><PERSON><PERSON>", "btoa", "fetchWithAuth", "endpoint", "method", "body", "Error", "headers", "options", "mode", "credentials", "JSON", "stringify", "url", "BASE_URL", "LOG_API_CALLS", "response", "fetch", "responseData", "contentType", "get", "includes", "json", "text", "ok", "errorMessage", "message", "status", "error", "test<PERSON>pi<PERSON>ey", "setItem", "baseUrls", "ALTERNATE_BASE_URLS", "baseUrl", "originalBaseUrl", "console", "log", "result", "getTeamMembers", "valid", "data", "removeItem", "ENDPOINTS", "TEAM_MEMBERS", "getTeamUsage", "startDate", "endDate", "start", "Date", "getTime", "end", "TEAM_USAGE", "getTeamSpend", "searchTerm", "page", "pageSize", "TEAM_SPEND", "getMemberUsage", "userId", "_teamData$memberUsage", "teamData", "membersData", "member", "find", "m", "email", "id", "memberData", "usage", "memberUsage", "u", "getMembersUsage", "membersWithUsage", "map", "_teamData$memberUsage2", "members"], "sources": ["/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/services/api.js"], "sourcesContent": ["import { logApiRequest, logApiResponse, logApiError } from '../utils/debug';\nimport { API_CONFIG, DEBUG_CONFIG } from '../config';\n\n// 从本地存储获取API Key\nconst getApiKey = () => localStorage.getItem('cursor_api_key');\n\n// 创建Basic Auth凭证\nconst createBasicAuth = (apiKey) => {\n  // 根据文档，用户名是API Key，密码为空\n  return 'Basic ' + btoa(`${apiKey}:`);\n};\n\n// API请求通用方法\nconst fetchWithAuth = async (endpoint, method = 'GET', body = null) => {\n  const apiKey = getApiKey();\n  \n  if (!apiKey) throw new Error('API Key未设置');\n  \n  const headers = {\n    'Content-Type': 'application/json',\n    'Authorization': createBasicAuth(apiKey)\n  };\n  \n  const options = {\n    method,\n    headers,\n    // 添加跨域支持\n    mode: 'cors',\n    credentials: 'same-origin'\n  };\n  \n  // 如果是POST/PUT请求并且有body，添加请求体\n  if ((method === 'POST' || method === 'PUT') && body) {\n    options.body = JSON.stringify(body);\n  }\n  \n  const url = `${API_CONFIG.BASE_URL}${endpoint}`;\n  \n  if (DEBUG_CONFIG.LOG_API_CALLS) {\n    logApiRequest(url, options);\n  }\n  \n  try {\n    const response = await fetch(url, options);\n    \n    // 尝试解析响应内容\n    let responseData;\n    const contentType = response.headers.get('content-type');\n    if (contentType && contentType.includes('application/json')) {\n      responseData = await response.json();\n    } else {\n      responseData = await response.text();\n    }\n    \n    if (DEBUG_CONFIG.LOG_API_CALLS) {\n      logApiResponse(url, response, responseData);\n    }\n    \n    if (!response.ok) {\n      const errorMessage = typeof responseData === 'object' && responseData.message \n        ? responseData.message \n        : `API请求失败: ${response.status}`;\n      throw new Error(errorMessage);\n    }\n    \n    return responseData;\n  } catch (error) {\n    if (DEBUG_CONFIG.LOG_API_CALLS) {\n      logApiError(url, error);\n    }\n    throw error;\n  }\n};\n\n// 测试API Key是否有效\nexport const testApiKey = async (apiKey) => {\n  // 临时保存API Key用于测试\n  localStorage.setItem('cursor_api_key', apiKey);\n  \n  // 尝试所有可能的基础URL\n  const baseUrls = [API_CONFIG.BASE_URL, ...API_CONFIG.ALTERNATE_BASE_URLS];\n  \n  for (const baseUrl of baseUrls) {\n    try {\n      // 临时覆盖基础URL\n      const originalBaseUrl = API_CONFIG.BASE_URL;\n      API_CONFIG.BASE_URL = baseUrl;\n      \n      console.log(`尝试基础URL: ${baseUrl}`);\n      \n      // 尝试获取团队成员列表来验证API Key\n      const result = await getTeamMembers();\n      \n      // 如果成功，保持这个基础URL\n      console.log(`成功连接到: ${baseUrl}`);\n      return { valid: true, data: result };\n    } catch (error) {\n      console.error(`使用 ${baseUrl} 验证失败:`, error);\n      // 继续尝试下一个URL\n    }\n  }\n  \n  // 所有URL都失败了\n  localStorage.removeItem('cursor_api_key');\n  return { \n    valid: false, \n    error: '无法连接到Cursor API。请检查API密钥和网络连接。' \n  };\n};\n\n// 获取团队成员列表\nexport const getTeamMembers = () => {\n  return fetchWithAuth(API_CONFIG.ENDPOINTS.TEAM_MEMBERS);\n};\n\n// 获取团队使用统计\nexport const getTeamUsage = (startDate, endDate) => {\n  // 将日期转换为毫秒时间戳\n  const start = new Date(startDate).getTime();\n  const end = new Date(endDate).getTime();\n  \n  return fetchWithAuth(\n    API_CONFIG.ENDPOINTS.TEAM_USAGE, \n    'POST', \n    { startDate: start, endDate: end }\n  );\n};\n\n// 获取团队支出数据\nexport const getTeamSpend = (searchTerm = '', page = 1, pageSize = 10) => {\n  return fetchWithAuth(\n    API_CONFIG.ENDPOINTS.TEAM_SPEND,\n    'POST',\n    { searchTerm, page, pageSize }\n  );\n};\n\n// 获取特定成员使用情况 (通过筛选团队使用数据)\nexport const getMemberUsage = async (userId, startDate, endDate) => {\n  // 获取团队使用数据\n  const teamData = await getTeamUsage(startDate, endDate);\n  \n  // 获取成员列表\n  const membersData = await getTeamMembers();\n  \n  // 找到指定成员\n  const member = membersData.find(m => m.email === userId || m.id === userId);\n  \n  if (!member) {\n    throw new Error('找不到指定成员');\n  }\n  \n  // 筛选该成员的使用数据\n  // 注意：这里的实现取决于API返回的数据结构\n  // 可能需要根据实际返回的数据结构进行调整\n  const memberData = {\n    ...member,\n    // 假设API返回的数据中包含成员使用情况\n    usage: teamData.memberUsage?.find(u => u.email === member.email || u.id === member.id) || {}\n  };\n  \n  return memberData;\n};\n\n// 获取所有成员使用情况\nexport const getMembersUsage = async (startDate, endDate) => {\n  // 获取团队使用数据\n  const teamData = await getTeamUsage(startDate, endDate);\n  \n  // 获取成员列表\n  const membersData = await getTeamMembers();\n  \n  // 合并成员信息和使用数据\n  // 注意：这里的实现取决于API返回的数据结构\n  // 可能需要根据实际返回的数据结构进行调整\n  const membersWithUsage = membersData.map(member => {\n    return {\n      ...member,\n      // 假设API返回的数据中包含成员使用情况\n      usage: teamData.memberUsage?.find(u => u.email === member.email || u.id === member.id) || {}\n    };\n  });\n  \n  return { members: membersWithUsage };\n};\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,cAAc,EAAEC,WAAW,QAAQ,gBAAgB;AAC3E,SAASC,UAAU,EAAEC,YAAY,QAAQ,WAAW;;AAEpD;AACA,MAAMC,SAAS,GAAGA,CAAA,KAAMC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;;AAE9D;AACA,MAAMC,eAAe,GAAIC,MAAM,IAAK;EAClC;EACA,OAAO,QAAQ,GAAGC,IAAI,CAAC,GAAGD,MAAM,GAAG,CAAC;AACtC,CAAC;;AAED;AACA,MAAME,aAAa,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,MAAM,GAAG,KAAK,EAAEC,IAAI,GAAG,IAAI,KAAK;EACrE,MAAML,MAAM,GAAGJ,SAAS,CAAC,CAAC;EAE1B,IAAI,CAACI,MAAM,EAAE,MAAM,IAAIM,KAAK,CAAC,YAAY,CAAC;EAE1C,MAAMC,OAAO,GAAG;IACd,cAAc,EAAE,kBAAkB;IAClC,eAAe,EAAER,eAAe,CAACC,MAAM;EACzC,CAAC;EAED,MAAMQ,OAAO,GAAG;IACdJ,MAAM;IACNG,OAAO;IACP;IACAE,IAAI,EAAE,MAAM;IACZC,WAAW,EAAE;EACf,CAAC;;EAED;EACA,IAAI,CAACN,MAAM,KAAK,MAAM,IAAIA,MAAM,KAAK,KAAK,KAAKC,IAAI,EAAE;IACnDG,OAAO,CAACH,IAAI,GAAGM,IAAI,CAACC,SAAS,CAACP,IAAI,CAAC;EACrC;EAEA,MAAMQ,GAAG,GAAG,GAAGnB,UAAU,CAACoB,QAAQ,GAAGX,QAAQ,EAAE;EAE/C,IAAIR,YAAY,CAACoB,aAAa,EAAE;IAC9BxB,aAAa,CAACsB,GAAG,EAAEL,OAAO,CAAC;EAC7B;EAEA,IAAI;IACF,MAAMQ,QAAQ,GAAG,MAAMC,KAAK,CAACJ,GAAG,EAAEL,OAAO,CAAC;;IAE1C;IACA,IAAIU,YAAY;IAChB,MAAMC,WAAW,GAAGH,QAAQ,CAACT,OAAO,CAACa,GAAG,CAAC,cAAc,CAAC;IACxD,IAAID,WAAW,IAAIA,WAAW,CAACE,QAAQ,CAAC,kBAAkB,CAAC,EAAE;MAC3DH,YAAY,GAAG,MAAMF,QAAQ,CAACM,IAAI,CAAC,CAAC;IACtC,CAAC,MAAM;MACLJ,YAAY,GAAG,MAAMF,QAAQ,CAACO,IAAI,CAAC,CAAC;IACtC;IAEA,IAAI5B,YAAY,CAACoB,aAAa,EAAE;MAC9BvB,cAAc,CAACqB,GAAG,EAAEG,QAAQ,EAAEE,YAAY,CAAC;IAC7C;IAEA,IAAI,CAACF,QAAQ,CAACQ,EAAE,EAAE;MAChB,MAAMC,YAAY,GAAG,OAAOP,YAAY,KAAK,QAAQ,IAAIA,YAAY,CAACQ,OAAO,GACzER,YAAY,CAACQ,OAAO,GACpB,YAAYV,QAAQ,CAACW,MAAM,EAAE;MACjC,MAAM,IAAIrB,KAAK,CAACmB,YAAY,CAAC;IAC/B;IAEA,OAAOP,YAAY;EACrB,CAAC,CAAC,OAAOU,KAAK,EAAE;IACd,IAAIjC,YAAY,CAACoB,aAAa,EAAE;MAC9BtB,WAAW,CAACoB,GAAG,EAAEe,KAAK,CAAC;IACzB;IACA,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,UAAU,GAAG,MAAO7B,MAAM,IAAK;EAC1C;EACAH,YAAY,CAACiC,OAAO,CAAC,gBAAgB,EAAE9B,MAAM,CAAC;;EAE9C;EACA,MAAM+B,QAAQ,GAAG,CAACrC,UAAU,CAACoB,QAAQ,EAAE,GAAGpB,UAAU,CAACsC,mBAAmB,CAAC;EAEzE,KAAK,MAAMC,OAAO,IAAIF,QAAQ,EAAE;IAC9B,IAAI;MACF;MACA,MAAMG,eAAe,GAAGxC,UAAU,CAACoB,QAAQ;MAC3CpB,UAAU,CAACoB,QAAQ,GAAGmB,OAAO;MAE7BE,OAAO,CAACC,GAAG,CAAC,YAAYH,OAAO,EAAE,CAAC;;MAElC;MACA,MAAMI,MAAM,GAAG,MAAMC,cAAc,CAAC,CAAC;;MAErC;MACAH,OAAO,CAACC,GAAG,CAAC,UAAUH,OAAO,EAAE,CAAC;MAChC,OAAO;QAAEM,KAAK,EAAE,IAAI;QAAEC,IAAI,EAAEH;MAAO,CAAC;IACtC,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdO,OAAO,CAACP,KAAK,CAAC,MAAMK,OAAO,QAAQ,EAAEL,KAAK,CAAC;MAC3C;IACF;EACF;;EAEA;EACA/B,YAAY,CAAC4C,UAAU,CAAC,gBAAgB,CAAC;EACzC,OAAO;IACLF,KAAK,EAAE,KAAK;IACZX,KAAK,EAAE;EACT,CAAC;AACH,CAAC;;AAED;AACA,OAAO,MAAMU,cAAc,GAAGA,CAAA,KAAM;EAClC,OAAOpC,aAAa,CAACR,UAAU,CAACgD,SAAS,CAACC,YAAY,CAAC;AACzD,CAAC;;AAED;AACA,OAAO,MAAMC,YAAY,GAAGA,CAACC,SAAS,EAAEC,OAAO,KAAK;EAClD;EACA,MAAMC,KAAK,GAAG,IAAIC,IAAI,CAACH,SAAS,CAAC,CAACI,OAAO,CAAC,CAAC;EAC3C,MAAMC,GAAG,GAAG,IAAIF,IAAI,CAACF,OAAO,CAAC,CAACG,OAAO,CAAC,CAAC;EAEvC,OAAO/C,aAAa,CAClBR,UAAU,CAACgD,SAAS,CAACS,UAAU,EAC/B,MAAM,EACN;IAAEN,SAAS,EAAEE,KAAK;IAAED,OAAO,EAAEI;EAAI,CACnC,CAAC;AACH,CAAC;;AAED;AACA,OAAO,MAAME,YAAY,GAAGA,CAACC,UAAU,GAAG,EAAE,EAAEC,IAAI,GAAG,CAAC,EAAEC,QAAQ,GAAG,EAAE,KAAK;EACxE,OAAOrD,aAAa,CAClBR,UAAU,CAACgD,SAAS,CAACc,UAAU,EAC/B,MAAM,EACN;IAAEH,UAAU;IAAEC,IAAI;IAAEC;EAAS,CAC/B,CAAC;AACH,CAAC;;AAED;AACA,OAAO,MAAME,cAAc,GAAG,MAAAA,CAAOC,MAAM,EAAEb,SAAS,EAAEC,OAAO,KAAK;EAAA,IAAAa,qBAAA;EAClE;EACA,MAAMC,QAAQ,GAAG,MAAMhB,YAAY,CAACC,SAAS,EAAEC,OAAO,CAAC;;EAEvD;EACA,MAAMe,WAAW,GAAG,MAAMvB,cAAc,CAAC,CAAC;;EAE1C;EACA,MAAMwB,MAAM,GAAGD,WAAW,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,KAAK,KAAKP,MAAM,IAAIM,CAAC,CAACE,EAAE,KAAKR,MAAM,CAAC;EAE3E,IAAI,CAACI,MAAM,EAAE;IACX,MAAM,IAAIxD,KAAK,CAAC,SAAS,CAAC;EAC5B;;EAEA;EACA;EACA;EACA,MAAM6D,UAAU,GAAG;IACjB,GAAGL,MAAM;IACT;IACAM,KAAK,EAAE,EAAAT,qBAAA,GAAAC,QAAQ,CAACS,WAAW,cAAAV,qBAAA,uBAApBA,qBAAA,CAAsBI,IAAI,CAACO,CAAC,IAAIA,CAAC,CAACL,KAAK,KAAKH,MAAM,CAACG,KAAK,IAAIK,CAAC,CAACJ,EAAE,KAAKJ,MAAM,CAACI,EAAE,CAAC,KAAI,CAAC;EAC7F,CAAC;EAED,OAAOC,UAAU;AACnB,CAAC;;AAED;AACA,OAAO,MAAMI,eAAe,GAAG,MAAAA,CAAO1B,SAAS,EAAEC,OAAO,KAAK;EAC3D;EACA,MAAMc,QAAQ,GAAG,MAAMhB,YAAY,CAACC,SAAS,EAAEC,OAAO,CAAC;;EAEvD;EACA,MAAMe,WAAW,GAAG,MAAMvB,cAAc,CAAC,CAAC;;EAE1C;EACA;EACA;EACA,MAAMkC,gBAAgB,GAAGX,WAAW,CAACY,GAAG,CAACX,MAAM,IAAI;IAAA,IAAAY,sBAAA;IACjD,OAAO;MACL,GAAGZ,MAAM;MACT;MACAM,KAAK,EAAE,EAAAM,sBAAA,GAAAd,QAAQ,CAACS,WAAW,cAAAK,sBAAA,uBAApBA,sBAAA,CAAsBX,IAAI,CAACO,CAAC,IAAIA,CAAC,CAACL,KAAK,KAAKH,MAAM,CAACG,KAAK,IAAIK,CAAC,CAACJ,EAAE,KAAKJ,MAAM,CAACI,EAAE,CAAC,KAAI,CAAC;IAC7F,CAAC;EACH,CAAC,CAAC;EAEF,OAAO;IAAES,OAAO,EAAEH;EAAiB,CAAC;AACtC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}