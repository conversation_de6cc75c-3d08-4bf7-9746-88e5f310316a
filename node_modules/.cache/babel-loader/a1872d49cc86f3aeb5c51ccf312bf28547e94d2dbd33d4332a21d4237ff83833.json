{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Dashboard/MembersList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { getTeamMembers, getMembersUsage } from '../../services/api';\nimport UsageBarChart from '../Charts/UsageBarChart';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction MembersList({\n  onSelectMember\n}) {\n  _s();\n  const [members, setMembers] = useState([]);\n  const [usageData, setUsageData] = useState({});\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  useEffect(() => {\n    fetchData();\n  }, []);\n  const fetchData = async () => {\n    setLoading(true);\n    try {\n      console.log('正在获取成员数据...');\n\n      // 获取成员列表\n      const membersData = await getTeamMembers();\n      console.log('成员列表响应:', membersData);\n\n      // 设置成员数据 - 直接使用数组，不需要.members属性\n      const members = Array.isArray(membersData) ? membersData : membersData.members || [];\n      setMembers(members);\n      console.log('成员数量:', members.length);\n\n      // 获取使用数据 - 使用最近30天的数据\n      const endDate = new Date().toISOString().split('T')[0];\n      const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];\n      try {\n        const usageResponse = await getMembersUsage(startDate, endDate);\n        console.log('成员使用数据响应:', usageResponse);\n\n        // 将使用数据转换为以email为键的对象\n        const usageByMember = {};\n        (usageResponse.members || []).forEach(member => {\n          const memberId = member.email || member.id || member.userId;\n          usageByMember[memberId] = member.usage || {\n            totalPrompts: 0,\n            totalTokens: 0,\n            totalLinesAdded: 0,\n            totalLinesDeleted: 0\n          };\n        });\n        setUsageData(usageByMember);\n      } catch (usageErr) {\n        console.warn('获取使用数据失败，使用空数据:', usageErr);\n        // 如果获取使用数据失败，创建空的使用数据对象\n        const usageByMember = {};\n        members.forEach(member => {\n          usageByMember[member.email || member.id || member.userId] = {\n            totalPrompts: 0,\n            totalTokens: 0,\n            totalLinesAdded: 0,\n            totalLinesDeleted: 0\n          };\n        });\n        setUsageData(usageByMember);\n      }\n    } catch (err) {\n      console.error('获取成员数据失败:', err);\n      setError(`获取成员数据失败: ${err.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (loading) return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"\\u52A0\\u8F7D\\u4E2D...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 73,\n    columnNumber: 23\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"error-message\",\n    children: error\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 74,\n    columnNumber: 21\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"members-list\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"\\u56E2\\u961F\\u6210\\u5458\\u4F7F\\u7528\\u60C5\\u51B5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(UsageBarChart, {\n      members: members,\n      usageData: usageData\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"members-table\",\n      children: /*#__PURE__*/_jsxDEV(\"table\", {\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u6210\\u5458\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u63D0\\u793A\\u6B21\\u6570\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u6D88\\u8017Token\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"\\u64CD\\u4F5C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: members.map(member => {\n            const memberId = member.email || member.id || member.userId;\n            const usage = usageData[memberId] || {};\n            return /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: member.name || member.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: usage.totalPrompts || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: (usage.totalLinesAdded || 0) + (usage.totalLinesDeleted || 0)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => onSelectMember(memberId),\n                  children: \"\\u67E5\\u770B\\u8BE6\\u60C5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 102,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 19\n              }, this)]\n            }, memberId, true, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 5\n  }, this);\n}\n_s(MembersList, \"DDwlpJdin/LFqQxRgm8LT+3JIJs=\");\n_c = MembersList;\nexport default MembersList;\nvar _c;\n$RefreshReg$(_c, \"MembersList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "getTeamMembers", "getMembersUsage", "UsageBarChart", "jsxDEV", "_jsxDEV", "MembersList", "onSelectMember", "_s", "members", "setMembers", "usageData", "setUsageData", "loading", "setLoading", "error", "setError", "fetchData", "console", "log", "membersData", "Array", "isArray", "length", "endDate", "Date", "toISOString", "split", "startDate", "now", "usageResponse", "usageByMember", "for<PERSON>ach", "member", "memberId", "email", "id", "userId", "usage", "totalPrompts", "totalTokens", "totalLinesAdded", "totalLinesDeleted", "usageErr", "warn", "err", "message", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "map", "name", "onClick", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Dashboard/MembersList.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { getTeamMembers, getMembersUsage } from '../../services/api';\nimport UsageBarChart from '../Charts/UsageBarChart';\n\nfunction MembersList({ onSelectMember }) {\n  const [members, setMembers] = useState([]);\n  const [usageData, setUsageData] = useState({});\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n\n  useEffect(() => {\n    fetchData();\n  }, []);\n\n  const fetchData = async () => {\n    setLoading(true);\n    try {\n      console.log('正在获取成员数据...');\n\n      // 获取成员列表\n      const membersData = await getTeamMembers();\n      console.log('成员列表响应:', membersData);\n\n      // 设置成员数据 - 直接使用数组，不需要.members属性\n      const members = Array.isArray(membersData) ? membersData : (membersData.members || []);\n      setMembers(members);\n\n      console.log('成员数量:', members.length);\n\n      // 获取使用数据 - 使用最近30天的数据\n      const endDate = new Date().toISOString().split('T')[0];\n      const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];\n\n      try {\n        const usageResponse = await getMembersUsage(startDate, endDate);\n        console.log('成员使用数据响应:', usageResponse);\n\n        // 将使用数据转换为以email为键的对象\n        const usageByMember = {};\n        (usageResponse.members || []).forEach(member => {\n          const memberId = member.email || member.id || member.userId;\n          usageByMember[memberId] = member.usage || {\n            totalPrompts: 0,\n            totalTokens: 0,\n            totalLinesAdded: 0,\n            totalLinesDeleted: 0\n          };\n        });\n\n        setUsageData(usageByMember);\n      } catch (usageErr) {\n        console.warn('获取使用数据失败，使用空数据:', usageErr);\n        // 如果获取使用数据失败，创建空的使用数据对象\n        const usageByMember = {};\n        members.forEach(member => {\n          usageByMember[member.email || member.id || member.userId] = {\n            totalPrompts: 0,\n            totalTokens: 0,\n            totalLinesAdded: 0,\n            totalLinesDeleted: 0\n          };\n        });\n        setUsageData(usageByMember);\n      }\n    } catch (err) {\n      console.error('获取成员数据失败:', err);\n      setError(`获取成员数据失败: ${err.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) return <div>加载中...</div>;\n  if (error) return <div className=\"error-message\">{error}</div>;\n\n  return (\n    <div className=\"members-list\">\n      <h2>团队成员使用情况</h2>\n      \n      <UsageBarChart members={members} usageData={usageData} />\n      \n      <div className=\"members-table\">\n        <table>\n          <thead>\n            <tr>\n              <th>成员</th>\n              <th>提示次数</th>\n              <th>消耗Token</th>\n              <th>操作</th>\n            </tr>\n          </thead>\n          <tbody>\n            {members.map(member => {\n              const memberId = member.email || member.id || member.userId;\n              const usage = usageData[memberId] || {};\n              return (\n                <tr key={memberId}>\n                  <td>{member.name || member.email}</td>\n                  <td>{usage.totalPrompts || 0}</td>\n                  <td>{(usage.totalLinesAdded || 0) + (usage.totalLinesDeleted || 0)}</td>\n                  <td>\n                    <button onClick={() => onSelectMember(memberId)}>\n                      查看详情\n                    </button>\n                  </td>\n                </tr>\n              );\n            })}\n          </tbody>\n        </table>\n      </div>\n    </div>\n  );\n}\n\nexport default MembersList;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,cAAc,EAAEC,eAAe,QAAQ,oBAAoB;AACpE,OAAOC,aAAa,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,SAASC,WAAWA,CAAC;EAAEC;AAAe,CAAC,EAAE;EAAAC,EAAA;EACvC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9C,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAEtCC,SAAS,CAAC,MAAM;IACdiB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5BH,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACFI,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;;MAE1B;MACA,MAAMC,WAAW,GAAG,MAAMnB,cAAc,CAAC,CAAC;MAC1CiB,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEC,WAAW,CAAC;;MAEnC;MACA,MAAMX,OAAO,GAAGY,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,GAAGA,WAAW,GAAIA,WAAW,CAACX,OAAO,IAAI,EAAG;MACtFC,UAAU,CAACD,OAAO,CAAC;MAEnBS,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEV,OAAO,CAACc,MAAM,CAAC;;MAEpC;MACA,MAAMC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACtD,MAAMC,SAAS,GAAG,IAAIH,IAAI,CAACA,IAAI,CAACI,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACH,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAE7F,IAAI;QACF,MAAMG,aAAa,GAAG,MAAM5B,eAAe,CAAC0B,SAAS,EAAEJ,OAAO,CAAC;QAC/DN,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEW,aAAa,CAAC;;QAEvC;QACA,MAAMC,aAAa,GAAG,CAAC,CAAC;QACxB,CAACD,aAAa,CAACrB,OAAO,IAAI,EAAE,EAAEuB,OAAO,CAACC,MAAM,IAAI;UAC9C,MAAMC,QAAQ,GAAGD,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACG,EAAE,IAAIH,MAAM,CAACI,MAAM;UAC3DN,aAAa,CAACG,QAAQ,CAAC,GAAGD,MAAM,CAACK,KAAK,IAAI;YACxCC,YAAY,EAAE,CAAC;YACfC,WAAW,EAAE,CAAC;YACdC,eAAe,EAAE,CAAC;YAClBC,iBAAiB,EAAE;UACrB,CAAC;QACH,CAAC,CAAC;QAEF9B,YAAY,CAACmB,aAAa,CAAC;MAC7B,CAAC,CAAC,OAAOY,QAAQ,EAAE;QACjBzB,OAAO,CAAC0B,IAAI,CAAC,iBAAiB,EAAED,QAAQ,CAAC;QACzC;QACA,MAAMZ,aAAa,GAAG,CAAC,CAAC;QACxBtB,OAAO,CAACuB,OAAO,CAACC,MAAM,IAAI;UACxBF,aAAa,CAACE,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACG,EAAE,IAAIH,MAAM,CAACI,MAAM,CAAC,GAAG;YAC1DE,YAAY,EAAE,CAAC;YACfC,WAAW,EAAE,CAAC;YACdC,eAAe,EAAE,CAAC;YAClBC,iBAAiB,EAAE;UACrB,CAAC;QACH,CAAC,CAAC;QACF9B,YAAY,CAACmB,aAAa,CAAC;MAC7B;IACF,CAAC,CAAC,OAAOc,GAAG,EAAE;MACZ3B,OAAO,CAACH,KAAK,CAAC,WAAW,EAAE8B,GAAG,CAAC;MAC/B7B,QAAQ,CAAC,aAAa6B,GAAG,CAACC,OAAO,EAAE,CAAC;IACtC,CAAC,SAAS;MACRhC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAID,OAAO,EAAE,oBAAOR,OAAA;IAAA0C,QAAA,EAAK;EAAM;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EACrC,IAAIpC,KAAK,EAAE,oBAAOV,OAAA;IAAK+C,SAAS,EAAC,eAAe;IAAAL,QAAA,EAAEhC;EAAK;IAAAiC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAE9D,oBACE9C,OAAA;IAAK+C,SAAS,EAAC,cAAc;IAAAL,QAAA,gBAC3B1C,OAAA;MAAA0C,QAAA,EAAI;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAEjB9C,OAAA,CAACF,aAAa;MAACM,OAAO,EAAEA,OAAQ;MAACE,SAAS,EAAEA;IAAU;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEzD9C,OAAA;MAAK+C,SAAS,EAAC,eAAe;MAAAL,QAAA,eAC5B1C,OAAA;QAAA0C,QAAA,gBACE1C,OAAA;UAAA0C,QAAA,eACE1C,OAAA;YAAA0C,QAAA,gBACE1C,OAAA;cAAA0C,QAAA,EAAI;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACX9C,OAAA;cAAA0C,QAAA,EAAI;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACb9C,OAAA;cAAA0C,QAAA,EAAI;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChB9C,OAAA;cAAA0C,QAAA,EAAI;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACR9C,OAAA;UAAA0C,QAAA,EACGtC,OAAO,CAAC4C,GAAG,CAACpB,MAAM,IAAI;YACrB,MAAMC,QAAQ,GAAGD,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACG,EAAE,IAAIH,MAAM,CAACI,MAAM;YAC3D,MAAMC,KAAK,GAAG3B,SAAS,CAACuB,QAAQ,CAAC,IAAI,CAAC,CAAC;YACvC,oBACE7B,OAAA;cAAA0C,QAAA,gBACE1C,OAAA;gBAAA0C,QAAA,EAAKd,MAAM,CAACqB,IAAI,IAAIrB,MAAM,CAACE;cAAK;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtC9C,OAAA;gBAAA0C,QAAA,EAAKT,KAAK,CAACC,YAAY,IAAI;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClC9C,OAAA;gBAAA0C,QAAA,EAAK,CAACT,KAAK,CAACG,eAAe,IAAI,CAAC,KAAKH,KAAK,CAACI,iBAAiB,IAAI,CAAC;cAAC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxE9C,OAAA;gBAAA0C,QAAA,eACE1C,OAAA;kBAAQkD,OAAO,EAAEA,CAAA,KAAMhD,cAAc,CAAC2B,QAAQ,CAAE;kBAAAa,QAAA,EAAC;gBAEjD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA,GAREjB,QAAQ;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASb,CAAC;UAET,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC3C,EAAA,CA7GQF,WAAW;AAAAkD,EAAA,GAAXlD,WAAW;AA+GpB,eAAeA,WAAW;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}