{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Auth/Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { testApiKey } from '../../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Login({\n  onLoginSuccess\n}) {\n  _s();\n  const [apiKey, setApiKey] = useState('');\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [testResult, setTestResult] = useState('');\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    try {\n      console.log('开始验证API Key...');\n      // 测试API Key是否有效\n      const result = await testApiKey(apiKey);\n      if (result.valid) {\n        console.log('API Key验证成功，登录成功');\n        // 登录成功\n        onLoginSuccess();\n      } else {\n        console.log('API Key验证失败:', result.error);\n        setError(result.error || 'API Key无效，请检查后重试');\n        localStorage.removeItem('cursor_api_key');\n      }\n    } catch (err) {\n      console.error('登录过程中发生错误:', err);\n      setError(`连接失败: ${err.message}`);\n      localStorage.removeItem('cursor_api_key');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleTestConnection = async () => {\n    if (!apiKey.trim()) {\n      setTestResult('请先输入API Key');\n      return;\n    }\n    setTestResult('测试连接中...');\n    try {\n      const response = await fetch('/api/teams/members', {\n        method: 'GET',\n        headers: {\n          'Authorization': `Basic ${btoa(apiKey + ':')}`\n        }\n      });\n      if (response.ok) {\n        setTestResult('✅ 连接成功！API Key有效');\n      } else {\n        const errorText = await response.text();\n        setTestResult(`❌ 连接失败: ${response.status} - ${errorText}`);\n      }\n    } catch (error) {\n      setTestResult(`❌ 网络错误: ${error.message}`);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"login-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Cursor\\u56E2\\u961F\\u4F7F\\u7528\\u60C5\\u51B5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"API Key\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"password\",\n          value: apiKey,\n          onChange: e => setApiKey(e.target.value),\n          placeholder: \"\\u8F93\\u5165\\u60A8\\u7684Cursor Admin API Key\",\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 19\n      }, this), testResult && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: testResult.includes('✅') ? 'success-message' : 'info-message',\n        children: testResult\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 24\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"button-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: handleTestConnection,\n          disabled: loading,\n          children: \"\\u6D4B\\u8BD5\\u8FDE\\u63A5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: loading,\n          children: loading ? '验证中...' : '登录'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 65,\n    columnNumber: 5\n  }, this);\n}\n_s(Login, \"enokxjM2LJbuGzDmB+ddH8r/AY4=\");\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "test<PERSON>pi<PERSON>ey", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "onLoginSuccess", "_s", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "error", "setError", "loading", "setLoading", "testResult", "setTestResult", "handleSubmit", "e", "preventDefault", "console", "log", "result", "valid", "localStorage", "removeItem", "err", "message", "handleTestConnection", "trim", "response", "fetch", "method", "headers", "btoa", "ok", "errorText", "text", "status", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "value", "onChange", "target", "placeholder", "required", "includes", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Auth/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { testApiKey } from '../../services/api';\n\nfunction Login({ onLoginSuccess }) {\n  const [apiKey, setApiKey] = useState('');\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [testResult, setTestResult] = useState('');\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    try {\n      console.log('开始验证API Key...');\n      // 测试API Key是否有效\n      const result = await testApiKey(apiKey);\n\n      if (result.valid) {\n        console.log('API Key验证成功，登录成功');\n        // 登录成功\n        onLoginSuccess();\n      } else {\n        console.log('API Key验证失败:', result.error);\n        setError(result.error || 'API Key无效，请检查后重试');\n        localStorage.removeItem('cursor_api_key');\n      }\n    } catch (err) {\n      console.error('登录过程中发生错误:', err);\n      setError(`连接失败: ${err.message}`);\n      localStorage.removeItem('cursor_api_key');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleTestConnection = async () => {\n    if (!apiKey.trim()) {\n      setTestResult('请先输入API Key');\n      return;\n    }\n\n    setTestResult('测试连接中...');\n    try {\n      const response = await fetch('/api/teams/members', {\n        method: 'GET',\n        headers: {\n          'Authorization': `Basic ${btoa(apiKey + ':')}`\n        }\n      });\n\n      if (response.ok) {\n        setTestResult('✅ 连接成功！API Key有效');\n      } else {\n        const errorText = await response.text();\n        setTestResult(`❌ 连接失败: ${response.status} - ${errorText}`);\n      }\n    } catch (error) {\n      setTestResult(`❌ 网络错误: ${error.message}`);\n    }\n  };\n\n  return (\n    <div className=\"login-container\">\n      <h2>Cursor团队使用情况</h2>\n      <form onSubmit={handleSubmit}>\n        <div className=\"form-group\">\n          <label>API Key</label>\n          <input\n            type=\"password\"\n            value={apiKey}\n            onChange={(e) => setApiKey(e.target.value)}\n            placeholder=\"输入您的Cursor Admin API Key\"\n            required\n          />\n        </div>\n        {error && <div className=\"error-message\">{error}</div>}\n        {testResult && <div className={testResult.includes('✅') ? 'success-message' : 'info-message'}>{testResult}</div>}\n        <div className=\"button-group\">\n          <button type=\"button\" onClick={handleTestConnection} disabled={loading}>\n            测试连接\n          </button>\n          <button type=\"submit\" disabled={loading}>\n            {loading ? '验证中...' : '登录'}\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n}\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,UAAU,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,SAASC,KAAKA,CAAC;EAAEC;AAAe,CAAC,EAAE;EAAAC,EAAA;EACjC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACS,KAAK,EAAEC,QAAQ,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACa,UAAU,EAAEC,aAAa,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAMe,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBL,UAAU,CAAC,IAAI,CAAC;IAChBF,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACFQ,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;MAC7B;MACA,MAAMC,MAAM,GAAG,MAAMnB,UAAU,CAACM,MAAM,CAAC;MAEvC,IAAIa,MAAM,CAACC,KAAK,EAAE;QAChBH,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;QAC/B;QACAd,cAAc,CAAC,CAAC;MAClB,CAAC,MAAM;QACLa,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEC,MAAM,CAACX,KAAK,CAAC;QACzCC,QAAQ,CAACU,MAAM,CAACX,KAAK,IAAI,kBAAkB,CAAC;QAC5Ca,YAAY,CAACC,UAAU,CAAC,gBAAgB,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZN,OAAO,CAACT,KAAK,CAAC,YAAY,EAAEe,GAAG,CAAC;MAChCd,QAAQ,CAAC,SAASc,GAAG,CAACC,OAAO,EAAE,CAAC;MAChCH,YAAY,CAACC,UAAU,CAAC,gBAAgB,CAAC;IAC3C,CAAC,SAAS;MACRX,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMc,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACnB,MAAM,CAACoB,IAAI,CAAC,CAAC,EAAE;MAClBb,aAAa,CAAC,aAAa,CAAC;MAC5B;IACF;IAEAA,aAAa,CAAC,UAAU,CAAC;IACzB,IAAI;MACF,MAAMc,QAAQ,GAAG,MAAMC,KAAK,CAAC,oBAAoB,EAAE;QACjDC,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,eAAe,EAAE,SAASC,IAAI,CAACzB,MAAM,GAAG,GAAG,CAAC;QAC9C;MACF,CAAC,CAAC;MAEF,IAAIqB,QAAQ,CAACK,EAAE,EAAE;QACfnB,aAAa,CAAC,kBAAkB,CAAC;MACnC,CAAC,MAAM;QACL,MAAMoB,SAAS,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;QACvCrB,aAAa,CAAC,WAAWc,QAAQ,CAACQ,MAAM,MAAMF,SAAS,EAAE,CAAC;MAC5D;IACF,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACdK,aAAa,CAAC,WAAWL,KAAK,CAACgB,OAAO,EAAE,CAAC;IAC3C;EACF,CAAC;EAED,oBACEtB,OAAA;IAAKkC,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC9BnC,OAAA;MAAAmC,QAAA,EAAI;IAAY;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACrBvC,OAAA;MAAMwC,QAAQ,EAAE5B,YAAa;MAAAuB,QAAA,gBAC3BnC,OAAA;QAAKkC,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBnC,OAAA;UAAAmC,QAAA,EAAO;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACtBvC,OAAA;UACEyC,IAAI,EAAC,UAAU;UACfC,KAAK,EAAEtC,MAAO;UACduC,QAAQ,EAAG9B,CAAC,IAAKR,SAAS,CAACQ,CAAC,CAAC+B,MAAM,CAACF,KAAK,CAAE;UAC3CG,WAAW,EAAC,8CAA0B;UACtCC,QAAQ;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EACLjC,KAAK,iBAAIN,OAAA;QAAKkC,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAE7B;MAAK;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EACrD7B,UAAU,iBAAIV,OAAA;QAAKkC,SAAS,EAAExB,UAAU,CAACqC,QAAQ,CAAC,GAAG,CAAC,GAAG,iBAAiB,GAAG,cAAe;QAAAZ,QAAA,EAAEzB;MAAU;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAChHvC,OAAA;QAAKkC,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BnC,OAAA;UAAQyC,IAAI,EAAC,QAAQ;UAACO,OAAO,EAAEzB,oBAAqB;UAAC0B,QAAQ,EAAEzC,OAAQ;UAAA2B,QAAA,EAAC;QAExE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvC,OAAA;UAAQyC,IAAI,EAAC,QAAQ;UAACQ,QAAQ,EAAEzC,OAAQ;UAAA2B,QAAA,EACrC3B,OAAO,GAAG,QAAQ,GAAG;QAAI;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACpC,EAAA,CAvFQF,KAAK;AAAAiD,EAAA,GAALjD,KAAK;AAyFd,eAAeA,KAAK;AAAC,IAAAiD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}