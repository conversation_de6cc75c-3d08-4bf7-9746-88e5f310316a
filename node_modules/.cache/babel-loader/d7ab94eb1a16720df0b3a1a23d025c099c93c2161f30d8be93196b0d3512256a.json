{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport Login from './components/Auth/Login';\nimport TeamOverview from './components/Dashboard/TeamOverview';\nimport MembersList from './components/Dashboard/MembersList';\nimport MemberDetail from './components/Dashboard/MemberDetail';\nimport Header from './components/UI/Header';\nimport ApiTest from './components/ApiTest';\nimport './styles.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\n  const [selectedMemberId, setSelectedMemberId] = useState(null);\n  useEffect(() => {\n    // 检查是否已有API Key\n    const apiKey = localStorage.getItem('cursor_api_key');\n    if (apiKey) {\n      setIsLoggedIn(true);\n    }\n  }, []);\n  const handleLogout = () => {\n    localStorage.removeItem('cursor_api_key');\n    setIsLoggedIn(false);\n    setSelectedMemberId(null);\n  };\n  const handleSelectMember = userId => {\n    setSelectedMemberId(userId);\n  };\n  const handleBackToList = () => {\n    setSelectedMemberId(null);\n  };\n  if (!isLoggedIn) {\n    return /*#__PURE__*/_jsxDEV(Login, {\n      onLoginSuccess: () => setIsLoggedIn(true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      onLogout: handleLogout\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"content\",\n      children: selectedMemberId ? /*#__PURE__*/_jsxDEV(MemberDetail, {\n        memberId: selectedMemberId,\n        onBack: handleBackToList\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(TeamOverview, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(MembersList, {\n          onSelectMember: handleSelectMember\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"gZ8bWlp06tQEXtxuTRVUA6F5GAA=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON>", "TeamOverview", "MembersList", "MemberDetail", "Header", "ApiTest", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "App", "_s", "isLoggedIn", "setIsLoggedIn", "selectedMemberId", "setSelectedMemberId", "<PERSON><PERSON><PERSON><PERSON>", "localStorage", "getItem", "handleLogout", "removeItem", "handleSelectMember", "userId", "handleBackToList", "onLoginSuccess", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "onLogout", "memberId", "onBack", "onSelectMember", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport Login from './components/Auth/Login';\nimport TeamOverview from './components/Dashboard/TeamOverview';\nimport MembersList from './components/Dashboard/MembersList';\nimport MemberDetail from './components/Dashboard/MemberDetail';\nimport Header from './components/UI/Header';\nimport ApiTest from './components/ApiTest';\nimport './styles.css';\n\nfunction App() {\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\n  const [selectedMemberId, setSelectedMemberId] = useState(null);\n  \n  useEffect(() => {\n    // 检查是否已有API Key\n    const apiKey = localStorage.getItem('cursor_api_key');\n    if (apiKey) {\n      setIsLoggedIn(true);\n    }\n  }, []);\n  \n  const handleLogout = () => {\n    localStorage.removeItem('cursor_api_key');\n    setIsLoggedIn(false);\n    setSelectedMemberId(null);\n  };\n  \n  const handleSelectMember = (userId) => {\n    setSelectedMemberId(userId);\n  };\n  \n  const handleBackToList = () => {\n    setSelectedMemberId(null);\n  };\n\n  if (!isLoggedIn) {\n    return <Login onLoginSuccess={() => setIsLoggedIn(true)} />;\n  }\n\n  return (\n    <div className=\"app\">\n      <Header onLogout={handleLogout} />\n      \n      <main className=\"content\">\n        {selectedMemberId ? (\n          <MemberDetail \n            memberId={selectedMemberId} \n            onBack={handleBackToList} \n          />\n        ) : (\n          <>\n            <TeamOverview />\n            <MembersList onSelectMember={handleSelectMember} />\n          </>\n        )}\n      </main>\n    </div>\n  );\n}\n\nexport default App;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,yBAAyB;AAC3C,OAAOC,YAAY,MAAM,qCAAqC;AAC9D,OAAOC,WAAW,MAAM,oCAAoC;AAC5D,OAAOC,YAAY,MAAM,qCAAqC;AAC9D,OAAOC,MAAM,MAAM,wBAAwB;AAC3C,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACgB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAE9DC,SAAS,CAAC,MAAM;IACd;IACA,MAAMiB,MAAM,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IACrD,IAAIF,MAAM,EAAE;MACVH,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMM,YAAY,GAAGA,CAAA,KAAM;IACzBF,YAAY,CAACG,UAAU,CAAC,gBAAgB,CAAC;IACzCP,aAAa,CAAC,KAAK,CAAC;IACpBE,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMM,kBAAkB,GAAIC,MAAM,IAAK;IACrCP,mBAAmB,CAACO,MAAM,CAAC;EAC7B,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BR,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,IAAI,CAACH,UAAU,EAAE;IACf,oBAAOL,OAAA,CAACP,KAAK;MAACwB,cAAc,EAAEA,CAAA,KAAMX,aAAa,CAAC,IAAI;IAAE;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC7D;EAEA,oBACErB,OAAA;IAAKsB,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClBvB,OAAA,CAACH,MAAM;MAAC2B,QAAQ,EAAEZ;IAAa;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAElCrB,OAAA;MAAMsB,SAAS,EAAC,SAAS;MAAAC,QAAA,EACtBhB,gBAAgB,gBACfP,OAAA,CAACJ,YAAY;QACX6B,QAAQ,EAAElB,gBAAiB;QAC3BmB,MAAM,EAAEV;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,gBAEFrB,OAAA,CAAAE,SAAA;QAAAqB,QAAA,gBACEvB,OAAA,CAACN,YAAY;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChBrB,OAAA,CAACL,WAAW;UAACgC,cAAc,EAAEb;QAAmB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA,eACnD;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACjB,EAAA,CAjDQD,GAAG;AAAAyB,EAAA,GAAHzB,GAAG;AAmDZ,eAAeA,GAAG;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}