[{"/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/index.js": "1", "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/App.js": "2", "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Dashboard/TeamOverview.js": "3", "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Auth/Login.js": "4", "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/UI/Header.js": "5", "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Dashboard/MembersList.js": "6", "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Dashboard/MemberDetail.js": "7", "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/UI/DateRangePicker.js": "8", "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/services/api.js": "9", "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Charts/TrendLineChart.js": "10", "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Charts/UsageBarChart.js": "11", "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/config.js": "12", "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/utils/debug.js": "13", "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/ApiTest.js": "14"}, {"size": 231, "mtime": 1749028368294, "results": "15", "hashOfConfig": "16"}, {"size": 1875, "mtime": 1749033603656, "results": "17", "hashOfConfig": "16"}, {"size": 3780, "mtime": 1749036811061, "results": "18", "hashOfConfig": "16"}, {"size": 2865, "mtime": 1749036265792, "results": "19", "hashOfConfig": "16"}, {"size": 388, "mtime": 1749028081254, "results": "20", "hashOfConfig": "16"}, {"size": 3804, "mtime": 1749036687867, "results": "21", "hashOfConfig": "16"}, {"size": 2877, "mtime": 1749028075232, "results": "22", "hashOfConfig": "16"}, {"size": 1597, "mtime": 1749028082069, "results": "23", "hashOfConfig": "16"}, {"size": 7154, "mtime": 1749036586207, "results": "24", "hashOfConfig": "16"}, {"size": 2202, "mtime": 1749036740008, "results": "25", "hashOfConfig": "16"}, {"size": 2565, "mtime": 1749036720443, "results": "26", "hashOfConfig": "16"}, {"size": 787, "mtime": 1749036553013, "results": "27", "hashOfConfig": "16"}, {"size": 908, "mtime": 1749028697683, "results": "28", "hashOfConfig": "16"}, {"size": 3794, "mtime": 1749033529612, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "18s5asi", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/index.js", [], [], "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/App.js", [], [], "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Dashboard/TeamOverview.js", ["72"], [], "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Auth/Login.js", [], [], "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/UI/Header.js", [], [], "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Dashboard/MembersList.js", [], [], "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Dashboard/MemberDetail.js", ["73"], [], "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/UI/DateRangePicker.js", [], [], "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/services/api.js", [], [], "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Charts/TrendLineChart.js", [], [], "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Charts/UsageBarChart.js", [], [], "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/config.js", [], [], "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/utils/debug.js", [], [], "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/ApiTest.js", [], [], {"ruleId": "74", "severity": 1, "message": "75", "line": 17, "column": 6, "nodeType": "76", "endLine": 17, "endColumn": 17, "suggestions": "77"}, {"ruleId": "74", "severity": 1, "message": "78", "line": 17, "column": 6, "nodeType": "76", "endLine": 17, "endColumn": 27, "suggestions": "79"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchUsageData'. Either include it or remove the dependency array.", "ArrayExpression", ["80"], "React Hook useEffect has a missing dependency: 'fetchMemberData'. Either include it or remove the dependency array.", ["81"], {"desc": "82", "fix": "83"}, {"desc": "84", "fix": "85"}, "Update the dependencies array to be: [dateRange, fetchUsageData]", {"range": "86", "text": "87"}, "Update the dependencies array to be: [memberId, dateRange, fetchMemberData]", {"range": "88", "text": "89"}, [625, 636], "[date<PERSON><PERSON><PERSON>, fetchUsageData]", [650, 671], "[memberId, date<PERSON><PERSON>e, fetchMemberData]"]