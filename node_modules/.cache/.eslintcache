[{"/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/index.js": "1", "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/App.js": "2", "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Dashboard/TeamOverview.js": "3", "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Auth/Login.js": "4", "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/UI/Header.js": "5", "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Dashboard/MembersList.js": "6", "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Dashboard/MemberDetail.js": "7", "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/UI/DateRangePicker.js": "8", "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/services/api.js": "9", "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Charts/TrendLineChart.js": "10", "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Charts/UsageBarChart.js": "11", "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/config.js": "12", "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/utils/debug.js": "13"}, {"size": 231, "mtime": 1749028368294, "results": "14", "hashOfConfig": "15"}, {"size": 1526, "mtime": 1749027425710, "results": "16", "hashOfConfig": "15"}, {"size": 2075, "mtime": 1749027507299, "results": "17", "hashOfConfig": "15"}, {"size": 1537, "mtime": 1749032095159, "results": "18", "hashOfConfig": "15"}, {"size": 388, "mtime": 1749028081254, "results": "19", "hashOfConfig": "15"}, {"size": 2266, "mtime": 1749027511138, "results": "20", "hashOfConfig": "15"}, {"size": 2877, "mtime": 1749028075232, "results": "21", "hashOfConfig": "15"}, {"size": 1597, "mtime": 1749028082069, "results": "22", "hashOfConfig": "15"}, {"size": 5319, "mtime": 1749032098024, "results": "23", "hashOfConfig": "15"}, {"size": 2200, "mtime": 1749028077010, "results": "24", "hashOfConfig": "15"}, {"size": 2367, "mtime": 1749028078696, "results": "25", "hashOfConfig": "15"}, {"size": 764, "mtime": 1749032085551, "results": "26", "hashOfConfig": "15"}, {"size": 908, "mtime": 1749028697683, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "18s5asi", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/index.js", [], [], "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/App.js", [], [], "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Dashboard/TeamOverview.js", ["67"], [], "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Auth/Login.js", [], [], "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/UI/Header.js", [], [], "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Dashboard/MembersList.js", [], [], "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Dashboard/MemberDetail.js", ["68"], [], "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/UI/DateRangePicker.js", [], [], "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/services/api.js", ["69"], [], "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Charts/TrendLineChart.js", [], [], "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/components/Charts/UsageBarChart.js", [], [], "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/config.js", [], [], "/Users/<USER>/wubin/augment_project/cursor_ statistics_project/src/utils/debug.js", [], [], {"ruleId": "70", "severity": 1, "message": "71", "line": 17, "column": 6, "nodeType": "72", "endLine": 17, "endColumn": 17, "suggestions": "73"}, {"ruleId": "70", "severity": 1, "message": "74", "line": 17, "column": 6, "nodeType": "72", "endLine": 17, "endColumn": 27, "suggestions": "75"}, {"ruleId": "76", "severity": 1, "message": "77", "line": 86, "column": 13, "nodeType": "78", "messageId": "79", "endLine": 86, "endColumn": 28}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchUsageData'. Either include it or remove the dependency array.", "ArrayExpression", ["80"], "React Hook useEffect has a missing dependency: 'fetchMemberData'. Either include it or remove the dependency array.", ["81"], "no-unused-vars", "'originalBaseUrl' is assigned a value but never used.", "Identifier", "unusedVar", {"desc": "82", "fix": "83"}, {"desc": "84", "fix": "85"}, "Update the dependencies array to be: [dateRange, fetchUsageData]", {"range": "86", "text": "87"}, "Update the dependencies array to be: [memberId, dateRange, fetchMemberData]", {"range": "88", "text": "89"}, [625, 636], "[date<PERSON><PERSON><PERSON>, fetchUsageData]", [650, 671], "[memberId, date<PERSON><PERSON>e, fetchMemberData]"]