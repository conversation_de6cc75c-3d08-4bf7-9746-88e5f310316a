const express = require('express');
const cors = require('cors');
const { createProxyMiddleware } = require('http-proxy-middleware');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3001;

// 启用CORS
app.use(cors());

// 静态文件服务
app.use(express.static(path.join(__dirname, 'build')));

// 代理API请求
app.use('/api', createProxyMiddleware({
  target: 'https://cursor.com',
  changeOrigin: true,
  pathRewrite: {
    '^/api': '/api/v1/admin' // 重写路径
  },
  onProxyReq: (proxyReq, req, res) => {
    // 从请求中获取API密钥
    const apiKey = req.headers['x-api-key'];
    if (apiKey) {
      // 设置认证头
      proxyReq.setHeader('Authorization', `Bearer ${apiKey}`);
    }
  },
  onError: (err, req, res) => {
    console.error('代理错误:', err);
    res.status(500).send('代理请求失败');
  }
}));

// 所有其他请求返回React应用
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'build', 'index.html'));
});

app.listen(PORT, () => {
  console.log(`服务器运行在 http://localhost:${PORT}`);
});
