<!DOCTYPE html>
<html>
<head>
    <title>Cursor API测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        #result { margin-top: 20px; padding: 10px; border: 1px solid #ccc; min-height: 100px; }
        button { padding: 10px; margin-top: 10px; }
    </style>
</head>
<body>
    <h1>Cursor API测试</h1>
    <div>
        <label for="apiKey">API Key:</label>
        <input type="text" id="apiKey" size="60" value="key_cb5ef9928930ef58ae5316fdc429b3c21dfc4b5475921f3de78d98a723ff5350">
    </div>
    <div>
        <label for="endpoint">端点:</label>
        <input type="text" id="endpoint" value="/team/members" size="30">
    </div>
    <div>
        <button onclick="testAPI()">测试API</button>
        <button onclick="clearResult()">清除结果</button>
    </div>
    <div id="result"></div>

    <script>
        const BASE_URL = 'https://cursor.com/api/v1/admin';
        
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            const apiKey = document.getElementById('apiKey').value;
            const endpoint = document.getElementById('endpoint').value;
            
            resultDiv.innerHTML = '正在测试...';
            
            try {
                const url = `${BASE_URL}${endpoint}`;
                console.log('请求URL:', url);
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                console.log('响应状态:', response.status);
                
                let data;
                try {
                    data = await response.json();
                } catch (e) {
                    data = await response.text();
                }
                
                console.log('响应数据:', data);
                
                if (response.ok) {
                    resultDiv.innerHTML = `<p style="color:green">请求成功!</p><pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    resultDiv.innerHTML = `<p style="color:red">请求失败: ${response.status}</p><pre>${JSON.stringify(data, null, 2)}</pre>`;
                }
            } catch (error) {
                console.error('请求错误:', error);
                resultDiv.innerHTML = `<p style="color:red">错误: ${error.message}</p>`;
            }
        }
        
        function clearResult() {
            document.getElementById('result').innerHTML = '';
        }
    </script>
</body>
</html>