<!DOCTYPE html>
<html>
<head>
    <title>Cursor Admin API测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        #result { margin-top: 20px; padding: 10px; border: 1px solid #ccc; min-height: 100px; }
        button { padding: 10px; margin-top: 10px; }
        .url-option { margin: 5px 0; }
        .form-group { margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Cursor Admin API测试</h1>
    <div class="form-group">
        <label for="apiKey">API Key:</label>
        <input type="text" id="apiKey" size="60" value="key_cb5ef9928930ef58ae5316fdc429b3c21dfc4b5475921f3de78d98a723ff5350">
    </div>
    
    <div class="form-group">
        <p>选择基础URL:</p>
        <div class="url-option">
            <input type="radio" id="url1" name="baseUrl" value="https://api.cursor.com" checked>
            <label for="url1">https://api.cursor.com</label>
        </div>
        <div class="url-option">
            <input type="radio" id="url2" name="baseUrl" value="https://api.cursor.sh">
            <label for="url2">https://api.cursor.sh</label>
        </div>
        <div class="url-option">
            <input type="radio" id="url3" name="baseUrl" value="https://cursor.sh/api">
            <label for="url3">https://cursor.sh/api</label>
        </div>
        <div class="url-option">
            <input type="radio" id="url4" name="baseUrl" value="https://cursor.com/api">
            <label for="url4">https://cursor.com/api</label>
        </div>
    </div>
    
    <div class="form-group">
        <label for="endpoint">端点:</label>
        <input type="text" id="endpoint" value="/teams/members" size="30">
    </div>
    
    <div class="form-group">
        <label for="method">请求方法:</label>
        <select id="method">
            <option value="GET">GET</option>
            <option value="POST">POST</option>
        </select>
    </div>
    
    <div class="form-group" id="bodyContainer" style="display: none;">
        <label for="requestBody">请求体 (JSON):</label>
        <textarea id="requestBody" rows="5" cols="60">{"startDate": 1710720000000, "endDate": 1710892800000}</textarea>
    </div>
    
    <div class="form-group">
        <button onclick="testAPI()">测试API</button>
        <button onclick="clearResult()">清除结果</button>
    </div>
    
    <div id="result"></div>

    <script>
        // 当请求方法改变时显示/隐藏请求体
        document.getElementById('method').addEventListener('change', function() {
            document.getElementById('bodyContainer').style.display = 
                this.value === 'POST' ? 'block' : 'none';
        });
        
        // 创建Basic Auth凭证
        function createBasicAuth(apiKey) {
            // 根据文档，用户名是API Key，密码为空
            return 'Basic ' + btoa(`${apiKey}:`);
        }
        
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            const apiKey = document.getElementById('apiKey').value;
            const endpoint = document.getElementById('endpoint').value;
            const baseUrl = document.querySelector('input[name="baseUrl"]:checked').value;
            const method = document.getElementById('method').value;
            
            resultDiv.innerHTML = '正在测试...';
            
            try {
                const url = `${baseUrl}${endpoint}`;
                console.log('请求URL:', url);
                console.log('请求方法:', method);
                
                // 创建请求头，使用Basic Auth
                const headers = {
                    'Content-Type': 'application/json',
                    'Authorization': createBasicAuth(apiKey)
                };
                
                const options = {
                    method: method,
                    headers: headers
                };
                
                // 如果是POST请求，添加请求体
                if (method === 'POST') {
                    const bodyText = document.getElementById('requestBody').value;
                    try {
                        const bodyJson = JSON.parse(bodyText);
                        options.body = JSON.stringify(bodyJson);
                        console.log('请求体:', bodyJson);
                    } catch (e) {
                        resultDiv.innerHTML = `<p style="color:red">请求体JSON格式错误: ${e.message}</p>`;
                        return;
                    }
                }
                
                const response = await fetch(url, options);
                
                console.log('响应状态:', response.status);
                
                let data;
                try {
                    data = await response.json();
                } catch (e) {
                    data = await response.text();
                }
                
                console.log('响应数据:', data);
                
                if (response.ok) {
                    resultDiv.innerHTML = `<p style="color:green">请求成功!</p><pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    resultDiv.innerHTML = `<p style="color:red">请求失败: ${response.status}</p><pre>${JSON.stringify(data, null, 2)}</pre>`;
                }
            } catch (error) {
                console.error('请求错误:', error);
                resultDiv.innerHTML = `<p style="color:red">错误: ${error.message}</p>`;
            }
        }
        
        function clearResult() {
            document.getElementById('result').innerHTML = '';
        }
    </script>
</body>
</html>
