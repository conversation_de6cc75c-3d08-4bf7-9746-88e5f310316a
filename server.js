const express = require('express');
const cors = require('cors');
const { createProxyMiddleware } = require('http-proxy-middleware');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3001;

// 启用CORS
app.use(cors());

// 解析JSON请求体
app.use(express.json());

// 静态文件服务
app.use(express.static(path.join(__dirname, 'build')));

// 代理API请求到Cursor API
app.use('/api', createProxyMiddleware({
  target: 'https://api.cursor.com',
  changeOrigin: true,
  pathRewrite: {
    '^/api': '' // 移除/api前缀
  },
  onProxyReq: (proxyReq, req, res) => {
    // 从请求头中获取认证信息
    const authHeader = req.headers['authorization'];
    if (authHeader) {
      // 直接转发认证头（应该已经是Basic Auth格式）
      proxyReq.setHeader('Authorization', authHeader);
    }

    console.log('代理请求:', {
      method: req.method,
      url: req.url,
      target: 'https://api.cursor.com' + req.url.replace('/api', ''),
      hasAuth: !!authHeader,
      authType: authHeader ? authHeader.split(' ')[0] : 'none'
    });
  },
  onProxyRes: (proxyRes, req, res) => {
    console.log('代理响应:', {
      status: proxyRes.statusCode,
      url: req.url
    });
  },
  onError: (err, req, res) => {
    console.error('代理错误:', err.message);
    res.status(500).json({ 
      error: '代理请求失败', 
      message: err.message,
      url: req.url 
    });
  }
}));

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 所有其他请求返回React应用
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'build', 'index.html'));
});

app.listen(PORT, () => {
  console.log(`🚀 后端服务器运行在 http://localhost:${PORT}`);
  console.log(`📡 代理API请求到 https://api.cursor.com`);
});
